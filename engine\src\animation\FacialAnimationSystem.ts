/**
 * 面部动画系统
 * 用于管理和更新面部动画组件
 */
import * as THREE from 'three';
import { Entity } from '../core/Entity';
import { System } from '../core/System';
import { World } from '../core/World';
import { FacialAnimationComponent, FacialExpressionType, VisemeType, FacialAnimationConfig } from './FacialAnimation';
import { EventEmitter } from '../utils/EventEmitter';
import { FacialAnimationModelAdapterComponent } from './adapters/FacialAnimationModelAdapter';
import { FacialAnimationModelAdapterSystem } from './adapters/FacialAnimationModelAdapterSystem';

/**
 * 音频分析配置
 */
interface AudioAnalysisConfig {
  /** FFT大小 */
  fftSize: number;
  /** 采样频率 */
  sampleRate: number;
  /** 频率边界（男性） */
  boundingFrequencyMale: number[];
  /** 频率边界（女性） */
  boundingFrequencyFemale: number[];
  /** 灵敏度阈值 */
  sensitivityThreshold: number;
  /** 音量阈值 */
  volumeThreshold: number;
}

/**
 * 面部动画系统
 */
export class FacialAnimationSystem extends System {
  /** 系统类型 */
  static readonly type = 'FacialAnimation';

  /** 面部动画组件 */
  private components: Map<Entity, FacialAnimationComponent> = new Map();

  /** 配置 */
  private config: FacialAnimationConfig;

  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: FacialAnimationConfig = {
    debug: false,
    autoDetectAudio: true,
    useWebcam: false,
    useAIPrediction: false,
    blendSpeed: 5.0,
    smoothingFactor: 0.5
  };

  /** 音频分析配置 */
  private audioConfig: AudioAnalysisConfig = {
    fftSize: 1024,
    sampleRate: 44100,
    boundingFrequencyMale: [0, 400, 560, 2400, 4800],
    boundingFrequencyFemale: [0, 500, 700, 3000, 6000],
    sensitivityThreshold: 0.5,
    volumeThreshold: 0.01
  };

  /** 音频上下文 */
  private audioContext: AudioContext | null = null;
  /** 音频分析器 */
  private audioAnalyser: AnalyserNode | null = null;
  /** 频谱数据 */
  private spectrum: Float32Array | null = null;
  /** 是否正在跟踪口型 */
  private lipsyncTracking: boolean = false;

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** 模型适配器系统 */
  private modelAdapterSystem: FacialAnimationModelAdapterSystem | null = null;

  /**
   * 构造函数
   * @param world 世界
   * @param config 配置
   */
  constructor(world: World, config?: Partial<FacialAnimationConfig>) {
    super(world);
    this.config = { ...FacialAnimationSystem.DEFAULT_CONFIG, ...config };
  }

  /**
   * 设置模型适配器系统
   * @param system 模型适配器系统
   */
  public setModelAdapterSystem(system: FacialAnimationModelAdapterSystem): void {
    this.modelAdapterSystem = system;

    if (this.config.debug) {
      console.log('已设置模型适配器系统');
    }
  }

  /**
   * 获取模型适配器系统
   * @returns 模型适配器系统
   */
  public getModelAdapterSystem(): FacialAnimationModelAdapterSystem | null {
    return this.modelAdapterSystem;
  }

  /**
   * 创建面部动画组件
   * @param entity 实体
   * @returns 面部动画组件
   */
  public createFacialAnimation(entity: Entity): FacialAnimationComponent {
    // 检查是否已存在组件
    if (this.components.has(entity)) {
      return this.components.get(entity)!;
    }

    // 创建新组件
    const component = new FacialAnimationComponent(entity);
    this.components.set(entity, component);

    if (this.config.debug) {
      console.log(`创建面部动画组件: ${entity.id}`);
    }

    return component;
  }

  /**
   * 移除面部动画组件
   * @param entity 实体
   */
  public removeFacialAnimation(entity: Entity): void {
    if (this.components.has(entity)) {
      this.components.delete(entity);

      if (this.config.debug) {
        console.log(`移除面部动画组件: ${entity.id}`);
      }
    }
  }

  /**
   * 获取面部动画组件
   * @param entity 实体
   * @returns 面部动画组件，如果不存在则返回null
   */
  public getFacialAnimation(entity: Entity): FacialAnimationComponent | null {
    return this.components.get(entity) || null;
  }

  /**
   * 启动口型同步跟踪
   */
  public startLipsyncTracking(): void {
    if (this.lipsyncTracking) return;

    this.lipsyncTracking = true;

    // 创建音频上下文
    this.audioContext = new AudioContext();
    const fftSize = this.audioConfig.fftSize;

    // 计算频率索引
    const indicesFrequencyMale: number[] = [];
    const indicesFrequencyFemale: number[] = [];

    for (let i = 0; i < this.audioConfig.boundingFrequencyMale.length; i++) {
      indicesFrequencyMale[i] = Math.round(((2 * fftSize) / this.audioConfig.sampleRate) * this.audioConfig.boundingFrequencyMale[i]);
      indicesFrequencyFemale[i] = Math.round(((2 * fftSize) / this.audioConfig.sampleRate) * this.audioConfig.boundingFrequencyFemale[i]);
    }

    // 创建音频处理器
    const audioProcessor = this.audioContext.createScriptProcessor(fftSize, 1, 1);
    const userSpeechAnalyzer = this.audioContext.createAnalyser();
    userSpeechAnalyzer.fftSize = fftSize;
    this.audioAnalyser = userSpeechAnalyzer;

    // 连接音频节点
    audioProcessor.connect(this.audioContext.destination);
    userSpeechAnalyzer.connect(audioProcessor);

    // 处理音频数据
    audioProcessor.onaudioprocess = () => {
      if (!this.lipsyncTracking) return;

      // 获取频率数据
      this.spectrum = new Float32Array(userSpeechAnalyzer.frequencyBinCount);
      userSpeechAnalyzer.getFloatFrequencyData(this.spectrum);

      // 计算RMS
      const rms = this.getRMS(this.spectrum);

      // 如果音量太小，则设置为静默
      if (rms < this.audioConfig.volumeThreshold) {
        this.setVisemeForAllEntities(VisemeType.SILENT);
        return;
      }

      // 计算灵敏度映射
      const sensitivityMap = this.getSensitivityMap(this.spectrum);

      // 计算各频段能量
      const energy: number[] = [];
      for (let i = 0; i < indicesFrequencyMale.length - 1; i++) {
        let male_sum = 0;
        for (let j = indicesFrequencyMale[i]; j < indicesFrequencyMale[i + 1]; j++) {
          male_sum += this.spectrum[j];
        }
        energy[i] = male_sum / (indicesFrequencyMale[i + 1] - indicesFrequencyMale[i]);
      }

      // 根据能量确定口型
      const pucker = Math.max(0, energy[0]); // 低频
      const widen = Math.max(0, energy[1]);  // 中低频
      const open = Math.max(0, energy[2]);   // 中高频

      // 设置口型
      if (pucker > widen && pucker > open) {
        this.setVisemeForAllEntities(VisemeType.PP);
      } else if (widen > pucker && widen > open) {
        this.setVisemeForAllEntities(VisemeType.FF);
      } else if (open > pucker && open > widen) {
        this.setVisemeForAllEntities(VisemeType.AA);
      } else {
        this.setVisemeForAllEntities(VisemeType.SILENT);
      }
    };

    if (this.config.debug) {
      console.log('口型同步跟踪已启动');
    }
  }

  /**
   * 停止口型同步跟踪
   */
  public stopLipsyncTracking(): void {
    if (!this.lipsyncTracking) return;

    this.lipsyncTracking = false;

    // 关闭音频上下文
    if (this.audioContext) {
      this.audioContext.close();
      this.audioContext = null;
    }

    this.audioAnalyser = null;
    this.spectrum = null;

    // 重置所有实体的口型
    for (const component of this.components.values()) {
      component.resetViseme();
    }

    if (this.config.debug) {
      console.log('口型同步跟踪已停止');
    }
  }

  /**
   * 为所有实体设置口型
   * @param viseme 口型类型
   * @param weight 权重
   */
  private setVisemeForAllEntities(viseme: VisemeType, weight: number = 1.0): void {
    for (const component of this.components.values()) {
      component.setViseme(viseme, weight);
    }
  }

  /**
   * 计算RMS（均方根）
   * @param spectrum 频谱数据
   * @returns RMS值
   */
  private getRMS(spectrum: Float32Array): number {
    let rms = 0;
    for (let i = 0; i < spectrum.length; i++) {
      rms += spectrum[i] * spectrum[i];
    }
    rms /= spectrum.length;
    rms = Math.sqrt(rms);
    return rms;
  }

  /**
   * 获取灵敏度映射
   * @param spectrum 频谱数据
   * @returns 灵敏度映射
   */
  private getSensitivityMap(spectrum: Float32Array): Float32Array {
    const stPSD = new Float32Array(spectrum.length);
    for (let i = 0; i < spectrum.length; i++) {
      stPSD[i] = this.audioConfig.sensitivityThreshold + (spectrum[i] + 20) / 140;
    }
    return stPSD;
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    // 更新所有面部动画组件
    for (const [entity, component] of this.components.entries()) {
      // 更新组件
      component.update(deltaTime);

      // 如果有模型适配器系统，应用表情和口型到模型
      if (this.modelAdapterSystem) {
        const adapter = this.modelAdapterSystem.getModelAdapter(entity);
        if (adapter) {
          // 获取当前表情
          const expressionType = component.getCurrentExpression().expression;
          const expressionWeight = component.getCurrentExpression().weight;

          // 获取当前口型
          const visemeType = component.getCurrentViseme().viseme;
          const visemeWeight = component.getCurrentViseme().weight;

          // 应用表情和口型
          adapter.applyExpression(expressionType, expressionWeight);
          adapter.applyViseme(visemeType, visemeWeight);
        }
      }
    }
  }

  /**
   * 将面部动画组件与模型适配器关联
   * @param entity 实体
   * @param mesh 骨骼网格
   * @returns 是否成功关联
   */
  public linkToModel(entity: Entity, mesh: THREE.SkinnedMesh): boolean {
    // 检查是否有面部动画组件
    const component = this.getFacialAnimation(entity);
    if (!component) {
      if (this.config.debug) {
        console.warn(`无法关联模型: 实体 ${entity.id} 没有面部动画组件`);
      }
      return false;
    }

    // 检查是否有模型适配器系统
    if (!this.modelAdapterSystem) {
      if (this.config.debug) {
        console.warn('无法关联模型: 未设置模型适配器系统');
      }
      return false;
    }

    // 获取或创建模型适配器
    let adapter = this.modelAdapterSystem.getModelAdapter(entity);
    if (!adapter) {
      adapter = this.modelAdapterSystem.createModelAdapter(entity);
    }

    // 设置骨骼网格
    adapter.setSkinnedMesh(mesh);

    if (this.config.debug) {
      console.log(`已将实体 ${entity.id} 的面部动画组件与模型关联`);
    }

    return true;
  }
}
