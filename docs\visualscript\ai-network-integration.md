# AI和网络节点集成指南

本文档介绍了如何在IR引擎视觉脚本系统中集成AI节点和网络节点，创建智能网络应用。

## 目录

- [概述](#概述)
- [AI节点](#ai节点)
  - [AI模型节点](#ai模型节点)
  - [情感分析节点](#情感分析节点)
  - [自然语言处理节点](#自然语言处理节点)
- [网络节点](#网络节点)
  - [网络协议节点](#网络协议节点)
  - [WebRTC节点](#webrtc节点)
  - [网络安全节点](#网络安全节点)
- [集成示例](#集成示例)
  - [AI驱动的聊天机器人](#ai驱动的聊天机器人)
  - [多用户情感共享](#多用户情感共享)
  - [智能网络角色](#智能网络角色)
- [最佳实践](#最佳实践)
- [常见问题](#常见问题)

## 概述

AI节点和网络节点的集成为创建智能网络应用提供了强大的工具。通过组合这些节点，可以实现以下功能：

- 基于AI的聊天机器人，能够理解用户情感并做出相应反应
- 多用户共享的AI驱动角色，能够与多个用户交互
- 基于网络的AI情感分析和响应系统
- 安全的AI数据传输和处理

## AI节点

### AI模型节点

AI模型节点提供了加载和使用AI模型的功能。

#### 加载AI模型节点

**节点类型**: `ai/model/load`

**输入插槽**:
- `flow` (Flow): 输入流程
- `modelType` (string): 模型类型，如"gpt-3.5-turbo"
- `config` (object): 模型配置

**输出插槽**:
- `success` (Flow): 加载成功
- `fail` (Flow): 加载失败
- `model` (object): 加载的模型

**使用示例**:
```json
{
  "id": "loadModel",
  "type": "ai/model/load",
  "parameters": {
    "modelType": {
      "value": "gpt-3.5-turbo"
    },
    "config": {
      "value": {
        "temperature": 0.7,
        "maxTokens": 100
      }
    }
  }
}
```

#### 文本生成节点

**节点类型**: `ai/model/generateText`

**输入插槽**:
- `flow` (Flow): 输入流程
- `model` (object): AI模型
- `prompt` (string): 提示文本
- `maxTokens` (number): 最大令牌数
- `temperature` (number): 温度

**输出插槽**:
- `success` (Flow): 生成成功
- `fail` (Flow): 生成失败
- `text` (string): 生成的文本

**使用示例**:
```json
{
  "id": "generateText",
  "type": "ai/model/generateText",
  "parameters": {
    "model": {
      "reference": {
        "nodeId": "loadModel",
        "socket": "model"
      }
    },
    "prompt": {
      "value": "你好，请生成一段文本。"
    },
    "maxTokens": {
      "value": 100
    },
    "temperature": {
      "value": 0.7
    }
  }
}
```

### 情感分析节点

情感分析节点提供了分析文本情感和驱动角色动画的功能。

#### 情感分析节点

**节点类型**: `ai/emotion/analyze`

**输入插槽**:
- `flow` (Flow): 输入流程
- `text` (string): 要分析的文本
- `detailed` (boolean): 是否返回详细结果

**输出插槽**:
- `success` (Flow): 分析成功
- `fail` (Flow): 分析失败
- `emotion` (string): 主要情感
- `intensity` (number): 情感强度
- `detailedResult` (object): 详细分析结果

**使用示例**:
```json
{
  "id": "analyzeEmotion",
  "type": "ai/emotion/analyze",
  "parameters": {
    "text": {
      "value": "我今天非常开心！"
    },
    "detailed": {
      "value": true
    }
  }
}
```

#### 情感驱动动画节点

**节点类型**: `ai/emotion/driveAnimation`

**输入插槽**:
- `flow` (Flow): 输入流程
- `entity` (Entity): 目标实体
- `emotion` (string): 情感类型
- `intensity` (number): 情感强度
- `duration` (number): 持续时间（秒）

**输出插槽**:
- `flow` (Flow): 输出流程
- `success` (boolean): 是否成功

**使用示例**:
```json
{
  "id": "driveAnimation",
  "type": "ai/emotion/driveAnimation",
  "parameters": {
    "entity": {
      "value": "角色"
    },
    "emotion": {
      "reference": {
        "nodeId": "analyzeEmotion",
        "socket": "emotion"
      }
    },
    "intensity": {
      "reference": {
        "nodeId": "analyzeEmotion",
        "socket": "intensity"
      }
    },
    "duration": {
      "value": 3.0
    }
  }
}
```

### 自然语言处理节点

自然语言处理节点提供了文本分类、命名实体识别和文本摘要等功能。

#### 文本分类节点

**节点类型**: `ai/nlp/classifyText`

**输入插槽**:
- `flow` (Flow): 输入流程
- `text` (string): 要分类的文本
- `categories` (array): 可选的分类类别

**输出插槽**:
- `success` (Flow): 分类成功
- `fail` (Flow): 分类失败
- `category` (string): 分类结果
- `confidence` (number): 置信度
- `allCategories` (object): 所有分类结果及置信度

## 网络节点

### 网络协议节点

网络协议节点提供了不同网络协议的支持，如UDP、TCP、HTTP等。

#### UDP发送节点

**节点类型**: `network/protocol/udpSend`

**输入插槽**:
- `flow` (Flow): 输入流程
- `address` (string): 目标地址
- `port` (number): 目标端口
- `data` (any): 要发送的数据

**输出插槽**:
- `success` (Flow): 发送成功
- `fail` (Flow): 发送失败

#### HTTP请求节点

**节点类型**: `network/protocol/httpRequest`

**输入插槽**:
- `flow` (Flow): 输入流程
- `url` (string): 请求URL
- `method` (string): 请求方法
- `headers` (object): 请求头
- `body` (any): 请求体
- `timeout` (number): 超时时间（毫秒）

**输出插槽**:
- `success` (Flow): 请求成功
- `fail` (Flow): 请求失败
- `response` (object): 响应数据
- `statusCode` (number): 状态码
- `headers` (object): 响应头

### WebRTC节点

WebRTC节点提供了WebRTC相关功能，如媒体流控制、数据通道管理等。

#### 创建WebRTC连接节点

**节点类型**: `network/webrtc/createConnection`

**输入插槽**:
- `flow` (Flow): 输入流程
- `peerId` (string): 对等方ID
- `config` (object): WebRTC配置

**输出插槽**:
- `success` (Flow): 创建成功
- `fail` (Flow): 创建失败
- `connection` (object): WebRTC连接

### 网络安全节点

网络安全节点提供了数据加密/解密、用户认证、权限验证等功能。

#### 数据加密节点

**节点类型**: `network/security/encryptData`

**输入插槽**:
- `flow` (Flow): 输入流程
- `data` (any): 要加密的数据
- `algorithm` (string): 加密算法
- `key` (string): 加密密钥

**输出插槽**:
- `flow` (Flow): 输出流程
- `encryptedData` (string): 加密后的数据

## 集成示例

### AI驱动的聊天机器人

以下示例展示了如何创建一个AI驱动的聊天机器人，能够理解用户情感并做出相应反应：

1. 使用`ai/model/load`节点加载AI模型
2. 使用DOM事件监听用户输入
3. 使用`ai/emotion/analyze`节点分析用户输入的情感
4. 使用`ai/model/generateText`节点生成回复
5. 使用`ai/emotion/driveAnimation`节点驱动角色动画

详细示例请参考`examples/visualscript/AINetworkExample.ts`中的`createChatbotScript`方法。

### 多用户情感共享

以下示例展示了如何创建一个多用户情感共享系统：

1. 使用`network/connectToServer`节点连接到服务器
2. 使用`ai/emotion/analyze`节点分析用户输入的情感
3. 使用`network/sendMessage`节点将情感数据发送给其他用户
4. 使用`network/events/onMessage`节点接收其他用户的情感数据
5. 使用`ai/emotion/driveAnimation`节点驱动角色动画

详细示例请参考`examples/visualscript/AINetworkExample.ts`中的`createNetworkManagerScript`方法。

## 最佳实践

- **模型加载优化**：AI模型加载可能需要一些时间，建议在应用启动时预加载常用模型。
- **错误处理**：始终处理AI和网络操作可能出现的错误，提供适当的回退机制。
- **安全性**：使用网络安全节点保护敏感数据，特别是在传输AI生成的内容时。
- **性能优化**：对于复杂的AI操作，考虑使用异步处理和缓存机制。
- **用户体验**：提供适当的加载指示器和错误提示，让用户了解AI和网络操作的状态。

## 常见问题

**问题**：AI模型加载失败怎么办？
**回答**：检查网络连接和API密钥，考虑使用本地模型作为备选。

**问题**：如何优化网络传输中的AI数据？
**回答**：考虑压缩数据，只传输必要的信息，使用增量更新而不是完整状态。

**问题**：如何处理多用户环境中的AI资源竞争？
**回答**：实现请求队列和优先级系统，限制单个用户的请求频率。
