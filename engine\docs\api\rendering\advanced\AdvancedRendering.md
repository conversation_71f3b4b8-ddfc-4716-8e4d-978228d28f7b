# 高级渲染功能

IR引擎提供了多种高级渲染功能，用于创建逼真的视觉效果。本文档介绍了这些功能的使用方法和最佳实践。

## 目录

1. [PBR材质系统](#pbr材质系统)
2. [阴影系统](#阴影系统)
3. [后处理效果](#后处理效果)
4. [环境光照](#环境光照)
5. [体积光和体积雾](#体积光和体积雾)
6. [屏幕空间效果](#屏幕空间效果)
7. [渲染优化](#渲染优化)

## PBR材质系统

物理基础渲染（PBR）材质系统使用基于物理的方法模拟真实世界的材质外观。

### 基本用法

```typescript
import { MaterialSystem } from '../../rendering/materials/MaterialSystem';
import { PBRMaterial } from '../../rendering/materials/PBRMaterial';

// 创建材质系统
const materialSystem = new MaterialSystem();

// 创建PBR材质
const material = new PBRMaterial({
  name: '金属材质',
  baseColor: { r: 0.8, g: 0.8, b: 0.8 },
  metalness: 1.0,
  roughness: 0.2,
  normalMap: 'textures/metal_normal.jpg',
  metalnessMap: 'textures/metal_metalness.jpg',
  roughnessMap: 'textures/metal_roughness.jpg'
});

// 注册材质
materialSystem.registerMaterial(material);

// 获取材质
const retrievedMaterial = materialSystem.getMaterial('金属材质');
```

### 材质属性

PBR材质支持以下属性：

| 属性名 | 类型 | 描述 |
|--------|------|------|
| baseColor | Color | 基础颜色 |
| metalness | number | 金属度（0-1） |
| roughness | number | 粗糙度（0-1） |
| normalMap | string \| Texture | 法线贴图 |
| metalnessMap | string \| Texture | 金属度贴图 |
| roughnessMap | string \| Texture | 粗糙度贴图 |
| aoMap | string \| Texture | 环境光遮蔽贴图 |
| emissive | Color | 自发光颜色 |
| emissiveIntensity | number | 自发光强度 |
| emissiveMap | string \| Texture | 自发光贴图 |
| clearcoat | number | 清漆层强度（0-1） |
| clearcoatRoughness | number | 清漆层粗糙度（0-1） |
| transmission | number | 透射率（0-1） |
| ior | number | 折射率 |
| thickness | number | 厚度 |

### 材质优化

材质系统提供了自动优化功能，可以根据设备性能自动降级材质：

```typescript
// 启用材质自动优化
materialSystem.enableAutoOptimization(true);

// 设置优化级别
materialSystem.setOptimizationLevel(OptimizationLevel.MEDIUM);

// 手动优化材质
const optimizedMaterial = materialSystem.optimizeMaterial(material, OptimizationLevel.LOW);
```

## 阴影系统

阴影系统用于生成逼真的阴影效果，支持多种阴影类型和优化技术。

### 基本用法

```typescript
import { ShadowSystem } from '../../rendering/shadows/ShadowSystem';
import { ShadowType } from '../../rendering/shadows/ShadowType';

// 创建阴影系统
const shadowSystem = new ShadowSystem({
  enabled: true,
  type: ShadowType.PCF_SOFT,
  mapSize: 2048,
  bias: 0.0001,
  normalBias: 0.01
});

// 为光源启用阴影
light.castShadow = true;
light.shadow = shadowSystem.createShadowForLight(light);

// 为对象启用阴影投射和接收
object.castShadow = true;
object.receiveShadow = true;
```

### 阴影类型

阴影系统支持以下阴影类型：

| 类型 | 描述 |
|------|------|
| BASIC | 基本阴影映射 |
| PCF | 百分比接近滤波阴影 |
| PCF_SOFT | 软百分比接近滤波阴影 |
| VSM | 方差阴影映射 |
| CSM | 级联阴影映射 |

### 级联阴影映射

级联阴影映射（CSM）用于大型场景，提供高质量的远距离阴影：

```typescript
import { CSMShadowSystem } from '../../rendering/shadows/CSMShadowSystem';

// 创建CSM阴影系统
const csmShadowSystem = new CSMShadowSystem({
  enabled: true,
  cascades: 4,
  maxDistance: 100,
  shadowMapSize: 2048,
  lightDirection: { x: 1, y: -1, z: 1 },
  lightIntensity: 1.0,
  lightColor: { r: 1, g: 1, b: 1 }
});

// 更新CSM阴影系统
csmShadowSystem.update(camera);
```

## 后处理效果

后处理效果用于在场景渲染完成后应用各种视觉效果。详细信息请参阅[后处理系统文档](../postprocessing/PostProcessingSystem.md)。

### 常用效果组合

以下是一些常用的效果组合：

#### 电影效果

```typescript
// 创建后处理系统
const postProcessingSystem = new PostProcessingSystem({
  enabled: true,
  autoResize: true
});

// 添加电影效果
postProcessingSystem.addEffect(new VignetteEffect({ intensity: 0.5 }));
postProcessingSystem.addEffect(new FilmGrainEffect({ intensity: 0.3 }));
postProcessingSystem.addEffect(new ChromaticAberrationEffect({ distortion: 0.5 }));
postProcessingSystem.addEffect(new ToneMappingEffect({ type: ToneMappingType.CINEON }));
```

#### 逼真效果

```typescript
// 创建后处理系统
const postProcessingSystem = new PostProcessingSystem({
  enabled: true,
  autoResize: true
});

// 添加逼真效果
postProcessingSystem.addEffect(new SSAOEffect({ intensity: 1.0 }));
postProcessingSystem.addEffect(new SSREffect({ intensity: 0.5 }));
postProcessingSystem.addEffect(new DepthOfFieldEffect({ focusDistance: 5, focalLength: 50, bokehScale: 2 }));
postProcessingSystem.addEffect(new ToneMappingEffect({ type: ToneMappingType.ACES_FILMIC }));
```

## 环境光照

环境光照用于模拟环境对场景的间接照明，增强场景的真实感。

### 环境贴图

```typescript
import { EnvironmentSystem } from '../../rendering/environment/EnvironmentSystem';

// 创建环境系统
const environmentSystem = new EnvironmentSystem();

// 加载HDR环境贴图
environmentSystem.loadHDREnvironment('textures/environment.hdr', (envMap) => {
  // 设置环境贴图
  scene.environment = envMap;
  
  // 设置背景
  scene.background = envMap;
  
  // 生成环境光照
  const ambientLight = environmentSystem.createAmbientLightFromEnvironment(envMap, 1.0);
  scene.add(ambientLight);
});
```

### 光照探针

```typescript
import { LightProbeSystem } from '../../rendering/environment/LightProbeSystem';

// 创建光照探针系统
const lightProbeSystem = new LightProbeSystem();

// 创建光照探针
const lightProbe = lightProbeSystem.createLightProbe();

// 从环境贴图生成光照探针
lightProbeSystem.generateFromEnvironment(lightProbe, envMap);

// 添加光照探针到场景
scene.add(lightProbe);
```

## 体积光和体积雾

体积光和体积雾用于模拟光线在空气中的散射效果，增强场景的氛围感。

### 体积光

```typescript
import { VolumetricLightSystem } from '../../rendering/volumetric/VolumetricLightSystem';

// 创建体积光系统
const volumetricLightSystem = new VolumetricLightSystem({
  enabled: true,
  samples: 50,
  density: 0.1,
  weight: 0.5,
  decay: 0.95,
  exposure: 0.2
});

// 为光源添加体积光效果
volumetricLightSystem.addLight(light);

// 更新体积光系统
volumetricLightSystem.update(camera);
```

### 体积雾

```typescript
import { VolumetricFogSystem } from '../../rendering/volumetric/VolumetricFogSystem';

// 创建体积雾系统
const volumetricFogSystem = new VolumetricFogSystem({
  enabled: true,
  color: { r: 0.5, g: 0.5, b: 0.5 },
  density: 0.05,
  noiseScale: 0.1,
  noiseSpeed: 0.01,
  heightFalloff: 0.1
});

// 更新体积雾系统
volumetricFogSystem.update(camera, deltaTime);
```

## 屏幕空间效果

屏幕空间效果使用屏幕空间信息计算各种效果，如反射、环境光遮蔽和全局光照。

### 屏幕空间反射 (SSR)

```typescript
import { SSREffect } from '../../rendering/postprocessing/effects/SSREffect';

// 创建SSR效果
const ssrEffect = new SSREffect({
  name: 'SSR',
  enabled: true,
  intensity: 0.5,
  maxSteps: 20,
  stride: 1,
  refineSteps: 5,
  thickness: 0.5,
  maxDistance: 50
});

// 添加到后处理系统
postProcessingSystem.addEffect(ssrEffect);
```

### 屏幕空间环境光遮蔽 (SSAO)

```typescript
import { SSAOEffect } from '../../rendering/postprocessing/effects/SSAOEffect';

// 创建SSAO效果
const ssaoEffect = new SSAOEffect({
  name: 'SSAO',
  enabled: true,
  radius: 0.1825,
  bias: 0.025,
  intensity: 1.0,
  samples: 16,
  kernelRadius: 16
});

// 添加到后处理系统
postProcessingSystem.addEffect(ssaoEffect);
```

### 屏幕空间全局光照 (SSGI)

```typescript
import { SSGIEffect } from '../../rendering/postprocessing/effects/SSGIEffect';

// 创建SSGI效果
const ssgiEffect = new SSGIEffect({
  name: 'SSGI',
  enabled: true,
  intensity: 1.0,
  radius: 2.0,
  samples: 16,
  denoise: true,
  denoiseRadius: 12
});

// 添加到后处理系统
postProcessingSystem.addEffect(ssgiEffect);
```

## 渲染优化

渲染优化技术用于提高渲染性能，特别是在处理大型场景或低性能设备时。

### LOD系统

```typescript
import { LODSystem } from '../../rendering/optimization/LODSystem';

// 创建LOD系统
const lodSystem = new LODSystem({
  enabled: true,
  autoUpdate: true,
  updateInterval: 0.5
});

// 创建LOD对象
const lodObject = lodSystem.createLODObject();

// 添加LOD级别
lodObject.addLevel(highDetailMesh, 0);    // 近距离使用高细节模型
lodObject.addLevel(mediumDetailMesh, 10); // 中等距离使用中等细节模型
lodObject.addLevel(lowDetailMesh, 50);    // 远距离使用低细节模型

// 自动生成LOD级别
lodSystem.generateLODLevels(highDetailMesh, [
  { distance: 10, reduction: 0.5 },
  { distance: 50, reduction: 0.2 }
]);
```

### 视锥体剔除

```typescript
import { FrustumCullingSystem } from '../../rendering/optimization/FrustumCullingSystem';

// 创建视锥体剔除系统
const frustumCullingSystem = new FrustumCullingSystem({
  enabled: true,
  autoUpdate: true,
  updateInterval: 0.1,
  useBVH: true
});

// 添加可剔除对象
frustumCullingSystem.addCullableObject(object);

// 更新视锥体剔除系统
frustumCullingSystem.update(camera);
```

### 实例化渲染

```typescript
import { InstancedRenderingSystem } from '../../rendering/optimization/InstancedRenderingSystem';

// 创建实例化渲染系统
const instancedRenderingSystem = new InstancedRenderingSystem({
  enabled: true,
  maxInstancesPerBatch: 1000,
  useGPUInstancing: true
});

// 创建实例化对象
const instancedObject = instancedRenderingSystem.createInstancedObject(geometry, material);

// 添加实例
for (let i = 0; i < 1000; i++) {
  const position = { x: Math.random() * 100 - 50, y: 0, z: Math.random() * 100 - 50 };
  const rotation = { x: 0, y: Math.random() * Math.PI * 2, z: 0 };
  const scale = { x: 1, y: 1, z: 1 };
  
  instancedObject.addInstance(position, rotation, scale);
}

// 更新实例化对象
instancedObject.updateInstances();
```

## 最佳实践

1. **性能与质量平衡**：根据目标平台调整渲染质量
2. **资源管理**：使用纹理压缩和模型优化减少内存使用
3. **渐进式增强**：先实现基本功能，再添加高级效果
4. **性能监控**：使用性能监控工具识别瓶颈
5. **移动设备优化**：为移动设备提供特定的渲染路径和优化
