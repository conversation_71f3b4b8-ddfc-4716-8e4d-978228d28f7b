/**
 * 网络实体组件
 * 用于标识和管理网络同步的实体
 */
import { Component } from '../../core/Component';
import { Entity } from '../../core/Entity';
import { TransformComponent } from '../../core/TransformComponent';
import { NetworkEntityOwnershipMode, NetworkEntitySyncMode, NetworkEntityType } from '../NetworkEntity';

/**
 * 网络实体组件属性
 */
export interface NetworkEntityComponentProps {
  /** 实体ID */
  entityId: string;

  /** 实体类型 */
  type?: NetworkEntityType;

  /** 实体所有者ID */
  ownerId: string;

  /** 实体同步模式 */
  syncMode?: NetworkEntitySyncMode;

  /** 实体所有权模式 */
  ownershipMode?: NetworkEntityOwnershipMode;

  /** 同步间隔（毫秒） */
  syncInterval?: number;

  /** 是否自动同步 */
  autoSync?: boolean;

  /** 是否是本地拥有 */
  isLocallyOwned?: boolean;

  /** 是否可转移所有权 */
  canTransferOwnership?: boolean;

  /** 同步优先级 */
  syncPriority?: number;

  /** 同步距离 */
  syncDistance?: number;

  /** 同步组 */
  syncGroup?: string;

  /** 同步标签 */
  syncTags?: string[];

  /** 自定义数据 */
  customData?: Record<string, any>;
}

/**
 * 网络实体组件
 * 用于标识和管理网络同步的实体
 */
export class NetworkEntityComponent extends Component {
  /** 组件类型 */
  public static readonly type = 'NetworkEntity';

  /** 实体ID */
  public entityId: string;

  /** 实体类型 */
  public type: NetworkEntityType;

  /** 实体所有者ID */
  public ownerId: string;

  /** 实体同步模式 */
  public syncMode: NetworkEntitySyncMode;

  /** 实体所有权模式 */
  public ownershipMode: NetworkEntityOwnershipMode;

  /** 同步间隔（毫秒） */
  public syncInterval: number;

  /** 是否自动同步 */
  public autoSync: boolean;

  /** 是否是本地拥有 */
  public isLocallyOwned: boolean;

  /** 是否可转移所有权 */
  public canTransferOwnership: boolean;

  /** 同步优先级 */
  public syncPriority: number;

  /** 同步距离 */
  public syncDistance: number;

  /** 同步组 */
  public syncGroup: string;

  /** 同步标签 */
  public syncTags: string[];

  /** 自定义数据 */
  public customData: Record<string, any>;

  /** 上次同步时间 */
  private lastSyncTime: number = 0;

  /** 是否有待同步的更改 */
  private hasPendingChanges: boolean = false;

  /** 待同步的属性 */
  private pendingProperties: Set<string> = new Set();

  /**
   * 创建网络实体组件
   * @param props 组件属性
   */
  constructor(props: NetworkEntityComponentProps) {
    super(NetworkEntityComponent.type);

    this.entityId = props.entityId;
    this.type = props.type || NetworkEntityType.DYNAMIC;
    this.ownerId = props.ownerId;
    this.syncMode = props.syncMode || NetworkEntitySyncMode.TRANSFORM;
    this.ownershipMode = props.ownershipMode || NetworkEntityOwnershipMode.FIXED;
    this.syncInterval = props.syncInterval || 100;
    this.autoSync = props.autoSync !== undefined ? props.autoSync : true;
    this.isLocallyOwned = props.isLocallyOwned || false;
    this.canTransferOwnership = props.canTransferOwnership !== undefined ? props.canTransferOwnership : true;
    this.syncPriority = props.syncPriority || 0;
    this.syncDistance = props.syncDistance || 0;
    this.syncGroup = props.syncGroup || 'default';
    this.syncTags = props.syncTags || [];
    this.customData = props.customData || {};
  }

  /**
   * 初始化组件
   */
  public initialize(): void {
    // 初始化逻辑
  }

  /**
   * 更新组件
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    // 如果启用了自动同步，则检查是否需要同步
    if (this.autoSync && this.isLocallyOwned && this.hasPendingChanges) {
      const now = Date.now();

      // 检查是否达到同步间隔
      if (now - this.lastSyncTime >= this.syncInterval) {
        this.sync();
      }
    }
  }

  /**
   * 同步实体
   */
  public sync(): void {
    if (!this.hasPendingChanges) {
      return;
    }

    // 获取需要同步的数据
    const syncData = this.getSyncData();

    // 触发同步事件
    const entity = this.getEntity();
    if (entity && (entity as any).emit) {
      (entity as any).emit('networkSync', {
        entityId: this.entityId,
        ownerId: this.ownerId,
        data: syncData,
      });
    }

    // 更新同步时间
    this.lastSyncTime = Date.now();

    // 清除待同步标记
    this.hasPendingChanges = false;
    this.pendingProperties.clear();
  }

  /**
   * 获取同步数据
   * @returns 同步数据
   */
  public getSyncData(): any {
    const data: any = {
      entityId: this.entityId,
      type: this.type,
    };

    // 根据同步模式获取不同的数据
    switch (this.syncMode) {
      case NetworkEntitySyncMode.TRANSFORM:
        // 同步变换数据
        const transform = this.getEntity()?.getComponent('Transform') as TransformComponent;
        if (transform) {
          data.transform = {
            position: {
              x: transform.position.x,
              y: transform.position.y,
              z: transform.position.z,
            },
            rotation: {
              x: transform.quaternion.x,
              y: transform.quaternion.y,
              z: transform.quaternion.z,
              w: transform.quaternion.w,
            },
            scale: {
              x: transform.scale.x,
              y: transform.scale.y,
              z: transform.scale.z,
            },
          };
        }
        break;

      case NetworkEntitySyncMode.PHYSICS:
        // 同步物理数据
        const physics = this.entity.getComponent('PhysicsBody');
        if (physics) {
          data.physics = {
            position: {
              x: physics.position.x,
              y: physics.position.y,
              z: physics.position.z,
            },
            rotation: {
              x: physics.rotation.x,
              y: physics.rotation.y,
              z: physics.rotation.z,
              w: physics.rotation.w,
            },
            velocity: {
              x: physics.velocity.x,
              y: physics.velocity.y,
              z: physics.velocity.z,
            },
            angularVelocity: {
              x: physics.angularVelocity.x,
              y: physics.angularVelocity.y,
              z: physics.angularVelocity.z,
            },
          };
        }
        break;

      case NetworkEntitySyncMode.FULL:
        // 同步所有数据
        // 变换数据
        const fullTransform = this.entity.getComponent('Transform');
        if (fullTransform) {
          data.transform = {
            position: {
              x: fullTransform.position.x,
              y: fullTransform.position.y,
              z: fullTransform.position.z,
            },
            rotation: {
              x: fullTransform.rotation.x,
              y: fullTransform.rotation.y,
              z: fullTransform.rotation.z,
              w: fullTransform.rotation.w,
            },
            scale: {
              x: fullTransform.scale.x,
              y: fullTransform.scale.y,
              z: fullTransform.scale.z,
            },
          };
        }

        // 物理数据
        const fullPhysics = this.entity.getComponent('PhysicsBody');
        if (fullPhysics) {
          data.physics = {
            position: {
              x: fullPhysics.position.x,
              y: fullPhysics.position.y,
              z: fullPhysics.position.z,
            },
            rotation: {
              x: fullPhysics.rotation.x,
              y: fullPhysics.rotation.y,
              z: fullPhysics.rotation.z,
              w: fullPhysics.rotation.w,
            },
            velocity: {
              x: fullPhysics.velocity.x,
              y: fullPhysics.velocity.y,
              z: fullPhysics.velocity.z,
            },
            angularVelocity: {
              x: fullPhysics.angularVelocity.x,
              y: fullPhysics.angularVelocity.y,
              z: fullPhysics.angularVelocity.z,
            },
          };
        }

        // 其他组件数据
        // ...
        break;

      case NetworkEntitySyncMode.CUSTOM:
        // 自定义同步数据
        data.customData = this.customData;
        break;
    }

    return data;
  }

  /**
   * 应用同步数据
   * @param data 同步数据
   */
  public applySyncData(data: any): void {
    // 根据同步模式应用不同的数据
    switch (this.syncMode) {
      case NetworkEntitySyncMode.TRANSFORM:
        // 应用变换数据
        if (data.transform) {
          const transform = this.entity.getComponent('Transform');
          if (transform) {
            if (data.transform.position) {
              transform.position.set(
                data.transform.position.x,
                data.transform.position.y,
                data.transform.position.z
              );
            }

            if (data.transform.rotation) {
              transform.rotation.set(
                data.transform.rotation.x,
                data.transform.rotation.y,
                data.transform.rotation.z,
                data.transform.rotation.w
              );
            }

            if (data.transform.scale) {
              transform.scale.set(
                data.transform.scale.x,
                data.transform.scale.y,
                data.transform.scale.z
              );
            }
          }
        }
        break;

      case NetworkEntitySyncMode.PHYSICS:
        // 应用物理数据
        if (data.physics) {
          const physics = this.entity.getComponent('PhysicsBody');
          if (physics) {
            if (data.physics.position) {
              physics.position.set(
                data.physics.position.x,
                data.physics.position.y,
                data.physics.position.z
              );
            }

            if (data.physics.rotation) {
              physics.rotation.set(
                data.physics.rotation.x,
                data.physics.rotation.y,
                data.physics.rotation.z,
                data.physics.rotation.w
              );
            }

            if (data.physics.velocity) {
              physics.velocity.set(
                data.physics.velocity.x,
                data.physics.velocity.y,
                data.physics.velocity.z
              );
            }

            if (data.physics.angularVelocity) {
              physics.angularVelocity.set(
                data.physics.angularVelocity.x,
                data.physics.angularVelocity.y,
                data.physics.angularVelocity.z
              );
            }
          }
        }
        break;

      case NetworkEntitySyncMode.FULL:
        // 应用所有数据
        // 变换数据
        if (data.transform) {
          const fullTransform = this.entity.getComponent('Transform');
          if (fullTransform) {
            if (data.transform.position) {
              fullTransform.position.set(
                data.transform.position.x,
                data.transform.position.y,
                data.transform.position.z
              );
            }

            if (data.transform.rotation) {
              fullTransform.rotation.set(
                data.transform.rotation.x,
                data.transform.rotation.y,
                data.transform.rotation.z,
                data.transform.rotation.w
              );
            }

            if (data.transform.scale) {
              fullTransform.scale.set(
                data.transform.scale.x,
                data.transform.scale.y,
                data.transform.scale.z
              );
            }
          }
        }

        // 物理数据
        if (data.physics) {
          const fullPhysics = this.entity.getComponent('PhysicsBody');
          if (fullPhysics) {
            if (data.physics.position) {
              fullPhysics.position.set(
                data.physics.position.x,
                data.physics.position.y,
                data.physics.position.z
              );
            }

            if (data.physics.rotation) {
              fullPhysics.rotation.set(
                data.physics.rotation.x,
                data.physics.rotation.y,
                data.physics.rotation.z,
                data.physics.rotation.w
              );
            }

            if (data.physics.velocity) {
              fullPhysics.velocity.set(
                data.physics.velocity.x,
                data.physics.velocity.y,
                data.physics.velocity.z
              );
            }

            if (data.physics.angularVelocity) {
              fullPhysics.angularVelocity.set(
                data.physics.angularVelocity.x,
                data.physics.angularVelocity.y,
                data.physics.angularVelocity.z
              );
            }
          }
        }

        // 其他组件数据
        // ...
        break;

      case NetworkEntitySyncMode.CUSTOM:
        // 应用自定义同步数据
        if (data.customData) {
          this.customData = { ...this.customData, ...data.customData };
        }
        break;
    }
  }

  /**
   * 标记属性为待同步
   * @param property 属性名
   */
  public markPropertyDirty(property: string): void {
    this.hasPendingChanges = true;
    this.pendingProperties.add(property);
  }

  /**
   * 标记所有属性为待同步
   */
  public markAllPropertiesDirty(): void {
    this.hasPendingChanges = true;
  }

  /**
   * 请求所有权
   * @returns 是否成功
   */
  public requestOwnership(): boolean {
    if (!this.canTransferOwnership) {
      return false;
    }

    // 触发请求所有权事件
    this.entity.emit('networkRequestOwnership', {
      entityId: this.entityId,
      currentOwnerId: this.ownerId,
    });

    return true;
  }

  /**
   * 转移所有权
   * @param newOwnerId 新所有者ID
   * @returns 是否成功
   */
  public transferOwnership(newOwnerId: string): boolean {
    if (!this.canTransferOwnership || !this.isLocallyOwned) {
      return false;
    }

    // 触发转移所有权事件
    this.entity.emit('networkTransferOwnership', {
      entityId: this.entityId,
      currentOwnerId: this.ownerId,
      newOwnerId,
    });

    return true;
  }

  /**
   * 设置所有者
   * @param ownerId 所有者ID
   * @param isLocallyOwned 是否是本地拥有
   */
  public setOwner(ownerId: string, isLocallyOwned: boolean): void {
    this.ownerId = ownerId;
    this.isLocallyOwned = isLocallyOwned;

    // 触发所有权变更事件
    this.entity.emit('networkOwnershipChanged', {
      entityId: this.entityId,
      ownerId,
      isLocallyOwned,
    });
  }

  /**
   * 销毁组件
   */
  public dispose(): void {
    // 销毁逻辑
  }
}
