# 喷泉系统教程

本教程将介绍如何使用引擎的喷泉系统创建各种类型的喷泉效果，包括标准喷泉、高喷泉、宽喷泉、多喷头喷泉、舞蹈喷泉、音乐喷泉、脉冲喷泉、交替喷泉、序列喷泉和随机喷泉等。

## 目录

1. [喷泉系统概述](#喷泉系统概述)
2. [创建基本喷泉](#创建基本喷泉)
3. [使用喷泉预设](#使用喷泉预设)
4. [自定义喷泉属性](#自定义喷泉属性)
5. [喷泉物理交互](#喷泉物理交互)
6. [喷泉粒子效果](#喷泉粒子效果)
7. [喷泉声音效果](#喷泉声音效果)
8. [高级喷泉效果](#高级喷泉效果)
9. [性能优化](#性能优化)
10. [常见问题解答](#常见问题解答)

## 喷泉系统概述

喷泉系统是IR引擎水体系统的扩展，专门用于创建各种类型的喷泉效果。喷泉系统由以下几个主要组件组成：

- **FountainComponent**：喷泉组件，用于定义喷泉的基本属性，如尺寸、位置、颜色等。
- **FountainPresets**：喷泉预设，提供各种类型的喷泉预设配置，如标准喷泉、高喷泉、宽喷泉等。
- **WaterPhysicsSystem**：水体物理系统，用于模拟喷泉的物理行为，如流动、湍流等。
- **WaterInteractionSystem**：水体交互系统，用于处理喷泉与其他物体的交互效果，如水花、水波纹等。
- **UnderwaterParticleSystem**：水下粒子系统，用于创建喷泉的粒子效果，如水雾、水花、水滴等。
- **AudioSystem**：音频系统，用于播放喷泉的声音效果。

## 创建基本喷泉

### 步骤1：创建喷泉组件

```typescript
// 创建喷泉实体
const fountainEntity = new Entity();
fountainEntity.setName('fountain');

// 创建喷泉组件
const fountainComponent = new FountainComponent(fountainEntity, {
  width: 5,
  height: 0.5,
  depth: 5,
  position: new THREE.Vector3(0, 0, 0),
  color: new THREE.Color(0xc0e0ff),
  opacity: 0.7,
  flowSpeed: 2.0,
  turbulenceStrength: 0.8,
  turbulenceFrequency: 1.5,
  turbulenceSpeed: 1.0,
  enableMistEffect: true,
  mistEffectStrength: 0.8,
  enableSplashEffect: true,
  splashEffectStrength: 0.8,
  enableDropletEffect: true,
  dropletEffectStrength: 0.8,
  enableSoundEffect: true,
  soundEffectVolume: 0.8,
  enableFluidDynamics: true,
  fountainType: FountainType.STANDARD,
  fountainMode: FountainMode.CONTINUOUS,
  jetHeight: 10.0,
  jetAngle: 0.0,
  jetCount: 1,
  jetInterval: 1.0,
  jetDuration: 2.0,
  jetDelay: 0.0,
  jetRandomness: 0.2
});

// 添加到世界
world.addEntity(fountainEntity);
```

## 使用喷泉预设

喷泉预设提供了各种类型的喷泉配置，可以快速创建不同类型的喷泉。

```typescript
// 使用预设创建标准喷泉
const standardFountainEntity = FountainPresets.createPreset(world, {
  type: FountainPresetType.STANDARD,
  position: new THREE.Vector3(0, 0, 0)
});

// 使用预设创建高喷泉
const highFountainEntity = FountainPresets.createPreset(world, {
  type: FountainPresetType.HIGH,
  position: new THREE.Vector3(20, 0, 0)
});

// 使用预设创建宽喷泉
const wideFountainEntity = FountainPresets.createPreset(world, {
  type: FountainPresetType.WIDE,
  position: new THREE.Vector3(-20, 0, 0)
});

// 使用预设创建多喷头喷泉
const multiJetFountainEntity = FountainPresets.createPreset(world, {
  type: FountainPresetType.MULTI_JET,
  position: new THREE.Vector3(0, 0, 20)
});

// 使用预设创建舞蹈喷泉
const dancingFountainEntity = FountainPresets.createPreset(world, {
  type: FountainPresetType.DANCING,
  position: new THREE.Vector3(0, 0, -20)
});

// 使用预设创建音乐喷泉
const musicalFountainEntity = FountainPresets.createPreset(world, {
  type: FountainPresetType.MUSICAL,
  position: new THREE.Vector3(20, 0, 20)
});

// 使用预设创建脉冲喷泉
const pulseFountainEntity = FountainPresets.createPreset(world, {
  type: FountainPresetType.PULSE,
  position: new THREE.Vector3(-20, 0, 20)
});

// 使用预设创建交替喷泉
const alternatingFountainEntity = FountainPresets.createPreset(world, {
  type: FountainPresetType.ALTERNATING,
  position: new THREE.Vector3(20, 0, -20)
});

// 使用预设创建序列喷泉
const sequenceFountainEntity = FountainPresets.createPreset(world, {
  type: FountainPresetType.SEQUENCE,
  position: new THREE.Vector3(-20, 0, -20)
});

// 使用预设创建随机喷泉
const randomFountainEntity = FountainPresets.createPreset(world, {
  type: FountainPresetType.RANDOM,
  position: new THREE.Vector3(0, 0, 40)
});
```

## 自定义喷泉属性

可以通过设置喷泉组件的属性来自定义喷泉效果。

```typescript
// 获取喷泉组件
const fountainComponent = fountainEntity.getComponent(FountainComponent);

// 设置喷泉类型
fountainComponent.setFountainType(FountainType.MULTI_JET);

// 设置喷泉模式
fountainComponent.setFountainMode(FountainMode.PULSE);

// 设置喷泉喷射高度
fountainComponent.setJetHeight(15.0);

// 设置喷泉喷射角度
fountainComponent.setJetAngle(10.0);

// 设置喷泉喷射数量
fountainComponent.setJetCount(5);

// 设置喷泉喷射间隔
fountainComponent.setJetInterval(2.0);

// 设置喷泉喷射持续时间
fountainComponent.setJetDuration(1.0);

// 设置喷泉喷射随机性
fountainComponent.setJetRandomness(0.3);

// 设置湍流强度
fountainComponent.setTurbulenceStrength(1.0);

// 设置湍流频率
fountainComponent.setTurbulenceFrequency(2.0);

// 设置湍流速度
fountainComponent.setTurbulenceSpeed(1.5);

// 设置是否启用水雾效果
fountainComponent.setEnableMistEffect(true);

// 设置水雾效果强度
fountainComponent.setMistEffectStrength(1.0);

// 设置是否启用水花效果
fountainComponent.setEnableSplashEffect(true);

// 设置水花效果强度
fountainComponent.setSplashEffectStrength(1.0);

// 设置是否启用水滴效果
fountainComponent.setEnableDropletEffect(true);

// 设置水滴效果强度
fountainComponent.setDropletEffectStrength(1.0);

// 设置是否启用声音效果
fountainComponent.setEnableSoundEffect(true);

// 设置声音效果音量
fountainComponent.setSoundEffectVolume(1.0);

// 设置是否启用水流动力学
fountainComponent.setEnableFluidDynamics(true);
```

## 喷泉物理交互

喷泉系统支持与物理系统的交互，可以实现物体与喷泉水流的交互效果。

```typescript
// 创建水体物理系统
const waterPhysicsSystem = new WaterPhysicsSystem(world, {
  enabled: true,
  autoUpdate: true,
  updateFrequency: 1,
  enableBuoyancy: true,
  enableDrag: true,
  enableFlow: true,
  enableWaves: true,
  enableCollision: true,
  enableParticles: true,
  enableMultithreading: true,
  workerCount: 4
});
world.addSystem(waterPhysicsSystem);

// 创建水体交互系统
const waterInteractionSystem = new WaterInteractionSystem(world, {
  enabled: true,
  autoUpdate: true,
  updateFrequency: 1,
  enableSplashEffect: true,
  enableRippleEffect: true,
  enableDropletEffect: true,
  enableFlowEffect: true,
  enableSplittingEffect: true,
  enableBuoyancyEffect: true,
  enableDragEffect: true
});
world.addSystem(waterInteractionSystem);
```

## 喷泉粒子效果

喷泉系统使用水下粒子系统来创建各种粒子效果，如水雾、水花、水滴等。

```typescript
// 创建水下粒子系统
const underwaterParticleSystem = new UnderwaterParticleSystem(world, {
  enabled: true,
  autoUpdate: true,
  updateFrequency: 1,
  maxParticles: 1000
});
world.addSystem(underwaterParticleSystem);
```

## 喷泉声音效果

喷泉系统使用音频系统来播放喷泉的声音效果。

```typescript
// 创建音频系统
const audioSystem = new AudioSystem(world, {
  enabled: true,
  autoUpdate: true
});
world.addSystem(audioSystem);
```

## 高级喷泉效果

### 舞蹈喷泉

```typescript
// 创建舞蹈喷泉
const dancingFountainEntity = FountainPresets.createPreset(world, {
  type: FountainPresetType.DANCING,
  position: new THREE.Vector3(0, 0, 0)
});
```

### 音乐喷泉

```typescript
// 创建音乐喷泉
const musicalFountainEntity = FountainPresets.createPreset(world, {
  type: FountainPresetType.MUSICAL,
  position: new THREE.Vector3(0, 0, 0)
});
```

### 交替喷泉

```typescript
// 创建交替喷泉
const alternatingFountainEntity = FountainPresets.createPreset(world, {
  type: FountainPresetType.ALTERNATING,
  position: new THREE.Vector3(0, 0, 0)
});
```

## 性能优化

### 多线程计算

```typescript
// 启用多线程计算
waterPhysicsSystem.setConfig({
  enableMultithreading: true,
  workerCount: 4
});
```

### 自适应更新频率

```typescript
// 启用自适应更新频率
waterPhysicsSystem.setConfig({
  enableAdaptiveUpdate: true,
  minUpdateFrequency: 1,
  maxUpdateFrequency: 10
});
```

### 空间分区

```typescript
// 启用空间分区
waterPhysicsSystem.setConfig({
  enableSpatialPartitioning: true,
  spatialGridSize: 10
});
```

## 常见问题解答

### 问题1：喷泉效果不显示

确保已经正确创建了喷泉组件，并且已经将喷泉实体添加到世界中。同时，确保已经创建了水体物理系统、水体交互系统和水下粒子系统，并且已经将它们添加到世界中。

### 问题2：喷泉效果不流畅

可能是由于性能问题导致的。尝试启用多线程计算、自适应更新频率和空间分区，以提高性能。同时，可以降低粒子数量和更新频率，以减少性能消耗。

### 问题3：喷泉声音不播放

确保已经正确创建了音频系统，并且已经将其添加到世界中。同时，确保喷泉组件的`enableSoundEffect`属性设置为`true`，并且`soundEffectVolume`属性设置为大于0的值。
