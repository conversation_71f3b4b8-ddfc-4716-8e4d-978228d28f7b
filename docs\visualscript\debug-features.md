# 视觉脚本调试功能使用指南

本文档介绍了IR引擎视觉脚本系统中的调试功能，包括断点、单步执行、变量监视和性能分析等，以及它们的使用方法和示例。

## 目录

- [调试功能概述](#调试功能概述)
- [断点功能](#断点功能)
  - [普通断点](#普通断点)
  - [条件断点](#条件断点)
  - [日志断点](#日志断点)
  - [断点管理器](#断点管理器)
- [单步执行](#单步执行)
- [变量监视](#变量监视)
  - [添加变量监视](#添加变量监视)
  - [条件变量监视](#条件变量监视)
  - [变量历史记录](#变量历史记录)
- [性能分析](#性能分析)
  - [节点执行时间分析](#节点执行时间分析)
  - [执行路径分析](#执行路径分析)
- [调试面板](#调试面板)
- [示例](#示例)
  - [基本调试示例](#基本调试示例)
  - [高级调试示例](#高级调试示例)

## 调试功能概述

视觉脚本系统提供了一系列强大的调试功能，帮助用户调试和优化脚本。这些功能包括断点、单步执行、变量监视和性能分析等，使用户能够更轻松地发现和解决问题。

## 断点功能

断点功能允许用户在脚本执行到特定节点时暂停执行，以便检查当前状态。

### 普通断点

普通断点是最基本的断点类型，当执行到设置了断点的节点时，脚本会暂停执行。

**设置方法**:
1. 在编辑器中双击节点
2. 使用断点管理器添加断点
3. 在代码中调用 `breakpointManager.addBreakpoint(nodeId, graphId)`

**示例**:
```typescript
// 添加普通断点
breakpointManager.addBreakpoint('node1', 'graph1');
```

### 条件断点

条件断点只有在满足特定条件时才会触发。这对于调试特定条件下的问题非常有用。

**设置方法**:
1. 在断点管理器中添加条件断点
2. 在代码中调用 `breakpointManager.addBreakpoint(nodeId, graphId, BreakpointType.CONDITIONAL, { condition: 'x > 10' })`

**示例**:
```typescript
// 添加条件断点
breakpointManager.addBreakpoint(
  'node1',
  'graph1',
  BreakpointType.CONDITIONAL,
  { condition: 'count > 10 && isActive' }
);
```

### 日志断点

日志断点不会暂停执行，而是在执行到断点时输出日志信息。这对于跟踪执行流程非常有用。

**设置方法**:
1. 在断点管理器中添加日志断点
2. 在代码中调用 `breakpointManager.addBreakpoint(nodeId, graphId, BreakpointType.LOG, { logMessage: '执行到节点: ${node.type}' })`

**示例**:
```typescript
// 添加日志断点
breakpointManager.addBreakpoint(
  'node1',
  'graph1',
  BreakpointType.LOG,
  { logMessage: '当前计数: ${count}, 状态: ${isActive ? "活跃" : "非活跃"}' }
);
```

### 断点管理器

断点管理器提供了一系列API，用于管理断点，包括添加、移除、启用、禁用和更新断点等。

**主要API**:
- `addBreakpoint(nodeId, graphId, type, options)`: 添加断点
- `removeBreakpoint(id)`: 移除断点
- `enableBreakpoint(id)`: 启用断点
- `disableBreakpoint(id)`: 禁用断点
- `updateBreakpoint(id, updates)`: 更新断点
- `getBreakpoint(id)`: 获取断点
- `getAllBreakpoints()`: 获取所有断点
- `clearAllBreakpoints()`: 清除所有断点

**示例**:
```typescript
// 创建断点管理器
const breakpointManager = new BreakpointManager();

// 添加断点
const breakpointId = breakpointManager.addBreakpoint('node1', 'graph1');

// 更新断点
breakpointManager.updateBreakpoint(breakpointId, {
  type: BreakpointType.CONDITIONAL,
  condition: 'count > 10'
});

// 禁用断点
breakpointManager.disableBreakpoint(breakpointId);

// 启用断点
breakpointManager.enableBreakpoint(breakpointId);

// 移除断点
breakpointManager.removeBreakpoint(breakpointId);
```

## 单步执行

单步执行功能允许用户逐步执行脚本，查看每一步的执行结果和变量值的变化。

**主要功能**:
- 单步执行: 执行当前节点，然后暂停
- 单步跳过: 执行当前节点及其子节点，然后暂停
- 单步跳出: 执行当前节点及其所有子节点，直到返回到上一级节点，然后暂停
- 继续执行: 继续执行，直到遇到下一个断点或脚本执行完毕

**示例**:
```typescript
// 单步执行
debugger.step(StepType.STEP_INTO);

// 单步跳过
debugger.step(StepType.STEP_OVER);

// 单步跳出
debugger.step(StepType.STEP_OUT);

// 继续执行
debugger.continue();
```

## 变量监视

变量监视功能允许用户监视变量的值，查看变量在执行过程中的变化。

### 添加变量监视

**设置方法**:
1. 在变量监视面板中添加变量
2. 在代码中调用 `variableWatcher.addWatch(name, graphId)`

**示例**:
```typescript
// 创建变量监视器
const variableWatcher = new VariableWatcher();

// 添加变量监视
variableWatcher.addWatch('count', 'graph1');
```

### 条件变量监视

条件变量监视只有在满足特定条件时才会触发。这对于监视特定条件下的变量变化非常有用。

**设置方法**:
1. 在变量监视面板中添加条件变量监视
2. 在代码中调用 `variableWatcher.addWatch(name, graphId, { condition: 'value > 10' })`

**示例**:
```typescript
// 添加条件变量监视
variableWatcher.addWatch(
  'count',
  'graph1',
  { condition: 'value > 10 && value < 20' }
);
```

### 变量历史记录

变量历史记录功能允许用户查看变量的历史值，了解变量是如何变化的。

**主要API**:
- `getHistory(name, graphId)`: 获取变量历史记录
- `clearHistory(name, graphId)`: 清除变量历史记录
- `clearAllHistory()`: 清除所有历史记录

**示例**:
```typescript
// 获取变量历史记录
const history = variableWatcher.getHistory('count', 'graph1');

// 清除变量历史记录
variableWatcher.clearHistory('count', 'graph1');

// 清除所有历史记录
variableWatcher.clearAllHistory();
```

## 性能分析

性能分析功能允许用户分析脚本的性能，找出性能瓶颈。

### 节点执行时间分析

节点执行时间分析功能允许用户查看每个节点的执行时间，找出执行时间较长的节点。

**主要功能**:
- 记录节点执行时间
- 显示节点执行时间统计
- 生成节点执行时间热图

**示例**:
```typescript
// 启用性能分析
debugger.enablePerformanceAnalysis();

// 获取节点执行时间统计
const statistics = debugger.getNodeExecutionTimeStatistics();

// 禁用性能分析
debugger.disablePerformanceAnalysis();
```

### 执行路径分析

执行路径分析功能允许用户查看脚本的执行路径，了解脚本是如何执行的。

**主要功能**:
- 记录执行路径
- 显示执行路径统计
- 生成执行路径图

**示例**:
```typescript
// 获取执行路径
const path = debugger.getExecutionPath();

// 清除执行路径
debugger.clearExecutionPath();
```

## 调试面板

调试面板提供了一个图形化界面，用于管理断点、监视变量、单步执行和性能分析等。

**主要功能**:
- 断点管理: 添加、移除、启用、禁用和编辑断点
- 变量监视: 添加、移除和编辑变量监视
- 单步执行: 单步执行、单步跳过、单步跳出和继续执行
- 性能分析: 查看节点执行时间和执行路径

## 示例

### 基本调试示例

以下是一个基本的调试示例，演示如何使用断点和单步执行功能：

```typescript
// 创建断点管理器
const breakpointManager = new BreakpointManager();

// 添加断点
breakpointManager.addBreakpoint('node1', 'graph1');

// 创建变量监视器
const variableWatcher = new VariableWatcher();

// 添加变量监视
variableWatcher.addWatch('count', 'graph1');

// 启动调试会话
const debugger = new VisualScriptDebugger(graph, breakpointManager, variableWatcher);

// 开始执行
debugger.start();

// 当断点触发时
debugger.on('breakpointHit', (info) => {
  console.log(`断点触发: ${info.breakpoint.id}`);
  
  // 获取变量值
  const count = debugger.getVariableValue('count');
  console.log(`当前计数: ${count}`);
  
  // 单步执行
  debugger.step(StepType.STEP_INTO);
});
```

### 高级调试示例

请参考 `examples/visualscript/DebugExample.ts` 中的完整示例，该示例演示了如何使用视觉脚本系统的高级调试功能。
