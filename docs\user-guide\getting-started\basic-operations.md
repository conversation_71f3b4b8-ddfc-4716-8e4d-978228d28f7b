# 基本操作

本文档将介绍IR引擎编辑器的基本操作，帮助您快速上手并开始创建3D场景。

## 创建新项目

1. 启动IR引擎编辑器
2. 点击顶部菜单栏的"文件 > 新建项目"
3. 在弹出的对话框中，输入项目名称和保存位置
4. 选择项目模板（空白项目、基础场景、游戏模板等）
5. 点击"创建"按钮完成项目创建

![新建项目](../../assets/images/new-project-dialog.png)

## 打开现有项目

1. 点击顶部菜单栏的"文件 > 打开项目"
2. 在弹出的文件浏览器中，找到并选择要打开的项目文件（.irproj）
3. 点击"打开"按钮

或者，您可以从最近项目列表中选择：

1. 点击顶部菜单栏的"文件 > 最近项目"
2. 从下拉菜单中选择要打开的项目

## 保存项目

1. 点击顶部菜单栏的"文件 > 保存项目"或按下Ctrl+S（Windows）/Command+S（Mac）
2. 如果是首次保存，会弹出对话框，输入项目名称和保存位置
3. 点击"保存"按钮

## 场景导航

### 旋转视图

- 按住鼠标右键并拖动鼠标

### 平移视图

- 按住鼠标中键（或按住Alt键+鼠标左键）并拖动鼠标

### 缩放视图

- 滚动鼠标滚轮向上或向下

### 聚焦对象

1. 在场景或层次面板中选择一个对象
2. 按下F键，视图将自动聚焦到选中的对象

## 选择对象

### 单选

- 在场景视图中点击对象
- 或在层次面板中点击对象名称

### 多选

- 按住Ctrl键（Windows）或Command键（Mac）的同时点击多个对象
- 或在场景视图中按住鼠标左键拖动，创建选择框

### 取消选择

- 点击场景中的空白区域
- 或按下Esc键

## 变换操作

IR引擎编辑器提供了三种基本的变换操作：移动、旋转和缩放。

### 移动对象

1. 选择要移动的对象
2. 按下W键或点击工具栏中的移动工具按钮
3. 拖动出现的坐标轴控制柄：
   - 红色轴：X轴移动
   - 绿色轴：Y轴移动
   - 蓝色轴：Z轴移动
   - 彩色平面：在平面上移动

![移动工具](../../assets/images/move-tool.png)

### 旋转对象

1. 选择要旋转的对象
2. 按下E键或点击工具栏中的旋转工具按钮
3. 拖动出现的旋转控制柄：
   - 红色环：绕X轴旋转
   - 绿色环：绕Y轴旋转
   - 蓝色环：绕Z轴旋转
   - 白色外环：自由旋转

![旋转工具](../../assets/images/rotate-tool.png)

### 缩放对象

1. 选择要缩放的对象
2. 按下R键或点击工具栏中的缩放工具按钮
3. 拖动出现的缩放控制柄：
   - 红色轴：X轴缩放
   - 绿色轴：Y轴缩放
   - 蓝色轴：Z轴缩放
   - 中心白色立方体：统一缩放

![缩放工具](../../assets/images/scale-tool.png)

### 变换空间切换

您可以在世界空间和局部空间之间切换变换操作：

- 按下X键切换变换空间
- 或点击工具栏中的变换空间切换按钮

### 精确变换

对于需要精确数值的变换，可以使用属性面板：

1. 选择对象
2. 在属性面板的"变换"部分，直接输入位置、旋转或缩放的数值

## 添加对象

1. 点击顶部菜单栏的"创建"或右键点击层次面板中的空白区域
2. 从下拉菜单中选择要创建的对象类型：
   - 3D对象（立方体、球体、圆柱体等）
   - 灯光（点光源、方向光、聚光灯等）
   - 相机
   - 空对象
   - 其他特殊对象

![添加对象](../../assets/images/add-object-menu.png)

## 删除对象

1. 选择要删除的对象
2. 按下Delete键
3. 或右键点击对象，选择"删除"

## 复制对象

### 快速复制

1. 选择要复制的对象
2. 按下Ctrl+D（Windows）或Command+D（Mac）

### 复制粘贴

1. 选择要复制的对象
2. 按下Ctrl+C（Windows）或Command+C（Mac）复制
3. 按下Ctrl+V（Windows）或Command+V（Mac）粘贴

## 重命名对象

1. 在层次面板中选择对象
2. 按下F2键或右键点击选择"重命名"
3. 输入新名称，按Enter确认

## 组织对象

### 创建父子关系

1. 在层次面板中，将一个对象拖动到另一个对象上
2. 释放鼠标按钮，第一个对象将成为第二个对象的子对象

### 创建空对象作为容器

1. 右键点击层次面板，选择"创建空对象"
2. 将相关对象拖动到空对象下，组织成一个组

## 使用组件

IR引擎编辑器使用组件系统为对象添加功能。

### 添加组件

1. 选择对象
2. 在属性面板底部，点击"添加组件"按钮
3. 从组件列表中选择要添加的组件类型

![添加组件](../../assets/images/add-component.png)

### 编辑组件属性

1. 选择对象
2. 在属性面板中找到要编辑的组件
3. 修改组件的属性值

### 移除组件

1. 选择对象
2. 在属性面板中找到要移除的组件
3. 点击组件标题右侧的齿轮图标，选择"移除组件"

## 预览场景

1. 点击工具栏中的播放按钮或按下Ctrl+P（Windows）/Command+P（Mac）
2. 场景将进入播放模式，您可以看到动画、物理模拟等效果
3. 再次点击播放按钮或按下Esc键退出预览模式

## 保存场景

1. 点击顶部菜单栏的"文件 > 保存场景"或按下Ctrl+S（Windows）/Command+S（Mac）
2. 如果是首次保存，会弹出对话框，输入场景名称
3. 点击"保存"按钮

## 下一步

现在您已经了解了编辑器的基本操作，可以继续学习更多高级功能：

- [场景编辑](../features/scene-editing.md)
- [材质编辑](../features/material-editing.md)
- [动画系统](../features/animation-system.md)
- [物理系统](../features/physics-system.md)
