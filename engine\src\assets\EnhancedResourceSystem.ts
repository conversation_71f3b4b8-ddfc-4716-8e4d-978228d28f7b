/**
 * 增强资源管理系统
 * 整合资源加载、缓存、依赖和预加载功能
 */
import { EventEmitter } from '../utils/EventEmitter';
import { AssetType, ResourceState } from './ResourceManager';
import { EnhancedResourceManager, EnhancedResourceManagerOptions } from './EnhancedResourceManager';
import { DependencyType, EnhancedResourceDependencyManager, EnhancedResourceDependencyManagerOptions } from './EnhancedResourceDependencyManager';
import { EnhancedResourcePreloader, EnhancedResourcePreloaderOptions, PreloadGroupInfo, PreloadProgressInfo, PreloadResourceInfo } from './EnhancedResourcePreloader';

/**
 * 增强资源管理系统选项
 */
export interface EnhancedResourceSystemOptions {
  /** 资源管理器选项 */
  resourceManagerOptions?: EnhancedResourceManagerOptions;
  /** 依赖管理器选项 */
  dependencyManagerOptions?: EnhancedResourceDependencyManagerOptions;
  /** 预加载器选项 */
  preloaderOptions?: Omit<EnhancedResourcePreloaderOptions, 'resourceManager' | 'dependencyManager'>;
  /** 是否启用调试模式 */
  debug?: boolean;
}

/**
 * 增强资源管理系统
 */
export class EnhancedResourceSystem extends EventEmitter {
  /** 资源管理器 */
  private resourceManager: EnhancedResourceManager;

  /** 依赖管理器 */
  private dependencyManager: EnhancedResourceDependencyManager;

  /** 预加载器 */
  private preloader: EnhancedResourcePreloader;

  /** 是否启用调试模式 */
  private debug: boolean;

  /** 是否已初始化 */
  private initialized: boolean = false;

  /**
   * 创建增强资源管理系统实例
   * @param options 资源管理系统选项
   */
  constructor(options: EnhancedResourceSystemOptions = {}) {
    super();

    this.debug = options.debug !== undefined ? options.debug : false;

    // 创建资源管理器
    this.resourceManager = new EnhancedResourceManager({
      debug: this.debug,
      ...options.resourceManagerOptions
    });

    // 创建依赖管理器
    this.dependencyManager = new EnhancedResourceDependencyManager({
      debug: this.debug,
      ...options.dependencyManagerOptions
    });

    // 创建预加载器
    this.preloader = new EnhancedResourcePreloader({
      resourceManager: this.resourceManager,
      dependencyManager: this.dependencyManager,
      debug: this.debug,
      ...options.preloaderOptions
    });

    // 转发事件
    this.forwardEvents();
  }

  /**
   * 转发子组件事件
   */
  private forwardEvents(): void {
    // 转发资源管理器事件
    this.resourceManager.on('loadStart', (data) => this.emit('resourceLoadStart', data));
    this.resourceManager.on('loadComplete', (data) => this.emit('resourceLoadComplete', data));
    this.resourceManager.on('loadError', (data) => this.emit('resourceLoadError', data));
    this.resourceManager.on('resourceReleased', (data) => this.emit('resourceReleased', data));
    this.resourceManager.on('cacheCleanup', (data) => this.emit('cacheCleanup', data));
    this.resourceManager.on('cacheCleared', () => this.emit('cacheCleared'));

    // 转发依赖管理器事件
    this.dependencyManager.on('dependencyAdded', (data) => this.emit('dependencyAdded', data));
    this.dependencyManager.on('dependencyRemoved', (data) => this.emit('dependencyRemoved', data));
    this.dependencyManager.on('dependencyUpdated', (data) => this.emit('dependencyUpdated', data));
    this.dependencyManager.on('dependenciesOptimized', () => this.emit('dependenciesOptimized'));
    this.dependencyManager.on('dependenciesCleared', () => this.emit('dependenciesCleared'));

    // 转发预加载器事件
    this.preloader.on('groupAdded', (data) => this.emit('preloadGroupAdded', data));
    this.preloader.on('groupRemoved', (data) => this.emit('preloadGroupRemoved', data));
    this.preloader.on('groupsCleared', () => this.emit('preloadGroupsCleared'));
    this.preloader.on('loadStart', (data) => this.emit('preloadStart', data));
    this.preloader.on('loadComplete', (data) => this.emit('preloadComplete', data));
    this.preloader.on('loadError', (data) => this.emit('preloadError', data));
    this.preloader.on('loadPaused', (data) => this.emit('preloadPaused', data));
    this.preloader.on('loadResumed', (data) => this.emit('preloadResumed', data));
    this.preloader.on('loadCancelled', () => this.emit('preloadCancelled'));
    this.preloader.on('progressUpdated', (data) => this.emit('preloadProgressUpdated', data));
  }

  /**
   * 初始化资源管理系统
   */
  public initialize(): void {
    if (this.initialized) {
      return;
    }

    // 初始化各组件
    this.resourceManager.initialize();
    this.dependencyManager.initialize();
    this.preloader.initialize();

    this.initialized = true;
    this.emit('initialized');

    if (this.debug) {
      console.log('[ResourceSystem] 资源管理系统已初始化');
    }
  }

  /**
   * 加载资源
   * @param id 资源ID
   * @param type 资源类型
   * @param url 资源URL
   * @param priority 资源优先级
   * @returns Promise，解析为资源数据
   */
  public async load(id: string, type: AssetType, url: string, priority: number = 0): Promise<any> {
    return this.resourceManager.load(id, type, url, priority);
  }

  /**
   * 释放资源
   * @param id 资源ID
   * @returns 是否成功释放
   */
  public release(id: string): boolean {
    return this.resourceManager.release(id);
  }

  /**
   * 获取资源数据
   * @param id 资源ID
   * @returns 资源数据
   */
  public getResource(id: string): any {
    return this.resourceManager.getResourceData(id);
  }

  /**
   * 添加依赖关系
   * @param resourceId 资源ID
   * @param dependencyId 依赖资源ID
   * @param type 依赖类型
   * @param priority 依赖优先级
   * @returns 是否成功添加
   */
  public addDependency(
    resourceId: string,
    dependencyId: string,
    type: DependencyType = DependencyType.STRONG,
    priority: number = 0
  ): boolean {
    return this.dependencyManager.addDependency(resourceId, dependencyId, type, priority);
  }

  /**
   * 移除依赖关系
   * @param resourceId 资源ID
   * @param dependencyId 依赖资源ID
   * @returns 是否成功移除
   */
  public removeDependency(resourceId: string, dependencyId: string): boolean {
    return this.dependencyManager.removeDependency(resourceId, dependencyId);
  }

  /**
   * 获取资源的依赖
   * @param resourceId 资源ID
   * @returns 依赖信息数组
   */
  public getDependencies(resourceId: string): any[] {
    return this.dependencyManager.getDependencies(resourceId);
  }

  /**
   * 添加预加载组
   * @param group 预加载组信息
   * @returns 是否成功添加
   */
  public addPreloadGroup(group: PreloadGroupInfo): boolean {
    return this.preloader.addGroup(group);
  }

  /**
   * 移除预加载组
   * @param name 组名
   * @returns 是否成功移除
   */
  public removePreloadGroup(name: string): boolean {
    return this.preloader.removeGroup(name);
  }

  /**
   * 加载预加载组
   * @param name 组名
   * @param onProgress 进度回调
   * @returns Promise，解析为加载结果
   */
  public async loadPreloadGroup(
    name: string,
    onProgress?: (progress: PreloadProgressInfo) => void
  ): Promise<PreloadProgressInfo> {
    return this.preloader.loadGroup(name, onProgress);
  }

  /**
   * 暂停预加载
   * @returns 是否成功暂停
   */
  public pausePreload(): boolean {
    return this.preloader.pause();
  }

  /**
   * 恢复预加载
   * @returns 是否成功恢复
   */
  public resumePreload(): boolean {
    return this.preloader.resume();
  }

  /**
   * 取消预加载
   * @returns 是否成功取消
   */
  public cancelPreload(): boolean {
    return this.preloader.cancel();
  }

  /**
   * 获取预加载组
   * @param name 组名
   * @returns 预加载组信息
   */
  public getPreloadGroup(name: string): PreloadGroupInfo | null {
    return this.preloader.getGroup(name);
  }

  /**
   * 获取预加载进度
   * @param name 组名
   * @returns 预加载进度信息
   */
  public getPreloadProgress(name: string): PreloadProgressInfo | null {
    return this.preloader.getProgress(name);
  }

  /**
   * 清理缓存
   */
  public cleanupCache(): void {
    this.resourceManager.cleanup();
  }

  /**
   * 清空缓存
   */
  public clearCache(): void {
    this.resourceManager.clearCache();
  }

  /**
   * 优化依赖关系
   * @returns 是否成功优化
   */
  public optimizeDependencies(): boolean {
    return this.dependencyManager.optimizeDependencies();
  }

  /**
   * 清除所有依赖关系
   */
  public clearDependencies(): void {
    this.dependencyManager.clearDependencies();
  }

  /**
   * 清除所有预加载组
   */
  public clearPreloadGroups(): void {
    this.preloader.clearGroups();
  }

  /**
   * 获取缓存统计信息
   * @returns 缓存统计信息
   */
  public getCacheStats(): any {
    return this.resourceManager.getCacheStats();
  }

  /**
   * 销毁资源管理系统
   */
  public dispose(): void {
    // 销毁各组件
    this.preloader.dispose();
    this.dependencyManager.dispose();
    this.resourceManager.dispose();

    // 移除所有事件监听器
    this.removeAllListeners();

    this.initialized = false;

    if (this.debug) {
      console.log('[ResourceSystem] 资源管理系统已销毁');
    }
  }
}
