/**
 * 动画事件系统
 * 用于在动画播放过程中触发事件
 */
import { Entity } from '../core/Entity';
import { Component } from '../core/Component';
import { System } from '../core/System';
import { World } from '../core/World';
import { EventEmitter } from '../utils/EventEmitter';
import { Animator } from './Animator';

/**
 * 动画事件类型
 */
export enum AnimationEventType {
  /** 开始 */
  START = 'start',
  /** 结束 */
  COMPLETE = 'complete',
  /** 循环 */
  LOOP = 'loop',
  /** 自定义 */
  CUSTOM = 'custom'
}

/**
 * 动画事件数据
 */
export interface AnimationEventData {
  /** 事件类型 */
  type: AnimationEventType;
  /** 事件名称 */
  name: string;
  /** 时间点（秒） */
  time: number;
  /** 动画片段名称 */
  clipName: string;
  /** 参数 */
  params?: any;
}

/**
 * 动画事件组件
 */
export class AnimationEventComponent extends Component {
  /** 组件类型 */
  public static readonly type: string = 'AnimationEvent';

  /** 事件列表 */
  private events: Map<string, AnimationEventData[]> = new Map();
  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();
  /** 动画控制器 */
  private animator: Animator | null = null;
  /** 上一帧时间 */
  private lastTime: Map<string, number> = new Map();
  /** 是否启用 */
  private eventEnabled: boolean = true;

  /**
   * 创建动画事件组件
   */
  constructor() {
    super(AnimationEventComponent.type);
  }

  /**
   * 设置动画控制器
   * @param animator 动画控制器
   */
  public setAnimator(animator: Animator): void {
    this.animator = animator;
  }

  /**
   * 获取动画控制器
   * @returns 动画控制器
   */
  public getAnimator(): Animator | null {
    return this.animator;
  }

  /**
   * 添加事件
   * @param clipName 动画片段名称
   * @param eventData 事件数据
   */
  public addEvent(clipName: string, eventData: AnimationEventData): void {
    // 获取动画片段的事件列表
    let events = this.events.get(clipName);
    if (!events) {
      events = [];
      this.events.set(clipName, events);
    }

    // 添加事件
    events.push(eventData);

    // 按时间排序
    events.sort((a, b) => a.time - b.time);
  }

  /**
   * 移除事件
   * @param clipName 动画片段名称
   * @param eventName 事件名称
   * @returns 是否成功移除
   */
  public removeEvent(clipName: string, eventName: string): boolean {
    // 获取动画片段的事件列表
    const events = this.events.get(clipName);
    if (!events) return false;

    // 查找事件
    const index = events.findIndex(event => event.name === eventName);
    if (index === -1) return false;

    // 移除事件
    events.splice(index, 1);

    return true;
  }

  /**
   * 清除事件
   * @param clipName 动画片段名称，如果不指定则清除所有事件
   */
  public clearEvents(clipName?: string): void {
    if (clipName) {
      this.events.delete(clipName);
    } else {
      this.events.clear();
    }
  }

  /**
   * 获取事件
   * @param clipName 动画片段名称
   * @returns 事件列表
   */
  public getEvents(clipName: string): AnimationEventData[] {
    return this.events.get(clipName) || [];
  }

  /**
   * 添加事件监听器
   * @param type 事件类型
   * @param listener 监听器
   */
  public addEventListener(type: string, listener: (data: any) => void): void {
    this.eventEmitter.on(type, listener);
  }

  /**
   * 移除事件监听器
   * @param type 事件类型
   * @param listener 监听器
   */
  public removeEventListener(type: string, listener: (data: any) => void): void {
    this.eventEmitter.off(type, listener);
  }

  /**
   * 是否启用
   * @returns 是否启用
   */
  public isEnabled(): boolean {
    return this.eventEnabled;
  }

  /**
   * 设置启用状态
   * @param enabled 启用状态
   */
  public setEnabled(enabled: boolean): void {
    this.eventEnabled = enabled;
  }

  /**
   * 更新组件
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(_deltaTime: number): void {
    if (!this.eventEnabled || !this.animator) return;

    // 获取当前动画
    const currentClip = this.animator.getCurrentClip();
    if (!currentClip) return;

    // 获取当前时间
    const currentTime = this.animator.getTime();

    // 获取上一帧时间
    const lastTime = this.lastTime.get(currentClip.name) || 0;

    // 更新上一帧时间
    this.lastTime.set(currentClip.name, currentTime);

    // 获取事件列表
    const events = this.events.get(currentClip.name);
    if (!events) return;

    // 检查事件
    for (const event of events) {
      // 如果事件时间在当前帧范围内
      if (event.time >= lastTime && event.time < currentTime) {
        // 触发事件
        this.eventEmitter.emit(event.name, event);
        this.eventEmitter.emit(event.type, event);
      }
    }

    // 检查循环事件
    if (currentClip.loopMode && currentTime < lastTime) {
      // 触发循环事件
      const loopEvent: AnimationEventData = {
        type: AnimationEventType.LOOP,
        name: `${currentClip.name}_loop`,
        time: currentTime,
        clipName: currentClip.name
      };
      this.eventEmitter.emit(loopEvent.name, loopEvent);
      this.eventEmitter.emit(loopEvent.type, loopEvent);
    }
  }
}

/**
 * 动画事件系统
 */
export class AnimationEventSystem extends System {
  /** 组件列表 */
  private components: Map<Entity, AnimationEventComponent> = new Map();
  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();
  /** 是否启用调试 */
  private debug: boolean = false;

  /**
   * 创建动画事件系统
   * @param world 世界
   * @param debug 是否启用调试
   */
  constructor(_world: World, debug: boolean = false) {
    super();
    this.debug = debug;
  }

  /**
   * 创建动画事件组件
   * @param entity 实体
   * @param animator 动画控制器
   * @returns 动画事件组件
   */
  public createAnimationEvent(entity: Entity, animator: Animator): AnimationEventComponent {
    // 创建组件
    const component = new AnimationEventComponent();
    component.setAnimator(animator);

    // 添加到实体
    entity.addComponent(component);

    // 添加到映射
    this.components.set(entity, component);

    return component;
  }

  /**
   * 移除动画事件组件
   * @param entity 实体
   * @returns 是否成功移除
   */
  public removeAnimationEvent(entity: Entity): boolean {
    // 检查实体是否有动画事件组件
    const component = this.components.get(entity);
    if (!component) {
      return false;
    }

    // 从映射中移除
    this.components.delete(entity);

    // 从实体中移除
    entity.removeComponent(AnimationEventComponent.type);

    return true;
  }

  /**
   * 添加事件
   * @param entity 实体
   * @param clipName 动画片段名称
   * @param eventData 事件数据
   * @returns 是否成功添加
   */
  public addEvent(entity: Entity, clipName: string, eventData: AnimationEventData): boolean {
    // 获取动画事件组件
    const component = this.components.get(entity);
    if (!component) {
      return false;
    }

    // 添加事件
    component.addEvent(clipName, eventData);

    if (this.debug) {
      console.log(`添加动画事件: ${clipName} - ${eventData.name} @ ${eventData.time}s`);
    }

    return true;
  }

  /**
   * 移除事件
   * @param entity 实体
   * @param clipName 动画片段名称
   * @param eventName 事件名称
   * @returns 是否成功移除
   */
  public removeEvent(entity: Entity, clipName: string, eventName: string): boolean {
    // 获取动画事件组件
    const component = this.components.get(entity);
    if (!component) {
      return false;
    }

    // 移除事件
    const result = component.removeEvent(clipName, eventName);

    if (result && this.debug) {
      console.log(`移除动画事件: ${clipName} - ${eventName}`);
    }

    return result;
  }

  /**
   * 添加事件监听器
   * @param entity 实体
   * @param type 事件类型
   * @param listener 监听器
   * @returns 是否成功添加
   */
  public addEventListener(entity: Entity, type: string, listener: (data: any) => void): boolean {
    // 获取动画事件组件
    const component = this.components.get(entity);
    if (!component) {
      return false;
    }

    // 添加事件监听器
    component.addEventListener(type, listener);

    return true;
  }

  /**
   * 移除事件监听器
   * @param entity 实体
   * @param type 事件类型
   * @param listener 监听器
   * @returns 是否成功移除
   */
  public removeEventListener(entity: Entity, type: string, listener: (data: any) => void): boolean {
    // 获取动画事件组件
    const component = this.components.get(entity);
    if (!component) {
      return false;
    }

    // 移除事件监听器
    component.removeEventListener(type, listener);

    return true;
  }

  /**
   * 添加全局事件监听器
   * @param type 事件类型
   * @param listener 监听器
   */
  public addGlobalEventListener(type: string, listener: (data: any) => void): void {
    this.eventEmitter.on(type, listener);
  }

  /**
   * 移除全局事件监听器
   * @param type 事件类型
   * @param listener 监听器
   */
  public removeGlobalEventListener(type: string, listener: (data: any) => void): void {
    this.eventEmitter.off(type, listener);
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    // 更新所有动画事件组件
    for (const [, component] of this.components) {
      if (component.isEnabled()) {
        component.update(deltaTime);
      }
    }
  }
}
