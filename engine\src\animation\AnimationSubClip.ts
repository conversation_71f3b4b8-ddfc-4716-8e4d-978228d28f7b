/**
 * 动画子片段
 * 用于从动画剪辑中提取子片段
 */

import * as THREE from 'three';
import { EventEmitter } from '../core/EventEmitter';

/**
 * 子片段事件类型
 */
export enum SubClipEventType {
  /** 子片段创建 */
  CREATED = 'created',
  /** 子片段更新 */
  UPDATED = 'updated',
  /** 子片段删除 */
  DELETED = 'deleted',
  /** 子片段播放 */
  PLAYED = 'played',
  /** 子片段停止 */
  STOPPED = 'stopped',
  /** 子片段循环 */
  LOOPED = 'looped'
}

/**
 * 子片段配置
 */
export interface SubClipConfig {
  /** 子片段名称 */
  name?: string;
  /** 原始剪辑名称 */
  originalClipName?: string;
  /** 开始时间（秒） */
  startTime?: number;
  /** 结束时间（秒） */
  endTime?: number;
  /** 是否循环 */
  loop?: boolean;
  /** 是否反向播放 */
  reverse?: boolean;
  /** 播放速度 */
  timeScale?: number;
  /** 是否启用调试 */
  debug?: boolean;
}

/**
 * 动画子片段
 */
export class AnimationSubClip {
  /** 子片段名称 */
  private name: string;
  /** 原始剪辑名称 */
  private originalClipName: string;
  /** 开始时间（秒） */
  private startTime: number;
  /** 结束时间（秒） */
  private endTime: number;
  /** 是否循环 */
  private loop: boolean;
  /** 是否反向播放 */
  private reverse: boolean;
  /** 播放速度 */
  private timeScale: number;
  /** 是否启用调试 */
  private debug: boolean;
  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();
  /** 子片段 */
  private subClip: THREE.AnimationClip | null = null;
  /** 原始剪辑 */
  private originalClip: THREE.AnimationClip | null = null;
  /** 子片段列表 */
  private subClips: AnimationSubClip[] = [];

  /**
   * 创建动画子片段
   * @param config 配置
   */
  constructor(config: SubClipConfig = {}) {
    this.name = config.name || 'subClip';
    this.originalClipName = config.originalClipName || '';
    this.startTime = config.startTime !== undefined ? config.startTime : 0;
    this.endTime = config.endTime !== undefined ? config.endTime : 0;
    this.loop = config.loop !== undefined ? config.loop : false;
    this.reverse = config.reverse !== undefined ? config.reverse : false;
    this.timeScale = config.timeScale !== undefined ? config.timeScale : 1.0;
    this.debug = config.debug !== undefined ? config.debug : false;
  }

  /**
   * 获取子片段名称
   * @returns 子片段名称
   */
  public getName(): string {
    return this.name;
  }

  /**
   * 设置子片段名称
   * @param name 子片段名称
   */
  public setName(name: string): void {
    this.name = name;
  }

  /**
   * 获取原始剪辑名称
   * @returns 原始剪辑名称
   */
  public getOriginalClipName(): string {
    return this.originalClipName;
  }

  /**
   * 设置原始剪辑名称
   * @param name 原始剪辑名称
   */
  public setOriginalClipName(name: string): void {
    this.originalClipName = name;
  }

  /**
   * 获取开始时间
   * @returns 开始时间（秒）
   */
  public getStartTime(): number {
    return this.startTime;
  }

  /**
   * 设置开始时间
   * @param time 开始时间（秒）
   */
  public setStartTime(time: number): void {
    this.startTime = time;
    this.updateSubClip();
  }

  /**
   * 获取结束时间
   * @returns 结束时间（秒）
   */
  public getEndTime(): number {
    return this.endTime;
  }

  /**
   * 设置结束时间
   * @param time 结束时间（秒）
   */
  public setEndTime(time: number): void {
    this.endTime = time;
    this.updateSubClip();
  }

  /**
   * 获取是否循环
   * @returns 是否循环
   */
  public getLoop(): boolean {
    return this.loop;
  }

  /**
   * 设置是否循环
   * @param loop 是否循环
   */
  public setLoop(loop: boolean): void {
    this.loop = loop;
  }

  /**
   * 获取是否反向播放
   * @returns 是否反向播放
   */
  public getReverse(): boolean {
    return this.reverse;
  }

  /**
   * 设置是否反向播放
   * @param reverse 是否反向播放
   */
  public setReverse(reverse: boolean): void {
    this.reverse = reverse;
    this.updateSubClip();
  }

  /**
   * 获取播放速度
   * @returns 播放速度
   */
  public getTimeScale(): number {
    return this.timeScale;
  }

  /**
   * 设置播放速度
   * @param timeScale 播放速度
   */
  public setTimeScale(timeScale: number): void {
    this.timeScale = timeScale;
  }

  /**
   * 获取子片段
   * @returns 子片段
   */
  public getSubClip(): THREE.AnimationClip | null {
    return this.subClip;
  }

  /**
   * 设置原始剪辑
   * @param clip 原始剪辑
   */
  public setOriginalClip(clip: THREE.AnimationClip): void {
    this.originalClip = clip;
    this.updateSubClip();
  }

  /**
   * 更新子片段
   */
  private updateSubClip(): void {
    if (!this.originalClip) return;

    // 计算开始和结束帧
    const duration = this.originalClip.duration;
    const startTime = Math.max(0, Math.min(this.startTime, duration));
    const endTime = Math.max(startTime, Math.min(this.endTime, duration));

    // 创建子片段
    this.subClip = THREE.AnimationUtils.subclip(
      this.originalClip,
      this.name,
      startTime,
      endTime,
      this.reverse ? -1 : 1
    );

    // 触发更新事件
    this.eventEmitter.emit(SubClipEventType.UPDATED, {
      subClip: this,
      clip: this.subClip
    });

    if (this.debug) {
      console.log(`更新子片段: ${this.name}, 开始时间: ${startTime}, 结束时间: ${endTime}`);
    }
  }

  /**
   * 添加子片段
   * @param subClip 子片段
   */
  public addSubClip(subClip: AnimationSubClip): void {
    this.subClips.push(subClip);
  }

  /**
   * 移除子片段
   * @param subClip 子片段
   */
  public removeSubClip(subClip: AnimationSubClip): void {
    const index = this.subClips.indexOf(subClip);
    if (index !== -1) {
      this.subClips.splice(index, 1);
    }
  }

  /**
   * 获取子片段列表
   * @returns 子片段列表
   */
  public getSubClips(): AnimationSubClip[] {
    return [...this.subClips];
  }

  /**
   * 清空子片段列表
   */
  public clearSubClips(): void {
    this.subClips = [];
  }

  /**
   * 添加事件监听器
   * @param event 事件类型
   * @param listener 监听器
   */
  public addEventListener(event: SubClipEventType, listener: (data: any) => void): void {
    this.eventEmitter.on(event, listener);
  }

  /**
   * 移除事件监听器
   * @param event 事件类型
   * @param listener 监听器
   */
  public removeEventListener(event: SubClipEventType, listener: (data: any) => void): void {
    this.eventEmitter.off(event, listener);
  }

  /**
   * 混合子片段
   * @param weight 权重
   * @returns 混合后的子片段
   */
  public blendSubClips(weight: number = 0.5): THREE.AnimationClip | null {
    if (this.subClips.length === 0 || !this.subClip) return this.subClip;

    // 获取第一个子片段
    const firstSubClip = this.subClips[0].getSubClip();
    if (!firstSubClip) return this.subClip;

    // 创建混合后的子片段
    const blendedClip = THREE.AnimationUtils.makeClipAdditive(this.subClip);
    const additiveClip = THREE.AnimationUtils.makeClipAdditive(firstSubClip);

    // 混合轨道
    for (let i = 0; i < blendedClip.tracks.length; i++) {
      const track = blendedClip.tracks[i];
      const additiveTrack = additiveClip.tracks.find(t => t.name === track.name);

      if (additiveTrack) {
        // 混合轨道值
        if (track instanceof THREE.VectorKeyframeTrack) {
          const values = track.values;
          const additiveValues = (additiveTrack as THREE.VectorKeyframeTrack).values;

          for (let j = 0; j < values.length; j++) {
            values[j] = values[j] * (1 - weight) + additiveValues[j] * weight;
          }
        }
        else if (track instanceof THREE.QuaternionKeyframeTrack) {
          const values = track.values;
          const additiveValues = (additiveTrack as THREE.QuaternionKeyframeTrack).values;

          for (let j = 0; j < values.length; j += 4) {
            const q1 = new THREE.Quaternion(values[j], values[j + 1], values[j + 2], values[j + 3]);
            const q2 = new THREE.Quaternion(additiveValues[j], additiveValues[j + 1], additiveValues[j + 2], additiveValues[j + 3]);
            q1.slerp(q2, weight);

            values[j] = q1.x;
            values[j + 1] = q1.y;
            values[j + 2] = q1.z;
            values[j + 3] = q1.w;
          }
        }
      }
    }

    return blendedClip;
  }
}
