/**
 * 场景图层管理器
 * 管理场景中的所有图层
 */
import { Scene } from './Scene';
import { SceneLayer, SceneLayerOptions, SceneLayerType } from './SceneLayer';
import { EventEmitter } from '../utils/EventEmitter';
import { Entity } from '../core/Entity';

/**
 * 场景图层管理器选项
 */
export interface SceneLayerManagerOptions {
  /** 是否自动创建默认图层 */
  createDefaultLayers?: boolean;
  /** 默认图层数量 */
  defaultLayerCount?: number;
}

/**
 * 场景图层查询选项
 */
export interface SceneLayerQueryOptions {
  /** 是否包含不可见图层 */
  includeInvisible?: boolean;
  /** 是否包含锁定图层 */
  includeLocked?: boolean;
  /** 标签过滤器 */
  tagFilter?: string[];
  /** 名称过滤器（支持正则表达式） */
  nameFilter?: string | RegExp;
  /** 自定义过滤函数 */
  customFilter?: (layer: SceneLayer) => boolean;
}

/**
 * 场景图层管理器
 */
export class SceneLayerManager extends EventEmitter {
  /** 所属场景 */
  private scene: Scene;

  /** 图层映射 */
  private layers: Map<string, SceneLayer> = new Map();

  /** 默认图层 */
  private defaultLayer: SceneLayer | null = null;

  /** 是否已初始化 */
  private initialized: boolean = false;

  /**
   * 创建场景图层管理器
   * @param scene 所属场景
   * @param options 选项
   */
  constructor(scene: Scene, options: SceneLayerManagerOptions = {}) {
    super();

    this.scene = scene;

    // 是否自动创建默认图层
    const createDefaultLayers = options.createDefaultLayers !== undefined ? options.createDefaultLayers : true;

    if (createDefaultLayers) {
      this.createDefaultLayers(options.defaultLayerCount || 1);
    }
  }

  /**
   * 初始化图层管理器
   */
  public initialize(): void {
    if (this.initialized) {
      return;
    }

    // 初始化所有图层
    for (const layer of Array.from(this.layers.values())) {
      layer.initialize();
    }

    this.initialized = true;
    this.emit('initialized');
  }

  /**
   * 创建默认图层
   * @param count 默认图层数量
   */
  private createDefaultLayers(count: number): void {
    // 创建默认图层组
    const rootGroup = this.createLayer({
      id: 'root',
      name: '根图层组',
      type: SceneLayerType.GROUP,
      visible: true,
      locked: false,
      order: 0,
      expanded: true
    });

    // 创建默认图层
    this.defaultLayer = this.createLayer({
      id: 'default',
      name: '默认图层',
      visible: true,
      locked: false,
      order: 0,
      parentId: 'root'
    });

    // 创建额外的默认图层
    for (let i = 1; i < count; i++) {
      this.createLayer({
        id: `layer_${i}`,
        name: `图层 ${i}`,
        visible: true,
        locked: false,
        order: i,
        parentId: 'root'
      });
    }
  }

  /**
   * 创建图层
   * @param options 图层选项
   * @returns 创建的图层
   */
  public createLayer(options: SceneLayerOptions): SceneLayer {
    // 检查图层ID是否已存在
    if (this.layers.has(options.id)) {
      throw new Error(`图层ID已存在: ${options.id}`);
    }

    // 创建图层
    const layer = new SceneLayer(this.scene, options);

    // 添加到图层映射
    this.layers.set(options.id, layer);

    // 处理父子关系
    if (options.parentId) {
      const parentLayer = this.getLayer(options.parentId);

      if (parentLayer) {
        // 如果父图层不是组，则将其转换为组
        if (!parentLayer.isGroup()) {
          parentLayer.setType(SceneLayerType.GROUP);
        }

        // 添加到父图层的子图层列表
        parentLayer.addChild(options.id);
      } else {
        console.warn(`父图层不存在: ${options.parentId}`);
      }
    }

    // 监听图层事件
    this.setupLayerEvents(layer);

    // 发出图层创建事件
    this.emit('layerCreated', layer);

    return layer;
  }

  /**
   * 设置图层事件监听
   * @param layer 图层
   */
  private setupLayerEvents(layer: SceneLayer): void {
    // 监听图层可见性变化事件
    layer.on('visibilityChanged', (visible: boolean) => {
      this.emit('layerVisibilityChanged', layer, visible);

      // 如果是图层组，同步更新所有子图层的可见性
      if (layer.isGroup()) {
        this.updateChildrenVisibility(layer.id, visible);
      }
    });

    // 监听图层锁定状态变化事件
    layer.on('lockChanged', (locked: boolean) => {
      this.emit('layerLockChanged', layer, locked);

      // 如果是图层组，同步更新所有子图层的锁定状态
      if (layer.isGroup()) {
        this.updateChildrenLock(layer.id, locked);
      }
    });

    // 监听图层顺序变化事件
    layer.on('orderChanged', (order: number) => {
      this.emit('layerOrderChanged', layer, order);
    });

    // 监听图层实体添加事件
    layer.on('entityAdded', (entity: Entity) => {
      this.emit('layerEntityAdded', layer, entity);
    });

    // 监听图层实体移除事件
    layer.on('entityRemoved', (entity: Entity) => {
      this.emit('layerEntityRemoved', layer, entity);
    });

    // 监听图层类型变化事件
    layer.on('typeChanged', (type: SceneLayerType) => {
      this.emit('layerTypeChanged', layer, type);
    });

    // 监听图层父图层变化事件
    layer.on('parentChanged', (parentId: string | null, oldParentId: string | null) => {
      // 从旧父图层中移除
      if (oldParentId) {
        const oldParent = this.getLayer(oldParentId);
        if (oldParent) {
          oldParent.removeChild(layer.id);
        }
      }

      // 添加到新父图层
      if (parentId) {
        const newParent = this.getLayer(parentId);
        if (newParent) {
          // 如果新父图层不是组，则将其转换为组
          if (!newParent.isGroup()) {
            newParent.setType(SceneLayerType.GROUP);
          }

          newParent.addChild(layer.id);
        }
      }

      this.emit('layerParentChanged', layer, parentId, oldParentId);
    });

    // 监听子图层添加事件
    layer.on('childAdded', (childId: string) => {
      this.emit('layerChildAdded', layer, childId);
    });

    // 监听子图层移除事件
    layer.on('childRemoved', (childId: string) => {
      this.emit('layerChildRemoved', layer, childId);
    });

    // 监听展开状态变化事件
    layer.on('expandedChanged', (expanded: boolean) => {
      this.emit('layerExpandedChanged', layer, expanded);
    });
  }

  /**
   * 获取图层
   * @param id 图层ID
   * @returns 图层实例
   */
  public getLayer(id: string): SceneLayer | null {
    return this.layers.get(id) || null;
  }

  /**
   * 获取默认图层
   * @returns 默认图层
   */
  public getDefaultLayer(): SceneLayer | null {
    return this.defaultLayer;
  }

  /**
   * 获取所有图层
   * @returns 图层数组
   */
  public getLayers(): SceneLayer[] {
    return Array.from(this.layers.values());
  }

  /**
   * 获取根图层
   * @returns 根图层数组（没有父图层的图层）
   */
  public getRootLayers(): SceneLayer[] {
    return Array.from(this.layers.values()).filter(layer => !layer.getParentId());
  }

  /**
   * 获取图层的子图层
   * @param layerId 图层ID
   * @returns 子图层数组
   */
  public getChildLayers(layerId: string): SceneLayer[] {
    const layer = this.getLayer(layerId);

    if (!layer) {
      return [];
    }

    const childrenIds = layer.getChildrenIds();
    const children: SceneLayer[] = [];

    for (const childId of childrenIds) {
      const childLayer = this.getLayer(childId);

      if (childLayer) {
        children.push(childLayer);
      }
    }

    return children;
  }

  /**
   * 更新子图层的可见性
   * @param layerId 图层ID
   * @param visible 是否可见
   */
  private updateChildrenVisibility(layerId: string, visible: boolean): void {
    const children = this.getChildLayers(layerId);

    for (const child of children) {
      child.setVisible(visible);

      // 递归更新子图层
      if (child.isGroup()) {
        this.updateChildrenVisibility(child.id, visible);
      }
    }
  }

  /**
   * 更新子图层的锁定状态
   * @param layerId 图层ID
   * @param locked 是否锁定
   */
  private updateChildrenLock(layerId: string, locked: boolean): void {
    const children = this.getChildLayers(layerId);

    for (const child of children) {
      child.setLocked(locked);

      // 递归更新子图层
      if (child.isGroup()) {
        this.updateChildrenLock(child.id, locked);
      }
    }
  }

  /**
   * 创建图层组
   * @param name 组名称
   * @param parentId 父图层ID
   * @returns 创建的图层组
   */
  public createLayerGroup(name: string, parentId?: string): SceneLayer {
    return this.createLayer({
      id: `group_${Date.now()}`,
      name,
      type: SceneLayerType.GROUP,
      visible: true,
      locked: false,
      order: this.layers.size,
      parentId,
      expanded: true
    });
  }

  /**
   * 获取图层数量
   * @returns 图层数量
   */
  public getLayerCount(): number {
    return this.layers.size;
  }

  /**
   * 查询图层
   * @param options 查询选项
   * @returns 匹配的图层数组
   */
  public queryLayers(options: SceneLayerQueryOptions = {}): SceneLayer[] {
    // 合并选项
    const mergedOptions: SceneLayerQueryOptions = {
      includeInvisible: options.includeInvisible !== undefined ? options.includeInvisible : false,
      includeLocked: options.includeLocked !== undefined ? options.includeLocked : true,
      tagFilter: options.tagFilter || [],
      nameFilter: options.nameFilter,
      customFilter: options.customFilter
    };

    // 过滤图层
    return Array.from(this.layers.values()).filter(layer => {
      // 检查可见性
      if (!mergedOptions.includeInvisible && !layer.isVisible()) {
        return false;
      }

      // 检查锁定状态
      if (!mergedOptions.includeLocked && layer.isLocked()) {
        return false;
      }

      // 检查标签过滤器
      if (mergedOptions.tagFilter && mergedOptions.tagFilter.length > 0) {
        const hasAnyTag = mergedOptions.tagFilter.some(tag => layer.hasTag(tag));

        if (!hasAnyTag) {
          return false;
        }
      }

      // 检查名称过滤器
      if (mergedOptions.nameFilter) {
        if (typeof mergedOptions.nameFilter === 'string') {
          if (!layer.name.includes(mergedOptions.nameFilter)) {
            return false;
          }
        } else if (mergedOptions.nameFilter instanceof RegExp) {
          if (!mergedOptions.nameFilter.test(layer.name)) {
            return false;
          }
        }
      }

      // 检查自定义过滤器
      if (mergedOptions.customFilter && !mergedOptions.customFilter(layer)) {
        return false;
      }

      return true;
    });
  }

  /**
   * 添加实体到图层
   * @param entity 实体
   * @param layerId 图层ID
   * @returns 是否成功添加
   */
  public addEntityToLayer(entity: Entity, layerId: string): boolean {
    const layer = this.getLayer(layerId);

    if (!layer) {
      return false;
    }

    return layer.addEntity(entity);
  }

  /**
   * 从图层中移除实体
   * @param entity 实体
   * @param layerId 图层ID
   * @returns 是否成功移除
   */
  public removeEntityFromLayer(entity: Entity, layerId: string): boolean {
    const layer = this.getLayer(layerId);

    if (!layer) {
      return false;
    }

    return layer.removeEntity(entity);
  }

  /**
   * 移除图层
   * @param id 图层ID
   * @param removeChildren 是否同时移除子图层
   * @returns 是否成功移除
   */
  public removeLayer(id: string, removeChildren: boolean = true): boolean {
    // 不能移除默认图层和根图层组
    if ((this.defaultLayer && id === this.defaultLayer.id) || id === 'root') {
      return false;
    }

    const layer = this.getLayer(id);

    if (!layer) {
      return false;
    }

    // 处理子图层
    if (layer.isGroup() && layer.hasChildren()) {
      const childrenIds = [...layer.getChildrenIds()]; // 创建副本，因为在循环中会修改集合

      if (removeChildren) {
        // 递归移除所有子图层
        for (const childId of childrenIds) {
          this.removeLayer(childId, true);
        }
      } else {
        // 将子图层移动到父图层
        const parentId = layer.getParentId();

        for (const childId of childrenIds) {
          const childLayer = this.getLayer(childId);

          if (childLayer) {
            childLayer.setParentId(parentId);
          }
        }
      }
    }

    // 从父图层中移除
    const parentId = layer.getParentId();

    if (parentId) {
      const parentLayer = this.getLayer(parentId);

      if (parentLayer) {
        parentLayer.removeChild(id);
      }
    }

    // 销毁图层
    layer.dispose();

    // 从图层映射中移除
    this.layers.delete(id);

    // 发出图层移除事件
    this.emit('layerRemoved', id);

    return true;
  }

  /**
   * 清空所有图层
   */
  public clearLayers(): void {
    // 保存默认图层
    const defaultLayer = this.defaultLayer;

    // 清空前发出事件
    this.emit('beforeClearLayers');

    // 销毁所有图层
    for (const layer of Array.from(this.layers.values())) {
      layer.dispose();
    }

    // 清空图层映射
    this.layers.clear();

    // 恢复默认图层
    if (defaultLayer) {
      this.layers.set(defaultLayer.id, defaultLayer);
      this.defaultLayer = defaultLayer;
    }

    // 发出清空事件
    this.emit('layersCleared');
  }

  /**
   * 销毁图层管理器
   */
  public dispose(): void {
    // 清空所有图层
    this.clearLayers();

    // 移除所有事件监听器
    this.removeAllListeners();

    this.initialized = false;
  }
}
