<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="800" height="500" xmlns="http://www.w3.org/2000/svg">
  <style>
    .editor-bg {
      fill: #f0f0f0;
      stroke: #ddd;
      stroke-width: 1;
    }
    .panel {
      fill: #ffffff;
      stroke: #ddd;
      stroke-width: 1;
      rx: 5;
      ry: 5;
    }
    .header {
      fill: #e0e0e0;
      stroke: #ddd;
      stroke-width: 1;
      rx: 5 5 0 0;
      ry: 5 5 0 0;
    }
    .title {
      font-family: 'Arial', sans-serif;
      font-size: 16px;
      font-weight: bold;
      fill: #333;
    }
    .label {
      font-family: 'Arial', sans-serif;
      font-size: 14px;
      fill: #333;
    }
    .button {
      fill: #4a90e2;
      stroke: #3a80d2;
      stroke-width: 1;
      rx: 4;
      ry: 4;
    }
    .button-text {
      font-family: 'Arial', sans-serif;
      font-size: 14px;
      fill: white;
      text-anchor: middle;
      dominant-baseline: middle;
    }
    .tutorial-panel {
      fill: rgba(255, 255, 255, 0.95);
      stroke: #4a90e2;
      stroke-width: 2;
      rx: 8;
      ry: 8;
      filter: drop-shadow(0px 4px 6px rgba(0, 0, 0, 0.1));
    }
    .tutorial-header {
      fill: #4a90e2;
      stroke: none;
      rx: 8 8 0 0;
      ry: 8 8 0 0;
    }
    .tutorial-title {
      font-family: 'Arial', sans-serif;
      font-size: 18px;
      font-weight: bold;
      fill: white;
    }
    .tutorial-text {
      font-family: 'Arial', sans-serif;
      font-size: 14px;
      fill: #333;
    }
    .tutorial-step {
      font-family: 'Arial', sans-serif;
      font-size: 12px;
      fill: #666;
    }
    .highlight {
      fill: none;
      stroke: #ff9800;
      stroke-width: 3;
      stroke-dasharray: 5, 5;
      rx: 4;
      ry: 4;
      filter: drop-shadow(0px 0px 8px rgba(255, 152, 0, 0.5));
    }
    .scene-view {
      fill: #2a2a2a;
    }
    .grid {
      stroke: #444;
      stroke-width: 1;
    }
    .hierarchy-item {
      font-family: 'Arial', sans-serif;
      font-size: 14px;
      fill: #333;
    }
    .inspector-row {
      fill: #f9f9f9;
    }
    .inspector-label {
      font-family: 'Arial', sans-serif;
      font-size: 12px;
      fill: #666;
    }
    .inspector-value {
      font-family: 'Arial', sans-serif;
      font-size: 12px;
      fill: #333;
    }
  </style>
  
  <!-- Editor Background -->
  <rect x="0" y="0" width="800" height="500" class="editor-bg" />
  
  <!-- Scene View -->
  <rect x="200" y="50" width="400" height="300" class="panel" />
  <rect x="200" y="50" width="400" height="30" class="header" />
  <text x="220" y="70" class="title">场景视图</text>
  <rect x="200" y="80" width="400" height="270" class="scene-view" />
  
  <!-- Grid in Scene View -->
  <line x1="200" y1="215" x2="600" y2="215" class="grid" />
  <line x1="400" y1="80" x2="400" y2="350" class="grid" />
  
  <!-- Simple 3D Cube in Scene View -->
  <polygon points="350,180 450,180 470,200 370,200" fill="#6a9eda" stroke="#333" />
  <polygon points="450,180 470,200 470,250 450,230" fill="#5d8bc3" stroke="#333" />
  <polygon points="370,200 470,200 470,250 370,250" fill="#4a7ab8" stroke="#333" />
  
  <!-- Hierarchy Panel -->
  <rect x="50" y="50" width="140" height="300" class="panel" />
  <rect x="50" y="50" width="140" height="30" class="header" />
  <text x="70" y="70" class="title">层级面板</text>
  
  <text x="60" y="100" class="hierarchy-item">► 场景</text>
  <text x="70" y="120" class="hierarchy-item">► 主摄像机</text>
  <text x="70" y="140" class="hierarchy-item">► 方向光</text>
  <text x="70" y="160" class="hierarchy-item" font-weight="bold">■ 立方体</text>
  <text x="70" y="180" class="hierarchy-item">► 地面</text>
  
  <!-- Inspector Panel -->
  <rect x="610" y="50" width="140" height="300" class="panel" />
  <rect x="610" y="50" width="140" height="30" class="header" />
  <text x="630" y="70" class="title">属性面板</text>
  
  <text x="620" y="100" class="label">立方体</text>
  
  <rect x="610" y="110" width="140" height="20" class="inspector-row" />
  <text x="620" y="125" class="inspector-label">位置</text>
  <text x="700" y="125" class="inspector-value">0, 0, 0</text>
  
  <rect x="610" y="130" width="140" height="20" class="inspector-row" />
  <text x="620" y="145" class="inspector-label">旋转</text>
  <text x="700" y="145" class="inspector-value">0, 0, 0</text>
  
  <rect x="610" y="150" width="140" height="20" class="inspector-row" />
  <text x="620" y="165" class="inspector-label">缩放</text>
  <text x="700" y="165" class="inspector-value">1, 1, 1</text>
  
  <!-- Project Panel -->
  <rect x="200" y="360" width="550" height="90" class="panel" />
  <rect x="200" y="360" width="550" height="30" class="header" />
  <text x="220" y="380" class="title">项目面板</text>
  
  <rect x="220" y="400" width="60" height="60" fill="#f0f0f0" stroke="#ddd" />
  <text x="250" y="440" class="label" text-anchor="middle">模型</text>
  
  <rect x="290" y="400" width="60" height="60" fill="#f0f0f0" stroke="#ddd" />
  <text x="320" y="440" class="label" text-anchor="middle">材质</text>
  
  <rect x="360" y="400" width="60" height="60" fill="#f0f0f0" stroke="#ddd" />
  <text x="390" y="440" class="label" text-anchor="middle">纹理</text>
  
  <rect x="430" y="400" width="60" height="60" fill="#f0f0f0" stroke="#ddd" />
  <text x="460" y="440" class="label" text-anchor="middle">脚本</text>
  
  <rect x="500" y="400" width="60" height="60" fill="#f0f0f0" stroke="#ddd" />
  <text x="530" y="440" class="label" text-anchor="middle">预制体</text>
  
  <rect x="570" y="400" width="60" height="60" fill="#f0f0f0" stroke="#ddd" />
  <text x="600" y="440" class="label" text-anchor="middle">场景</text>
  
  <!-- Console Panel -->
  <rect x="50" y="360" width="140" height="90" class="panel" />
  <rect x="50" y="360" width="140" height="30" class="header" />
  <text x="70" y="380" class="title">控制台</text>
  <text x="60" y="400" class="label" fill="#4a7ab8">信息: 场景已加载</text>
  
  <!-- Tutorial Panel -->
  <rect x="250" y="150" width="300" height="200" class="tutorial-panel" />
  <rect x="250" y="150" width="300" height="40" class="tutorial-header" />
  <text x="270" y="175" class="tutorial-title">编辑器基础教程</text>
  <text x="520" y="175" class="tutorial-step">步骤 1/5</text>
  
  <text x="270" y="210" class="tutorial-text">欢迎使用IR引擎编辑器！</text>
  <text x="270" y="235" class="tutorial-text">这是编辑器的主界面，由多个面板组成。</text>
  <text x="270" y="260" class="tutorial-text">左侧是层级面板，显示场景中的所有对象。</text>
  <text x="270" y="285" class="tutorial-text">中间是场景视图，用于编辑3D场景。</text>
  <text x="270" y="310" class="tutorial-text">右侧是属性面板，用于编辑选中对象的属性。</text>
  
  <rect x="270" y="330" width="80" height="30" class="button" />
  <text x="310" y="345" class="button-text">上一步</text>
  
  <rect x="360" y="330" width="80" height="30" class="button" />
  <text x="400" y="345" class="button-text">下一步</text>
  
  <rect x="450" y="330" width="80" height="30" class="button" />
  <text x="490" y="345" class="button-text">跳过</text>
  
  <!-- Highlight -->
  <rect x="47" y="47" width="146" height="306" class="highlight" />
</svg>
