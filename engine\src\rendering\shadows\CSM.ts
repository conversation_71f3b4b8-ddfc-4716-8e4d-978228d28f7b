/**
 * 级联阴影映射(CSM)实现
 * 基于Three.js实现的级联阴影映射系统
 */
import * as THREE from 'three';
import { Frustum } from './Frustum';
import { Shader } from './Shader';
import { EventEmitter } from '../../utils/EventEmitter';

// 原始着色器代码
const originalLightsFragmentBegin = THREE.ShaderChunk.lights_fragment_begin;
const originalLightsParsBegin = THREE.ShaderChunk.lights_pars_begin;

// 辅助矩阵
const _lightOrientationMatrix = new THREE.Matrix4();
const _lightOrientationMatrixInverse = new THREE.Matrix4();
const _cameraToLightMatrix = new THREE.Matrix4();
const _lightSpaceFrustum = new Frustum();
const _center = new THREE.Vector3();
const _bbox = new THREE.Box3();
const _uniformArray = [];
const _logArray = [];

/**
 * CSM模式枚举
 */
export const CSMModes = {
  UNIFORM: 'UNIFORM',      // 均匀分割
  LOGARITHMIC: 'LOGARITHMIC', // 对数分割
  PRACTICAL: 'PRACTICAL',    // 实用分割
  CUSTOM: 'CUSTOM'        // 自定义分割
};

/**
 * CSM参数接口
 */
export interface CSMParams {
  light?: THREE.DirectionalLight;
  cascades?: number;
  maxFar?: number;
  mode?: string;
  shadowMapSize?: number;
  shadowBias?: number;
  lightDirection?: THREE.Vector3;
  lightDirectionUp?: THREE.Vector3;
  lightIntensity?: number;
  lightColor?: THREE.ColorRepresentation;
  lightNear?: number;
  lightFar?: number;
  lightMargin?: number;
  customSplitsCallback?: (amount: number, near: number, far: number, target: number[]) => void;
  fade?: boolean;
}

/**
 * 级联阴影映射类
 */
export class CSM extends EventEmitter {
  cascades: number;
  maxFar: number;
  mode: string;
  shadowBias: number;
  shadowNormalBias: number;
  shadowMapSize: number;
  lightDirection: THREE.Vector3;
  lightDirectionUp: THREE.Vector3;
  lightColor: THREE.ColorRepresentation;
  lightIntensity: number;
  lightMargin: number;
  customSplitsCallback?: (amount: number, near: number, far: number, target: number[]) => void;
  fade: boolean;
  mainFrustum: Frustum;
  frustums: Frustum[];
  breaks: number[];
  sourceLight?: THREE.DirectionalLight;
  lights: THREE.DirectionalLight[];
  lightEntities: any[] = [];
  shaders: Map<THREE.Material, THREE.Shader> = new Map();
  materials: Set<THREE.Material> = new Set();
  needsUpdate = false;

  /**
   * 创建CSM实例
   * @param params CSM参数
   */
  constructor(params: CSMParams = {}) {
    super();

    this.cascades = params.cascades ?? 4;
    this.maxFar = params.maxFar ?? 100;
    this.mode = params.mode ?? CSMModes.PRACTICAL;
    this.shadowMapSize = params.shadowMapSize ?? 2048;
    this.shadowBias = params.shadowBias ?? -0.000001;
    this.shadowNormalBias = 0;
    this.lightDirection = params.lightDirection ?? new THREE.Vector3(1, -1, 1).normalize();
    this.lightDirectionUp = params.lightDirectionUp ?? THREE.Object3D.DEFAULT_UP.clone();
    this.lightColor = params.lightColor ?? 0xffffff;
    this.lightIntensity = params.lightIntensity ?? 1;
    this.lightMargin = params.lightMargin ?? 200;
    this.customSplitsCallback = params.customSplitsCallback;
    this.fade = params.fade ?? true;
    this.mainFrustum = new Frustum();
    this.frustums = [];
    this.breaks = [];
    this.lights = [];

    this.createLights(params.light);
    this.updateFrustums();
    this.injectInclude();
  }

  /**
   * 创建光源
   * @param sourceLight 源光源
   */
  createLights(sourceLight?: THREE.DirectionalLight): void {
    if (sourceLight) {
      this.sourceLight = sourceLight;
      this.shadowBias = sourceLight.shadow.bias;
      this.lightIntensity = sourceLight.intensity;
      this.lightColor = sourceLight.color.clone();

      for (let i = 0; i < this.cascades; i++) {
        const light = sourceLight.clone();
        this.createLight(light, i);
      }
      return;
    }

    // 如果没有提供光源，创建默认光源
    for (let i = 0; i < this.cascades; i++) {
      const light = new THREE.DirectionalLight(this.lightColor, this.lightIntensity);
      this.createLight(light, i);
    }
  }

  /**
   * 创建单个光源
   * @param light 光源
   * @param index 索引
   */
  createLight(light: THREE.DirectionalLight, index: number): void {
    light.castShadow = true;
    light.frustumCulled = false;

    light.shadow.mapSize.width = this.shadowMapSize;
    light.shadow.mapSize.height = this.shadowMapSize;

    light.shadow.camera.near = 0;
    light.shadow.camera.far = 1;

    light.intensity = this.lightIntensity;

    this.lights.push(light);
    light.name = 'CSM_' + light.name;
    light.target.name = 'CSM_' + light.target.name;

    this.emit('lightCreated', light, index);
  }

  /**
   * 更新
   */
  update(camera: THREE.Camera): void {
    if (this.sourceLight) {
      this.lightDirection.subVectors(this.sourceLight.target.position, this.sourceLight.position);
    }

    if (this.needsUpdate) {
      this.injectInclude();
      this.updateFrustums();
      for (const light of this.lights) {
        if (light.shadow.map) {
          light.shadow.map.dispose();
          light.shadow.map = null as any;
        }
        light.shadow.camera.updateProjectionMatrix();
        light.shadow.needsUpdate = true;
      }
      this.needsUpdate = false;
    }

    // 更新光源位置和方向
    this.updateLights(camera);
  }

  /**
   * 更新光源位置和方向
   */
  updateLights(camera: THREE.Camera): void {
    const frustums = this.frustums;
    for (let i = 0; i < frustums.length; i++) {
      const light = this.lights[i];
      const frustum = frustums[i];
      const shadowCam = light.shadow.camera;

      // 计算光源位置和方向
      _lightOrientationMatrix.lookAt(
        new THREE.Vector3(0, 0, 0),
        this.lightDirection,
        this.lightDirectionUp
      );
      _lightOrientationMatrixInverse.copy(_lightOrientationMatrix).invert();

      // 计算相机到光源的变换矩阵
      _cameraToLightMatrix.multiplyMatrices(
        _lightOrientationMatrixInverse,
        camera.matrixWorldInverse
      );

      // 变换视锥体到光源空间
      _lightSpaceFrustum.copy(frustum);
      _lightSpaceFrustum.applyMatrix4(_cameraToLightMatrix);

      // 获取视锥体包围盒
      const lightSpaceBbox = _lightSpaceFrustum.getBoundingBox(_bbox);
      lightSpaceBbox.getCenter(_center);
      lightSpaceBbox.getSize(_center);

      // 设置光源位置和目标
      const position = light.position.copy(_center);
      position.x += this.lightDirection.x * this.lightMargin;
      position.y += this.lightDirection.y * this.lightMargin;
      position.z += this.lightDirection.z * this.lightMargin;
      light.target.position.copy(_center);

      // 更新光源矩阵
      light.updateMatrixWorld();
      light.target.updateMatrixWorld();

      // 设置阴影相机参数
      shadowCam.updateMatrixWorld();
      shadowCam.matrixWorldInverse.copy(shadowCam.matrixWorld).invert();
    }
  }

  /**
   * 注入着色器代码
   */
  injectInclude(): void {
    THREE.ShaderChunk.lights_fragment_begin = Shader.lights_fragment_begin(this.cascades);
    THREE.ShaderChunk.lights_pars_begin = Shader.lights_pars_begin();
  }

  /**
   * 移除着色器代码
   */
  removeInclude(): void {
    THREE.ShaderChunk.lights_fragment_begin = originalLightsFragmentBegin;
    THREE.ShaderChunk.lights_pars_begin = originalLightsParsBegin;
  }

  /**
   * 设置材质
   * @param mesh 网格
   */
  setupMaterial(mesh: THREE.Mesh): void {
    const material = mesh.material as THREE.Material;
    if (!material.userData) material.userData = {};

    const materials = this.materials;
    if (material.userData.IGNORE_CSM) return;
    if (materials.has(material)) return;

    materials.add(material);
    material.defines = material.defines || {};
    material.defines.USE_CSM = 1;
    material.defines.CSM_CASCADES = this.cascades;

    if (this.fade) material.defines.CSM_FADE = '';

    material.needsUpdate = true;
  }

  /**
   * 清理材质
   * @param material 材质
   */
  teardownMaterial(material: THREE.Material): void {
    if (!material?.isMaterial) return;
    if (!material.userData) material.userData = {};

    if (material.defines) {
      delete material.defines.USE_CSM;
      delete material.defines.CSM_CASCADES;
      delete material.defines.CSM_FADE;
    }

    material.needsUpdate = true;
    this.shaders.delete(material);
    this.materials.delete(material);
  }

  /**
   * 计算分割点
   */
  getBreaks(): void {
    const amount = this.cascades;
    const near = 0.1;
    const far = this.maxFar;

    this.breaks.length = 0;

    switch (this.mode) {
      case CSMModes.UNIFORM:
        this.getUniformBreaks(amount, near, far);
        break;
      case CSMModes.LOGARITHMIC:
        this.getLogarithmicBreaks(amount, near, far);
        break;
      case CSMModes.PRACTICAL:
        this.getPracticalBreaks(amount, near, far);
        break;
      case CSMModes.CUSTOM:
        if (this.customSplitsCallback) {
          this.customSplitsCallback(amount, near, far, this.breaks);
        }
        break;
    }
  }

  /**
   * 获取均匀分割点
   */
  private getUniformBreaks(amount: number, near: number, far: number): void {
    for (let i = 1; i <= amount; i++) {
      this.breaks.push((near + (far - near) * i / amount));
    }
  }

  /**
   * 获取对数分割点
   */
  private getLogarithmicBreaks(amount: number, near: number, far: number): void {
    for (let i = 1; i <= amount; i++) {
      this.breaks.push(near * Math.pow(far / near, i / amount));
    }
  }

  /**
   * 获取实用分割点
   */
  private getPracticalBreaks(amount: number, near: number, far: number): void {
    const lambda = 0.5;
    for (let i = 1; i <= amount; i++) {
      const log = near * Math.pow(far / near, i / amount);
      const uniform = near + (far - near) * i / amount;
      this.breaks.push(lambda * log + (1 - lambda) * uniform);
    }
  }

  /**
   * 初始化级联
   */
  initCascades(): void {
    this.frustums.length = 0;

    for (let i = 0; i < this.cascades; i++) {
      const frustum = new Frustum();
      this.frustums.push(frustum);
    }
  }

  /**
   * 更新阴影边界
   */
  updateShadowBounds(): void {
    // 更新主视锥体
    this.mainFrustum.setFromProjectionMatrix(
      new THREE.Matrix4().makeOrthographic(-1, 1, 1, -1, 0.1, this.maxFar),
      this.maxFar
    );

    // 更新各级联的视锥体
    for (let i = 0; i < this.cascades; i++) {
      const near = i === 0 ? 0.1 : this.breaks[i - 1];
      const far = this.breaks[i];

      this.frustums[i].setFromProjectionMatrix(
        new THREE.Matrix4().makeOrthographic(-1, 1, 1, -1, near, far),
        far
      );
    }
  }

  /**
   * 更改光源
   */
  changeLights(light: THREE.DirectionalLight): void {
    this.sourceLight = light;
    this.lightDirection.subVectors(light.target.position, light.position).normalize();
    this.lightIntensity = light.intensity;
    this.lightColor = light.color.clone();
    this.shadowBias = light.shadow.bias;

    // 更新所有级联光源
    for (let i = 0; i < this.lights.length; i++) {
      const cascadeLight = this.lights[i];
      cascadeLight.intensity = this.lightIntensity;
      cascadeLight.color.copy(light.color);
      cascadeLight.shadow.bias = this.shadowBias;
    }

    this.needsUpdate = true;
  }

  /**
   * 更新视锥体
   */
  updateFrustums(): void {
    this.getBreaks();
    this.initCascades();
    this.updateShadowBounds();
  }

  /**
   * 销毁
   */
  dispose(): void {
    this.materials.forEach((material: THREE.Material) => {
      this.teardownMaterial(material);
    });

    this.materials.clear();
    this.shaders.clear();

    for (const light of this.lights) {
      if (light.shadow.map) {
        light.shadow.map.dispose();
      }
      light.dispose();
    }

    this.lights = [];
    this.removeInclude();
  }
}
