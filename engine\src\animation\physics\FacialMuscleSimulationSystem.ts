/**
 * 面部肌肉模拟系统
 * 管理面部肌肉模拟组件
 */
import * as THREE from 'three';
import * as CANNON from 'cannon-es';
import { Entity } from '../../core/Entity';
import { System } from '../../core/System';
import { World } from '../../core/World';
import { EventEmitter } from '../../utils/EventEmitter';
import { CannonPhysicsEngine } from './CannonPhysicsEngine';
import { FacialMuscleSimulationComponent, FacialMuscleSimulationConfig, MuscleConfig, MuscleType } from './FacialMuscleSimulation';
import { FacialExpressionType } from '../FacialAnimation';

/**
 * 面部肌肉模拟系统配置
 */
export interface FacialMuscleSimulationSystemConfig {
  /** 是否启用调试 */
  debug?: boolean;
  /** 重力 */
  gravity?: THREE.Vector3;
  /** 迭代次数 */
  iterations?: number;
  /** 是否使用软体 */
  useSoftBodies?: boolean;
}

/**
 * 面部肌肉模拟系统
 */
export class FacialMuscleSimulationSystem extends System {
  /** 系统类型 */
  static readonly type = 'FacialMuscleSimulation';

  /** 面部肌肉模拟组件 */
  private components: Map<Entity, FacialMuscleSimulationComponent> = new Map();
  
  /** 配置 */
  private config: FacialMuscleSimulationSystemConfig;
  
  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: FacialMuscleSimulationSystemConfig = {
    debug: false,
    gravity: new THREE.Vector3(0, -9.81, 0),
    iterations: 10,
    useSoftBodies: false
  };

  /** 物理引擎 */
  private physicsEngine: CannonPhysicsEngine;
  
  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /**
   * 构造函数
   * @param world 世界
   * @param config 配置
   */
  constructor(world: World, config?: Partial<FacialMuscleSimulationSystemConfig>) {
    super(world);
    this.config = { ...FacialMuscleSimulationSystem.DEFAULT_CONFIG, ...config };
    
    // 创建物理引擎
    this.physicsEngine = new CannonPhysicsEngine({
      gravity: new CANNON.Vec3(
        this.config.gravity?.x || 0,
        this.config.gravity?.y || -9.81,
        this.config.gravity?.z || 0
      ),
      iterations: this.config.iterations,
      debug: this.config.debug
    });
  }

  /**
   * 创建面部肌肉模拟组件
   * @param entity 实体
   * @param config 配置
   * @returns 面部肌肉模拟组件
   */
  public createFacialMuscleSimulation(entity: Entity, config?: Partial<FacialMuscleSimulationConfig>): FacialMuscleSimulationComponent {
    // 检查是否已存在组件
    if (this.components.has(entity)) {
      return this.components.get(entity)!;
    }

    // 合并配置
    const mergedConfig: FacialMuscleSimulationConfig = {
      debug: config?.debug !== undefined ? config.debug : this.config.debug,
      physicsEngine: this.physicsEngine,
      gravity: config?.gravity || this.config.gravity,
      iterations: config?.iterations || this.config.iterations,
      useSoftBodies: config?.useSoftBodies !== undefined ? config.useSoftBodies : this.config.useSoftBodies
    };

    // 创建新组件
    const component = new FacialMuscleSimulationComponent(entity, this.physicsEngine, mergedConfig);
    this.components.set(entity, component);

    if (this.config.debug) {
      console.log(`创建面部肌肉模拟组件: ${entity.id}`);
    }

    return component;
  }

  /**
   * 移除面部肌肉模拟组件
   * @param entity 实体
   */
  public removeFacialMuscleSimulation(entity: Entity): void {
    if (this.components.has(entity)) {
      this.components.delete(entity);

      if (this.config.debug) {
        console.log(`移除面部肌肉模拟组件: ${entity.id}`);
      }
    }
  }

  /**
   * 获取面部肌肉模拟组件
   * @param entity 实体
   * @returns 面部肌肉模拟组件，如果不存在则返回null
   */
  public getFacialMuscleSimulation(entity: Entity): FacialMuscleSimulationComponent | null {
    return this.components.get(entity) || null;
  }

  /**
   * 创建默认肌肉配置
   * @param entity 实体
   * @returns 是否成功创建
   */
  public createDefaultMuscles(entity: Entity): boolean {
    const component = this.getFacialMuscleSimulation(entity);
    if (!component) return false;
    
    // 创建默认肌肉
    
    // 眉毛
    component.addMuscle({
      type: MuscleType.EYEBROW,
      name: 'eyebrow_left',
      start: new THREE.Vector3(-0.05, 0.1, 0.1),
      end: new THREE.Vector3(-0.1, 0.1, 0.1),
      mass: 0.01,
      radius: 0.005,
      stiffness: 100,
      damping: 0.5,
      fixedStart: true,
      fixedEnd: false
    });
    
    component.addMuscle({
      type: MuscleType.EYEBROW,
      name: 'eyebrow_right',
      start: new THREE.Vector3(0.05, 0.1, 0.1),
      end: new THREE.Vector3(0.1, 0.1, 0.1),
      mass: 0.01,
      radius: 0.005,
      stiffness: 100,
      damping: 0.5,
      fixedStart: true,
      fixedEnd: false
    });
    
    component.addMuscle({
      type: MuscleType.EYEBROW,
      name: 'eyebrow_inner_left',
      start: new THREE.Vector3(-0.02, 0.1, 0.1),
      end: new THREE.Vector3(-0.05, 0.1, 0.1),
      mass: 0.01,
      radius: 0.005,
      stiffness: 100,
      damping: 0.5,
      fixedStart: true,
      fixedEnd: false
    });
    
    component.addMuscle({
      type: MuscleType.EYEBROW,
      name: 'eyebrow_inner_right',
      start: new THREE.Vector3(0.02, 0.1, 0.1),
      end: new THREE.Vector3(0.05, 0.1, 0.1),
      mass: 0.01,
      radius: 0.005,
      stiffness: 100,
      damping: 0.5,
      fixedStart: true,
      fixedEnd: false
    });
    
    // 眼睑
    component.addMuscle({
      type: MuscleType.EYELID,
      name: 'eyelid_upper_left',
      start: new THREE.Vector3(-0.05, 0.08, 0.1),
      end: new THREE.Vector3(-0.05, 0.09, 0.1),
      mass: 0.005,
      radius: 0.003,
      stiffness: 200,
      damping: 0.7,
      fixedStart: true,
      fixedEnd: false
    });
    
    component.addMuscle({
      type: MuscleType.EYELID,
      name: 'eyelid_upper_right',
      start: new THREE.Vector3(0.05, 0.08, 0.1),
      end: new THREE.Vector3(0.05, 0.09, 0.1),
      mass: 0.005,
      radius: 0.003,
      stiffness: 200,
      damping: 0.7,
      fixedStart: true,
      fixedEnd: false
    });
    
    // 眼角
    component.addMuscle({
      type: MuscleType.EYE_CORNER,
      name: 'eye_corner_left',
      start: new THREE.Vector3(-0.08, 0.07, 0.1),
      end: new THREE.Vector3(-0.1, 0.07, 0.1),
      mass: 0.005,
      radius: 0.003,
      stiffness: 150,
      damping: 0.6,
      fixedStart: true,
      fixedEnd: false
    });
    
    component.addMuscle({
      type: MuscleType.EYE_CORNER,
      name: 'eye_corner_right',
      start: new THREE.Vector3(0.08, 0.07, 0.1),
      end: new THREE.Vector3(0.1, 0.07, 0.1),
      mass: 0.005,
      radius: 0.003,
      stiffness: 150,
      damping: 0.6,
      fixedStart: true,
      fixedEnd: false
    });
    
    // 鼻子
    component.addMuscle({
      type: MuscleType.NOSE,
      name: 'nose',
      start: new THREE.Vector3(0, 0.05, 0.12),
      end: new THREE.Vector3(0, 0.03, 0.15),
      mass: 0.01,
      radius: 0.005,
      stiffness: 300,
      damping: 0.8,
      fixedStart: true,
      fixedEnd: false
    });
    
    // 嘴唇
    component.addMuscle({
      type: MuscleType.LIP,
      name: 'lip_upper',
      start: new THREE.Vector3(0, 0.02, 0.12),
      end: new THREE.Vector3(0, 0.01, 0.13),
      mass: 0.008,
      radius: 0.004,
      stiffness: 120,
      damping: 0.5,
      fixedStart: true,
      fixedEnd: false
    });
    
    component.addMuscle({
      type: MuscleType.LIP,
      name: 'lip_lower',
      start: new THREE.Vector3(0, -0.01, 0.12),
      end: new THREE.Vector3(0, -0.02, 0.13),
      mass: 0.008,
      radius: 0.004,
      stiffness: 120,
      damping: 0.5,
      fixedStart: true,
      fixedEnd: false
    });
    
    // 嘴角
    component.addMuscle({
      type: MuscleType.MOUTH_CORNER,
      name: 'mouth_corner_left',
      start: new THREE.Vector3(-0.05, 0, 0.11),
      end: new THREE.Vector3(-0.07, 0, 0.11),
      mass: 0.01,
      radius: 0.005,
      stiffness: 100,
      damping: 0.5,
      fixedStart: true,
      fixedEnd: false
    });
    
    component.addMuscle({
      type: MuscleType.MOUTH_CORNER,
      name: 'mouth_corner_right',
      start: new THREE.Vector3(0.05, 0, 0.11),
      end: new THREE.Vector3(0.07, 0, 0.11),
      mass: 0.01,
      radius: 0.005,
      stiffness: 100,
      damping: 0.5,
      fixedStart: true,
      fixedEnd: false
    });
    
    // 下巴
    component.addMuscle({
      type: MuscleType.JAW,
      name: 'jaw',
      start: new THREE.Vector3(0, -0.03, 0.1),
      end: new THREE.Vector3(0, -0.05, 0.1),
      mass: 0.02,
      radius: 0.008,
      stiffness: 400,
      damping: 0.9,
      fixedStart: true,
      fixedEnd: false
    });
    
    // 脸颊
    component.addMuscle({
      type: MuscleType.CHEEK,
      name: 'cheek_left',
      start: new THREE.Vector3(-0.07, 0.03, 0.1),
      end: new THREE.Vector3(-0.09, 0.02, 0.1),
      mass: 0.01,
      radius: 0.006,
      stiffness: 80,
      damping: 0.4,
      fixedStart: true,
      fixedEnd: false
    });
    
    component.addMuscle({
      type: MuscleType.CHEEK,
      name: 'cheek_right',
      start: new THREE.Vector3(0.07, 0.03, 0.1),
      end: new THREE.Vector3(0.09, 0.02, 0.1),
      mass: 0.01,
      radius: 0.006,
      stiffness: 80,
      damping: 0.4,
      fixedStart: true,
      fixedEnd: false
    });
    
    // 初始化组件
    component.initialize();
    
    return true;
  }

  /**
   * 应用表情
   * @param entity 实体
   * @param expression 表情类型
   * @param weight 权重
   * @returns 是否成功应用
   */
  public applyExpression(entity: Entity, expression: FacialExpressionType, weight: number): boolean {
    const component = this.getFacialMuscleSimulation(entity);
    if (!component) return false;
    
    component.applyExpression(expression, weight);
    return true;
  }

  /**
   * 获取物理引擎
   * @returns 物理引擎
   */
  public getPhysicsEngine(): CannonPhysicsEngine {
    return this.physicsEngine;
  }

  /**
   * 添加事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  public addEventListener(event: string, callback: (data: any) => void): void {
    this.eventEmitter.on(event, callback);
  }

  /**
   * 移除事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  public removeEventListener(event: string, callback: (data: any) => void): void {
    this.eventEmitter.off(event, callback);
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    // 更新物理引擎
    this.physicsEngine.update(deltaTime);
    
    // 更新所有面部肌肉模拟组件
    for (const component of this.components.values()) {
      component.update(deltaTime);
    }
    
    // 发送更新事件
    this.eventEmitter.emit('update', { deltaTime });
  }
}
