# 创建示例项目指南

本文档详细介绍了如何创建高质量的IR引擎示例项目，包括项目结构、内容要求、资源准备和提交流程。

## 目录

- [示例项目概述](#示例项目概述)
- [项目结构](#项目结构)
- [内容要求](#内容要求)
- [资源准备](#资源准备)
- [代码规范](#代码规范)
- [文档编写](#文档编写)
- [测试和优化](#测试和优化)
- [提交流程](#提交流程)
- [最佳实践](#最佳实践)

## 示例项目概述

示例项目是展示IR引擎功能和用法的小型项目，旨在帮助用户快速了解和掌握引擎的各种功能。一个好的示例项目应该：

- 聚焦于特定功能或用例
- 代码清晰、注释充分
- 文档详细、易于理解
- 资源精简、质量高
- 性能良好、运行稳定

## 项目结构

每个示例项目应遵循以下标准结构：

```
/[示例项目名称]/
  ├── index.html                # 主入口文件
  ├── README.md                 # 项目说明文档
  ├── example.json              # 示例项目元数据
  ├── assets/                   # 项目特定资源
  │   ├── images/               # 图片资源
  │   ├── models/               # 3D模型
  │   ├── textures/             # 纹理
  │   └── scripts/              # 脚本
  ├── scenes/                   # 场景文件
  │   ├── main.json             # 主场景
  │   └── [其他场景].json       # 其他场景
  ├── scripts/                  # 项目脚本
  │   ├── main.js               # 主脚本
  │   └── [其他脚本].js         # 其他脚本
  └── styles/                   # 样式文件
      └── main.css              # 主样式文件
```

### example.json

`example.json` 文件包含示例项目的元数据，格式如下：

```json
{
  "title": "示例项目标题",
  "description": "示例项目描述",
  "category": "basic",
  "tags": ["标签1", "标签2", "标签3"],
  "version": "1.0.0",
  "author": "作者名称",
  "license": "MIT",
  "preview": "assets/images/preview.jpg",
  "difficulty": "beginner",
  "features": [
    {
      "title": "特性1",
      "description": "特性1描述"
    },
    {
      "title": "特性2",
      "description": "特性2描述"
    }
  ],
  "requirements": {
    "engineVersion": ">=1.0.0",
    "editorVersion": ">=1.0.0",
    "dependencies": []
  }
}
```

## 内容要求

### 功能演示

示例项目应该清晰地演示特定功能，包括：

1. **基本用法**：展示功能的基本用法和API
2. **常见场景**：展示功能在常见场景中的应用
3. **高级用法**：展示功能的高级用法和技巧
4. **最佳实践**：展示功能的最佳实践和优化方法

### 交互体验

示例项目应该提供良好的交互体验，包括：

1. **用户界面**：清晰、直观的用户界面
2. **操作反馈**：及时、明确的操作反馈
3. **错误处理**：友好、有用的错误提示
4. **帮助信息**：详细、易懂的帮助信息

### 学习曲线

示例项目应该考虑用户的学习曲线，包括：

1. **入门级**：简单、基础的示例，适合初学者
2. **中级**：稍复杂、进阶的示例，适合有一定基础的用户
3. **高级**：复杂、深入的示例，适合有经验的用户

## 资源准备

### 图片资源

- **预览图**：800x450像素，JPG或PNG格式，展示项目的主要内容
- **截图**：各种分辨率，PNG格式，展示项目的不同方面
- **图标**：32x32像素，PNG格式，用于项目列表显示

### 3D模型

- **格式**：优先使用glTF/GLB格式
- **复杂度**：控制模型的复杂度，避免过于复杂的模型
- **纹理**：优化纹理大小和格式，避免过大的纹理

### 纹理资源

- **格式**：根据用途选择合适的格式（JPG、PNG、KTX2等）
- **分辨率**：使用合适的分辨率，避免过高的分辨率
- **压缩**：使用纹理压缩技术减小文件大小

### 音频资源

- **格式**：优先使用MP3或OGG格式
- **质量**：根据用途选择合适的质量
- **大小**：控制音频文件的大小

## 代码规范

### 命名规范

- **文件名**：使用小写字母和连字符（例如：`example-name.js`）
- **变量名**：使用驼峰命名法（例如：`exampleVariable`）
- **类名**：使用帕斯卡命名法（例如：`ExampleClass`）
- **常量名**：使用大写字母和下划线（例如：`EXAMPLE_CONSTANT`）

### 注释规范

- **文件头注释**：包含文件名、描述、作者和日期
- **函数注释**：包含函数描述、参数和返回值
- **代码块注释**：解释复杂的代码逻辑
- **TODO注释**：标记待完成的任务

### 代码风格

- **缩进**：使用2个空格缩进
- **行长度**：控制在80-100个字符以内
- **空行**：使用空行分隔不同的代码块
- **括号**：使用一致的括号风格

## 文档编写

### README.md

README.md文件应包含以下内容：

1. **项目标题和简介**：简要介绍项目的目的和内容
2. **功能特性**：列出项目的主要功能和特性
3. **使用说明**：详细说明如何使用项目
4. **技术要点**：解释项目中使用的技术和方法
5. **学习要点**：指出用户可以从项目中学到的知识
6. **扩展建议**：提供项目的扩展和改进建议

### 代码注释

代码中应包含详细的注释，解释：

1. **功能实现**：如何实现特定功能
2. **API用法**：如何使用API
3. **参数说明**：参数的含义和用法
4. **注意事项**：使用时需要注意的问题

### 用户界面提示

用户界面中应包含适当的提示信息，帮助用户：

1. **操作指引**：如何操作界面元素
2. **功能说明**：界面元素的功能
3. **快捷键**：可用的快捷键
4. **错误提示**：可能出现的错误和解决方法

## 测试和优化

### 功能测试

- 测试所有功能是否正常工作
- 测试边缘情况和错误处理
- 测试不同参数和配置

### 性能优化

- 优化资源加载和使用
- 优化渲染性能
- 优化内存使用
- 优化代码执行效率

### 兼容性测试

- 测试不同浏览器的兼容性
- 测试不同设备的兼容性
- 测试不同分辨率的适配

## 提交流程

### 准备工作

1. 确保项目符合结构和内容要求
2. 确保所有资源都已优化
3. 确保文档完整且准确
4. 确保代码符合规范且注释充分

### 提交步骤

1. 将项目打包为ZIP文件
2. 登录IR引擎开发者平台
3. 进入示例项目提交页面
4. 填写项目信息并上传ZIP文件
5. 提交审核

### 审核流程

1. 管理员审核项目内容和质量
2. 如有问题，会通知修改
3. 审核通过后，项目将发布到示例项目库

## 最佳实践

### 聚焦单一功能

- 每个示例项目应该聚焦于单一功能或用例
- 避免在一个项目中包含过多不相关的功能

### 循序渐进

- 从简单的基础功能开始
- 逐步引入复杂的高级功能
- 提供清晰的学习路径

### 实用为主

- 示例项目应该实用，解决实际问题
- 避免过于抽象或理论的示例
- 提供可直接应用的解决方案

### 持续更新

- 根据引擎更新调整示例项目
- 根据用户反馈改进示例项目
- 添加新的功能和用例
