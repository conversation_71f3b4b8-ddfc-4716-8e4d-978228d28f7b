# 动画混合系统故障排除指南

本文档提供了IR引擎动画混合系统常见问题的故障排除步骤和解决方案，帮助开发者快速解决动画相关问题。

## 目录

1. [常见问题](#常见问题)
   - [动画不播放](#动画不播放)
   - [动画跳跃或抖动](#动画跳跃或抖动)
   - [动画穿插](#动画穿插)
   - [动画混合不正确](#动画混合不正确)
   - [性能问题](#性能问题)
2. [诊断工具](#诊断工具)
3. [常见错误代码](#常见错误代码)
4. [联系支持](#联系支持)

## 常见问题

### 动画不播放

**症状**：角色保持静止，动画没有播放。

**可能原因**：
- 动画片段未正确加载
- 动画控制器未初始化
- 混合器未更新
- 动画权重为0

**解决方案**：

1. **检查动画片段是否加载**：
   ```typescript
   // 检查动画片段是否存在
   const hasClip = animator.hasClip('walk');
   console.log('Has walk clip:', hasClip);
   
   // 列出所有可用的动画片段
   const clips = animator.getClips();
   console.log('Available clips:', clips.map(clip => clip.name));
   ```

2. **检查动画控制器初始化**：
   ```typescript
   // 检查动画控制器是否有效
   const mixer = animator.getMixer();
   console.log('Mixer valid:', !!mixer);
   ```

3. **确保混合器正在更新**：
   ```typescript
   // 在游戏循环中更新混合器
   function update(deltaTime) {
     blender.update(deltaTime);
   }
   ```

4. **检查动画权重**：
   ```typescript
   // 检查混合层权重
   const layers = blender.getLayers();
   layers.forEach((layer, index) => {
     console.log(`Layer ${index}: ${layer.clipName}, weight: ${layer.weight}`);
   });
   
   // 设置非零权重
   blender.setLayerWeight(layerIndex, 1.0);
   ```

### 动画跳跃或抖动

**症状**：动画播放不平滑，出现跳跃或抖动。

**可能原因**：
- 混合过渡时间太短
- 动画循环点不匹配
- 骨骼旋转插值问题
- 帧率不稳定

**解决方案**：

1. **增加混合过渡时间**：
   ```typescript
   // 设置更长的混合过渡时间
   blender.setLayerWeight(layerIndex, 1.0, 0.5); // 0.5秒过渡时间
   ```

2. **检查动画循环点**：
   ```typescript
   // 创建平滑循环的子片段
   const smoothLoop = blender.createSubClip(
     'smoothWalkLoop',
     'walk',
     0.2, // 开始于稳定姿势
     0.8, // 结束于相似姿势
     true
   );
   ```

3. **确保使用四元数插值**：
   ```typescript
   // 检查动画轨道类型
   const clip = animator.getClip('walk');
   clip.tracks.forEach(track => {
     console.log(`Track: ${track.name}, type: ${track.ValueTypeName}`);
     // 确保旋转轨道使用QuaternionKeyframeTrack
   });
   ```

4. **稳定帧率**：
   ```typescript
   // 使用固定时间步长
   const fixedDeltaTime = 1/60; // 60fps
   blender.update(fixedDeltaTime);
   ```

### 动画穿插

**症状**：不同部位的动画相互干扰，导致不自然的姿势。

**可能原因**：
- 遮罩设置不正确
- 混合模式不适合
- 动画姿势不兼容

**解决方案**：

1. **检查并修正遮罩**：
   ```typescript
   // 创建明确的上半身遮罩
   const upperBodyMask = AnimationMask.createBoneGroupMask(BoneGroupType.UPPER_BODY);
   
   // 创建明确的下半身遮罩
   const lowerBodyMask = AnimationMask.createBoneGroupMask(BoneGroupType.LOWER_BODY);
   
   // 应用遮罩到相应层
   blender.setLayerMask(upperLayerIndex, upperBodyMask);
   blender.setLayerMask(lowerLayerIndex, lowerBodyMask);
   ```

2. **调整混合模式**：
   ```typescript
   // 对于叠加动画，使用加法混合模式
   blender.setLayerBlendMode(layerIndex, BlendMode.ADDITIVE);
   
   // 对于替换动画，使用覆盖混合模式
   blender.setLayerBlendMode(baseLayerIndex, BlendMode.OVERRIDE);
   ```

3. **调整动画姿势**：
   ```typescript
   // 使用姿势匹配工具调整动画起始姿势
   const matchedClip = AnimationUtils.matchPose(sourceClip, targetPose);
   ```

### 动画混合不正确

**症状**：动画混合结果不符合预期，如动作不自然或混合不平滑。

**可能原因**：
- 混合权重设置不当
- 混合曲线不适合
- 动画片段不兼容
- 混合空间设置错误

**解决方案**：

1. **调整混合权重**：
   ```typescript
   // 确保权重在有效范围内
   blender.setLayerWeight(layerIndex, Math.max(0, Math.min(1, weight)));
   
   // 归一化多个层的权重
   const totalWeight = layers.reduce((sum, layer) => sum + layer.weight, 0);
   layers.forEach((layer, index) => {
     blender.setLayerWeight(index, layer.weight / totalWeight);
   });
   ```

2. **选择适当的混合曲线**：
   ```typescript
   // 设置平滑的缓入缓出曲线
   blender.setBlendCurveType(BlendCurveType.EASE_IN_OUT);
   
   // 对于弹性效果，使用弹性曲线
   blender.setBlendCurveType(BlendCurveType.ELASTIC);
   ```

3. **预处理动画片段**：
   ```typescript
   // 重定向动画以匹配目标骨架
   const retargetedClip = AnimationUtils.retarget(sourceClip, targetSkeleton);
   ```

4. **检查混合空间设置**：
   ```typescript
   // 验证混合空间节点分布
   const nodes = blendSpace.getNodes();
   console.log('Blend space nodes:', nodes.map(node => ({
     name: node.name,
     threshold: node.threshold
   })));
   
   // 确保节点覆盖整个参数范围
   blendSpace.addNode('min', minValue);
   blendSpace.addNode('max', maxValue);
   ```

### 性能问题

**症状**：动画系统导致帧率下降或卡顿。

**可能原因**：
- 混合层过多
- 遮罩计算复杂
- 缓存未启用
- 对象创建过多
- 更新频率过高

**解决方案**：

1. **减少混合层**：
   ```typescript
   // 合并相似的混合层
   blender.clearLayers();
   blender.addLayer('combinedAnimation', 1.0);
   
   // 使用LOD系统根据距离减少混合层
   const distance = camera.position.distanceTo(character.position);
   const layerCount = distance < 10 ? 5 : (distance < 50 ? 3 : 1);
   ```

2. **优化遮罩**：
   ```typescript
   // 使用预计算的遮罩
   const cachedMask = AnimationMask.getPrecomputedMask('upperBody');
   blender.setLayerMask(layerIndex, cachedMask);
   
   // 减少遮罩中的骨骼数量
   const simplifiedMask = new AnimationMask();
   simplifiedMask.setBoneWeight('spine', 1.0);
   simplifiedMask.setBoneWeight('head', 1.0);
   ```

3. **启用性能优化**：
   ```typescript
   // 启用所有性能优化
   blender.setCacheConfig(true, 1000);
   blender.setObjectPoolConfig(true);
   blender.setBatchProcessingConfig(true);
   
   // 监控性能
   const stats = blender.getPerformanceStats();
   console.log('Performance stats:', stats);
   ```

4. **减少更新频率**：
   ```typescript
   // 对远处或不重要的角色降低更新频率
   const updateFrequency = isImportant ? 1 : (isVisible ? 2 : 10);
   if (frameCount % updateFrequency === 0) {
     blender.update(deltaTime * updateFrequency);
   }
   ```

## 诊断工具

IR引擎提供了多种诊断工具，帮助开发者排查动画问题：

### 性能监控

```typescript
// 启用性能监控
const monitor = BlendPerformanceMonitor.getInstance();
monitor.enable();

// 游戏运行一段时间后
const stats = monitor.getStats();
console.log('Performance stats:', stats);

// 导出详细数据进行分析
const jsonData = monitor.exportToJSON();
```

### 动画调试可视化

```typescript
// 启用骨骼可视化
const skeletonHelper = new THREE.SkeletonHelper(model);
scene.add(skeletonHelper);

// 显示动画轨迹
const motionPath = new AnimationMotionPath(animator, 'walk');
motionPath.setVisible(true);
```

### 动画状态检查器

```typescript
// 创建动画状态检查器
const inspector = new AnimationInspector(animator);

// 输出当前状态
console.log('Current state:', inspector.getCurrentState());

// 监听状态变化
inspector.addEventListener('stateChange', (event) => {
  console.log('Animation state changed:', event);
});
```

## 常见错误代码

| 错误代码 | 描述 | 解决方案 |
|---------|------|---------|
| `ANIM001` | 动画片段未找到 | 检查动画片段名称和加载过程 |
| `ANIM002` | 混合器未初始化 | 确保在使用前正确初始化混合器 |
| `ANIM003` | 骨骼层次结构不匹配 | 使用动画重定向或调整骨骼名称 |
| `ANIM004` | 遮罩应用失败 | 检查遮罩配置和骨骼名称 |
| `ANIM005` | 混合空间参数超出范围 | 确保参数在混合空间的有效范围内 |
| `ANIM006` | 性能警告：混合层过多 | 减少混合层数量或优化混合计算 |
| `ANIM007` | 内存警告：缓存过大 | 减小缓存大小或禁用不必要的缓存 |

## 联系支持

如果您在尝试上述解决方案后仍然遇到问题，请联系我们的技术支持团队：

- **邮件**：<EMAIL>
- **论坛**：https://forum.ir-engine.com/animation
- **问题追踪**：https://github.com/ir-engine/issues

提交问题时，请包含以下信息：
- 错误代码和详细描述
- 复现步骤
- 性能监控数据
- 相关代码片段
- 系统环境信息
