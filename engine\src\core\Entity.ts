/**
 * 实体类
 * 游戏世界中的基本对象，由组件组成
 */
import { Component } from './Component';
import { World } from './World';
import { Transform } from '../scene/Transform';
import { EventEmitter } from '../utils/EventEmitter';

export class Entity extends EventEmitter {
  /** 实体ID */
  public id: string = '';

  /** 实体名称 */
  public name: string;

  /** 是否活跃 */
  private active: boolean = true;

  /** 标签列表 */
  private tags: Set<string> = new Set();

  /** 组件映射 */
  private components: Map<string, Component> = new Map();

  /** 子实体列表 */
  private children: Entity[] = [];

  /** 父实体 */
  private parent: Entity | null = null;

  /** 世界引用 */
  private world: World | null = null;

  /** 变换组件 */
  private transform: Transform;

  /**
   * 创建实体实例
   * @param name 实体名称
   */
  constructor(name: string = '实体') {
    super();
    this.name = name;

    // 创建变换组件
    this.transform = new Transform();
    this.addComponent(this.transform);
  }

  /**
   * 更新实体
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.active) {
      return;
    }

    // 更新所有组件
    for (const component of Array.from(this.components.values())) {
      if (component.isEnabled()) {
        component.update(deltaTime);
      }
    }

    // 更新所有子实体
    for (const child of this.children) {
      child.update(deltaTime);
    }
  }

  /**
   * 固定时间步长更新
   * @param fixedDeltaTime 固定帧间隔时间（秒）
   */
  public fixedUpdate(fixedDeltaTime: number): void {
    if (!this.active) {
      return;
    }

    // 固定更新所有组件
    for (const component of Array.from(this.components.values())) {
      if (component.isEnabled()) {
        component.fixedUpdate(fixedDeltaTime);
      }
    }

    // 固定更新所有子实体
    for (const child of this.children) {
      child.fixedUpdate(fixedDeltaTime);
    }
  }

  /**
   * 添加组件
   * @param component 组件实例
   * @returns 添加的组件
   */
  public addComponent<T extends Component>(component: T): T {
    const type = component.getType();

    // 检查是否已存在同类型组件
    if (this.components.has(type)) {
      console.warn(`实体 ${this.name} 已经有一个 ${type} 组件`);
      return this.components.get(type) as T;
    }

    // 设置组件的实体引用
    component.setEntity(this);

    // 添加到组件映射
    this.components.set(type, component);

    // 发出组件添加事件
    this.emit('componentAdded', component);

    return component;
  }

  /**
   * 获取组件
   * @param type 组件类型
   * @returns 组件实例，如果不存在则返回null
   */
  public getComponent<T extends Component>(type: string): T | null {
    return (this.components.get(type) as T) || null;
  }

  /**
   * 移除组件
   * @param component 组件实例或类型
   * @returns 是否成功移除
   */
  public removeComponent(component: Component | string): boolean {
    const type = typeof component === 'string' ? component : component.getType();

    // 不能移除变换组件
    if (type === 'Transform') {
      console.warn('不能移除变换组件');
      return false;
    }

    if (!this.components.has(type)) {
      return false;
    }

    const componentToRemove = this.components.get(type)!;

    // 销毁组件
    componentToRemove.dispose();

    // 从组件映射中移除
    this.components.delete(type);

    // 发出组件移除事件
    this.emit('componentRemoved', componentToRemove);

    return true;
  }

  /**
   * 获取所有组件
   * @returns 组件数组
   */
  public getAllComponents(): Component[] {
    return Array.from(this.components.values());
  }

  /**
   * 是否有组件
   * @param type 组件类型
   * @returns 是否有该类型的组件
   */
  public hasComponent(type: string): boolean {
    return this.components.has(type);
  }

  /**
   * 添加子实体
   * @param child 子实体
   * @returns 添加的子实体
   */
  public addChild(child: Entity): Entity {
    // 如果已经是子实体，则不做任何操作
    if (this.children.includes(child)) {
      return child;
    }

    // 如果有父实体，则从父实体中移除
    if (child.parent) {
      child.parent.removeChild(child);
    }

    // 设置父实体
    child.parent = this;

    // 添加到子实体列表
    this.children.push(child);

    // 更新变换
    child.transform.setParent(this.transform);

    // 发出子实体添加事件
    this.emit('childAdded', child);

    return child;
  }

  /**
   * 移除子实体
   * @param child 子实体
   * @returns 是否成功移除
   */
  public removeChild(child: Entity): boolean {
    const index = this.children.indexOf(child);

    if (index === -1) {
      return false;
    }

    // 清除父实体引用
    child.parent = null;

    // 从子实体列表中移除
    this.children.splice(index, 1);

    // 更新变换
    child.transform.setParent(null);

    // 发出子实体移除事件
    this.emit('childRemoved', child);

    return true;
  }

  /**
   * 获取所有子实体
   * @returns 子实体数组
   */
  public getChildren(): Entity[] {
    return [...this.children];
  }

  /**
   * 获取父实体
   * @returns 父实体，如果没有则返回null
   */
  public getParent(): Entity | null {
    return this.parent;
  }

  /**
   * 获取变换组件
   * @returns 变换组件
   */
  public getTransform(): Transform {
    return this.transform;
  }

  /**
   * 设置世界引用
   * @param world 世界实例
   */
  public setWorld(world: World): void {
    this.world = world;
  }

  /**
   * 获取世界引用
   * @returns 世界实例
   */
  public getWorld(): World | null {
    return this.world;
  }

  /**
   * 设置活跃状态
   * @param active 是否活跃
   */
  public setActive(active: boolean): void {
    if (this.active === active) {
      return;
    }

    this.active = active;

    // 发出活跃状态变更事件
    this.emit('activeChanged', active);

    // 更新所有子实体的活跃状态
    for (const child of this.children) {
      child.setActive(active);
    }
  }

  /**
   * 是否活跃
   * @returns 是否活跃
   */
  public isActive(): boolean {
    // 如果父实体不活跃，则子实体也不活跃
    if (this.parent && !this.parent.isActive()) {
      return false;
    }

    return this.active;
  }

  /**
   * 添加标签
   * @param tag 标签
   */
  public addTag(tag: string): void {
    this.tags.add(tag);
  }

  /**
   * 移除标签
   * @param tag 标签
   * @returns 是否成功移除
   */
  public removeTag(tag: string): boolean {
    return this.tags.delete(tag);
  }

  /**
   * 是否有标签
   * @param tag 标签
   * @returns 是否有该标签
   */
  public hasTag(tag: string): boolean {
    return this.tags.has(tag);
  }

  /**
   * 获取所有标签
   * @returns 标签数组
   */
  public getTags(): string[] {
    return Array.from(this.tags);
  }

  /**
   * 销毁实体
   */
  public dispose(): void {
    // 移除所有子实体
    while (this.children.length > 0) {
      const child = this.children[0];
      this.removeChild(child);
      child.dispose();
    }

    // 移除所有组件
    for (const component of Array.from(this.components.values())) {
      component.dispose();
    }
    this.components.clear();

    // 如果有父实体，从父实体中移除
    if (this.parent) {
      this.parent.removeChild(this);
    }

    // 清除世界引用
    this.world = null;

    // 移除所有事件监听器
    this.removeAllListeners();
  }
}
