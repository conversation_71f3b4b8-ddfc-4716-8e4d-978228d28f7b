# IR引擎示例项目集成指南

本文档介绍了如何将示例项目集成到IR引擎编辑器中，包括示例项目浏览器的实现、示例项目的导入和导出功能，以及示例项目的更新和管理。

## 目录

- [示例项目浏览器](#示例项目浏览器)
- [示例项目导入](#示例项目导入)
- [示例项目导出](#示例项目导出)
- [示例项目更新](#示例项目更新)
- [示例项目管理](#示例项目管理)
- [开发者集成](#开发者集成)

## 示例项目浏览器

示例项目浏览器是IR引擎编辑器中的一个组件，用于浏览、搜索和加载示例项目。

### 实现方式

示例项目浏览器基于React和Ant Design实现，主要包括以下组件：

1. **ExampleBrowser**: 主容器组件，管理示例项目的加载和显示
2. **ExampleCard**: 示例项目卡片组件，显示项目预览和基本信息
3. **ExampleFilter**: 过滤和搜索组件，用于筛选示例项目
4. **ExampleDetail**: 示例项目详情组件，显示项目的详细信息和操作选项

### 集成到编辑器

将示例项目浏览器集成到编辑器的步骤如下：

1. 在编辑器的菜单中添加"示例项目"选项
2. 点击菜单选项时，打开示例项目浏览器面板
3. 在示例项目浏览器中，用户可以浏览、搜索和加载示例项目

### 示例代码

```jsx
// ExampleBrowser.jsx
import React, { useState, useEffect } from 'react';
import { Layout, Input, Select, Button, Row, Col } from 'antd';
import ExampleCard from './ExampleCard';
import ExampleFilter from './ExampleFilter';
import ExampleDetail from './ExampleDetail';
import { fetchExamples } from '../services/exampleService';

const { Header, Content } = Layout;
const { Search } = Input;

const ExampleBrowser = () => {
  const [examples, setExamples] = useState([]);
  const [filteredExamples, setFilteredExamples] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [searchText, setSearchText] = useState('');
  const [selectedExample, setSelectedExample] = useState(null);

  useEffect(() => {
    // 加载示例项目数据
    fetchExamples().then(data => {
      setExamples(data);
      setFilteredExamples(data);
    });
  }, []);

  // 处理类别过滤
  const handleCategoryChange = (category) => {
    setSelectedCategory(category);
    filterExamples(category, searchText);
  };

  // 处理搜索
  const handleSearch = (text) => {
    setSearchText(text);
    filterExamples(selectedCategory, text);
  };

  // 过滤示例项目
  const filterExamples = (category, text) => {
    let filtered = examples;
    
    // 按类别过滤
    if (category !== 'all') {
      filtered = filtered.filter(example => example.category === category);
    }
    
    // 按搜索文本过滤
    if (text) {
      const lowerText = text.toLowerCase();
      filtered = filtered.filter(example => 
        example.title.toLowerCase().includes(lowerText) || 
        example.description.toLowerCase().includes(lowerText) ||
        example.tags.some(tag => tag.toLowerCase().includes(lowerText))
      );
    }
    
    setFilteredExamples(filtered);
  };

  // 处理示例项目选择
  const handleExampleSelect = (example) => {
    setSelectedExample(example);
  };

  return (
    <Layout className="example-browser">
      <Header className="example-browser-header">
        <h2>示例项目浏览器</h2>
        <Search
          placeholder="搜索示例项目..."
          onSearch={handleSearch}
          style={{ width: 300 }}
        />
        <ExampleFilter
          selectedCategory={selectedCategory}
          onCategoryChange={handleCategoryChange}
        />
      </Header>
      <Content className="example-browser-content">
        {selectedExample ? (
          <ExampleDetail
            example={selectedExample}
            onBack={() => setSelectedExample(null)}
          />
        ) : (
          <Row gutter={[16, 16]}>
            {filteredExamples.map(example => (
              <Col key={example.id} xs={24} sm={12} md={8} lg={6}>
                <ExampleCard
                  example={example}
                  onClick={() => handleExampleSelect(example)}
                />
              </Col>
            ))}
          </Row>
        )}
      </Content>
    </Layout>
  );
};

export default ExampleBrowser;
```

## 示例项目导入

示例项目导入功能允许用户将示例项目导入到自己的工作空间中，以便进行学习、修改和扩展。

### 导入流程

1. 用户在示例项目浏览器中选择要导入的项目
2. 点击"导入项目"按钮
3. 弹出导入对话框，用户可以设置项目名称和保存位置
4. 确认导入后，系统将示例项目复制到用户的工作空间中
5. 导入完成后，自动打开导入的项目

### 实现方式

示例项目导入功能基于以下组件和服务实现：

1. **ImportDialog**: 导入对话框组件，用于设置项目名称和保存位置
2. **ProjectService**: 项目服务，处理项目的创建、复制和加载
3. **FileService**: 文件服务，处理文件和目录的复制和管理

### 示例代码

```jsx
// ImportDialog.jsx
import React, { useState } from 'react';
import { Modal, Form, Input, Select, Button } from 'antd';
import { importExample } from '../services/projectService';

const { Option } = Select;

const ImportDialog = ({ example, visible, onClose, onSuccess }) => {
  const [form] = Form.useForm();
  const [importing, setImporting] = useState(false);

  // 处理导入
  const handleImport = async () => {
    try {
      setImporting(true);
      const values = await form.validateFields();
      
      // 导入示例项目
      await importExample(example.id, values.name, values.location);
      
      setImporting(false);
      onSuccess();
    } catch (error) {
      setImporting(false);
      console.error('导入失败:', error);
    }
  };

  return (
    <Modal
      title="导入示例项目"
      visible={visible}
      onCancel={onClose}
      footer={[
        <Button key="cancel" onClick={onClose}>
          取消
        </Button>,
        <Button
          key="import"
          type="primary"
          loading={importing}
          onClick={handleImport}
        >
          导入
        </Button>
      ]}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          name: example ? example.title : '',
          location: 'projects'
        }}
      >
        <Form.Item
          name="name"
          label="项目名称"
          rules={[{ required: true, message: '请输入项目名称' }]}
        >
          <Input placeholder="请输入项目名称" />
        </Form.Item>
        <Form.Item
          name="location"
          label="保存位置"
          rules={[{ required: true, message: '请选择保存位置' }]}
        >
          <Select placeholder="请选择保存位置">
            <Option value="projects">项目目录</Option>
            <Option value="workspace">工作空间</Option>
            <Option value="custom">自定义位置</Option>
          </Select>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default ImportDialog;
```

## 示例项目导出

示例项目导出功能允许用户将自己的项目导出为示例项目，以便分享和贡献。

### 导出流程

1. 用户在编辑器中打开要导出的项目
2. 选择"文件 > 导出 > 导出为示例项目"
3. 弹出导出对话框，用户可以设置示例项目的标题、描述、类别和标签
4. 确认导出后，系统将项目导出为示例项目格式
5. 导出完成后，用户可以选择分享或提交示例项目

### 实现方式

示例项目导出功能基于以下组件和服务实现：

1. **ExportDialog**: 导出对话框组件，用于设置示例项目的信息
2. **ProjectService**: 项目服务，处理项目的导出和格式转换
3. **FileService**: 文件服务，处理文件和目录的复制和管理

### 示例代码

```jsx
// ExportDialog.jsx
import React, { useState } from 'react';
import { Modal, Form, Input, Select, Button, Tag, Upload } from 'antd';
import { PlusOutlined, UploadOutlined } from '@ant-design/icons';
import { exportAsExample } from '../services/projectService';

const { Option } = Select;
const { TextArea } = Input;

const ExportDialog = ({ project, visible, onClose, onSuccess }) => {
  const [form] = Form.useForm();
  const [tags, setTags] = useState([]);
  const [inputVisible, setInputVisible] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [exporting, setExporting] = useState(false);

  // 处理导出
  const handleExport = async () => {
    try {
      setExporting(true);
      const values = await form.validateFields();
      
      // 导出为示例项目
      await exportAsExample(project.id, {
        ...values,
        tags
      });
      
      setExporting(false);
      onSuccess();
    } catch (error) {
      setExporting(false);
      console.error('导出失败:', error);
    }
  };

  // 处理标签添加
  const handleTagAdd = () => {
    if (inputValue && !tags.includes(inputValue)) {
      setTags([...tags, inputValue]);
    }
    setInputValue('');
    setInputVisible(false);
  };

  return (
    <Modal
      title="导出为示例项目"
      visible={visible}
      onCancel={onClose}
      width={600}
      footer={[
        <Button key="cancel" onClick={onClose}>
          取消
        </Button>,
        <Button
          key="export"
          type="primary"
          loading={exporting}
          onClick={handleExport}
        >
          导出
        </Button>
      ]}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          title: project ? project.name : '',
          category: 'basic'
        }}
      >
        <Form.Item
          name="title"
          label="标题"
          rules={[{ required: true, message: '请输入标题' }]}
        >
          <Input placeholder="请输入标题" />
        </Form.Item>
        <Form.Item
          name="description"
          label="描述"
          rules={[{ required: true, message: '请输入描述' }]}
        >
          <TextArea rows={4} placeholder="请输入描述" />
        </Form.Item>
        <Form.Item
          name="category"
          label="类别"
          rules={[{ required: true, message: '请选择类别' }]}
        >
          <Select placeholder="请选择类别">
            <Option value="basic">基础功能</Option>
            <Option value="material">材质系统</Option>
            <Option value="animation">动画系统</Option>
            <Option value="physics">物理系统</Option>
            <Option value="visualscript">视觉脚本</Option>
            <Option value="advanced">高级示例</Option>
          </Select>
        </Form.Item>
        <Form.Item label="标签">
          <div className="tag-input">
            {tags.map((tag, index) => (
              <Tag
                key={tag}
                closable
                onClose={() => {
                  const newTags = [...tags];
                  newTags.splice(index, 1);
                  setTags(newTags);
                }}
              >
                {tag}
              </Tag>
            ))}
            {inputVisible ? (
              <Input
                type="text"
                size="small"
                style={{ width: 78 }}
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onBlur={handleTagAdd}
                onPressEnter={handleTagAdd}
                autoFocus
              />
            ) : (
              <Tag
                onClick={() => setInputVisible(true)}
                style={{ background: '#fff', borderStyle: 'dashed' }}
              >
                <PlusOutlined /> 添加标签
              </Tag>
            )}
          </div>
        </Form.Item>
        <Form.Item
          name="preview"
          label="预览图"
        >
          <Upload
            name="preview"
            listType="picture"
            maxCount={1}
            beforeUpload={() => false}
          >
            <Button icon={<UploadOutlined />}>上传预览图</Button>
          </Upload>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default ExportDialog;
```

## 示例项目更新

示例项目更新功能允许用户更新已导入的示例项目，以获取最新的功能和改进。

### 更新流程

1. 用户在编辑器中打开已导入的示例项目
2. 系统检测到有新版本的示例项目可用
3. 显示更新提示，用户可以选择更新或忽略
4. 确认更新后，系统将下载并应用最新版本的示例项目
5. 更新完成后，重新加载项目

### 实现方式

示例项目更新功能基于以下组件和服务实现：

1. **UpdateNotification**: 更新提示组件，用于显示更新信息和选项
2. **VersionService**: 版本服务，处理版本检查和比较
3. **UpdateService**: 更新服务，处理示例项目的下载和更新

### 示例代码

```jsx
// UpdateNotification.jsx
import React from 'react';
import { notification, Button } from 'antd';
import { updateExample } from '../services/updateService';

const UpdateNotification = ({ example, currentVersion, latestVersion, onUpdate, onIgnore }) => {
  // 处理更新
  const handleUpdate = async () => {
    try {
      // 更新示例项目
      await updateExample(example.id, latestVersion);
      
      notification.success({
        message: '更新成功',
        description: `示例项目 "${example.title}" 已更新到最新版本 ${latestVersion}。`,
      });
      
      onUpdate();
    } catch (error) {
      console.error('更新失败:', error);
      
      notification.error({
        message: '更新失败',
        description: `示例项目 "${example.title}" 更新失败，请稍后重试。`,
      });
    }
  };

  return (
    <div className="update-notification">
      <p>发现新版本的示例项目 "{example.title}"。</p>
      <p>当前版本: {currentVersion}</p>
      <p>最新版本: {latestVersion}</p>
      <div className="update-actions">
        <Button type="primary" onClick={handleUpdate}>
          更新
        </Button>
        <Button onClick={onIgnore}>
          忽略
        </Button>
      </div>
    </div>
  );
};

export default UpdateNotification;
```

## 示例项目管理

示例项目管理功能允许管理员管理和维护示例项目库，包括添加、编辑、删除和发布示例项目。

### 管理功能

1. **添加示例项目**: 添加新的示例项目到示例项目库
2. **编辑示例项目**: 编辑现有示例项目的信息和内容
3. **删除示例项目**: 从示例项目库中删除示例项目
4. **发布示例项目**: 将示例项目发布到公共示例项目库

### 实现方式

示例项目管理功能基于以下组件和服务实现：

1. **ExampleManager**: 示例项目管理组件，用于管理示例项目
2. **AdminService**: 管理服务，处理示例项目的添加、编辑、删除和发布

### 示例代码

```jsx
// ExampleManager.jsx
import React, { useState, useEffect } from 'react';
import { Table, Button, Space, Modal, message } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, UploadOutlined } from '@ant-design/icons';
import { fetchExamples, deleteExample, publishExample } from '../services/adminService';
import ExampleForm from './ExampleForm';

const ExampleManager = () => {
  const [examples, setExamples] = useState([]);
  const [loading, setLoading] = useState(false);
  const [formVisible, setFormVisible] = useState(false);
  const [currentExample, setCurrentExample] = useState(null);

  useEffect(() => {
    loadExamples();
  }, []);

  // 加载示例项目
  const loadExamples = async () => {
    setLoading(true);
    try {
      const data = await fetchExamples();
      setExamples(data);
    } catch (error) {
      console.error('加载失败:', error);
      message.error('加载示例项目失败，请稍后重试。');
    }
    setLoading(false);
  };

  // 处理添加
  const handleAdd = () => {
    setCurrentExample(null);
    setFormVisible(true);
  };

  // 处理编辑
  const handleEdit = (example) => {
    setCurrentExample(example);
    setFormVisible(true);
  };

  // 处理删除
  const handleDelete = (example) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除示例项目 "${example.title}" 吗？`,
      onOk: async () => {
        try {
          await deleteExample(example.id);
          message.success('删除成功');
          loadExamples();
        } catch (error) {
          console.error('删除失败:', error);
          message.error('删除失败，请稍后重试。');
        }
      }
    });
  };

  // 处理发布
  const handlePublish = (example) => {
    Modal.confirm({
      title: '确认发布',
      content: `确定要发布示例项目 "${example.title}" 到公共示例项目库吗？`,
      onOk: async () => {
        try {
          await publishExample(example.id);
          message.success('发布成功');
          loadExamples();
        } catch (error) {
          console.error('发布失败:', error);
          message.error('发布失败，请稍后重试。');
        }
      }
    });
  };

  // 表格列定义
  const columns = [
    {
      title: '标题',
      dataIndex: 'title',
      key: 'title',
    },
    {
      title: '类别',
      dataIndex: 'category',
      key: 'category',
    },
    {
      title: '版本',
      dataIndex: 'version',
      key: 'version',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button
            icon={<DeleteOutlined />}
            danger
            onClick={() => handleDelete(record)}
          >
            删除
          </Button>
          {record.status !== 'published' && (
            <Button
              icon={<UploadOutlined />}
              type="primary"
              onClick={() => handlePublish(record)}
            >
              发布
            </Button>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div className="example-manager">
      <div className="example-manager-header">
        <h2>示例项目管理</h2>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleAdd}
        >
          添加示例项目
        </Button>
      </div>
      <Table
        columns={columns}
        dataSource={examples}
        rowKey="id"
        loading={loading}
      />
      {formVisible && (
        <ExampleForm
          example={currentExample}
          visible={formVisible}
          onClose={() => setFormVisible(false)}
          onSuccess={() => {
            setFormVisible(false);
            loadExamples();
          }}
        />
      )}
    </div>
  );
};

export default ExampleManager;
```

## 开发者集成

开发者可以通过以下方式将自己的示例项目集成到IR引擎编辑器中：

### 1. 创建示例项目

按照[示例项目结构说明](./structure.md)创建符合规范的示例项目。

### 2. 添加示例项目元数据

在示例项目根目录下创建 `example.json` 文件，包含以下信息：

```json
{
  "title": "示例项目标题",
  "description": "示例项目描述",
  "category": "basic",
  "tags": ["标签1", "标签2", "标签3"],
  "version": "1.0.0",
  "author": "作者名称",
  "license": "MIT",
  "preview": "assets/images/preview.jpg",
  "dependencies": {
    "engine": ">=1.0.0",
    "editor": ">=1.0.0"
  }
}
```

### 3. 提交示例项目

将示例项目提交到IR引擎示例项目仓库，或者通过编辑器的示例项目管理功能上传示例项目。

### 4. 发布示例项目

管理员审核通过后，示例项目将被发布到公共示例项目库，供所有用户使用。

## 总结

通过本文档介绍的方法和技术，您可以将示例项目集成到IR引擎编辑器中，为用户提供丰富的学习和参考资源。示例项目不仅可以帮助用户快速了解和掌握IR引擎的各种功能，还可以作为开发者分享知识和经验的平台。
