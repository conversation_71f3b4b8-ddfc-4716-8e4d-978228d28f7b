/**
 * GPU加速的面部动画系统
 * 使用GPU加速面部动画计算，提高性能
 */
import * as THREE from 'three';
import { Entity } from '../core/Entity';
import { Component } from '../core/Component';
import { System } from '../core/System';
import { World } from '../core/World';
import { EventEmitter } from '../utils/EventEmitter';
import { FacialExpressionType } from './FacialExpressionType';
import { VisemeType } from './FacialAnimation';

/**
 * GPU面部动画配置
 */
export interface GPUFacialAnimationConfig {
  /** 是否使用计算着色器 */
  useComputeShader?: boolean;
  /** 是否启用调试 */
  debug?: boolean;
  /** 最大混合形状数量 */
  maxBlendShapes?: number;
  /** 纹理大小 */
  textureSize?: number;
}

/**
 * GPU面部动画组件
 */
export class GPUFacialAnimationComponent extends Component {
  /** 组件类型 */
  static readonly type = 'GPUFacialAnimation';

  /** 原始材质 */
  private originalMaterial: THREE.Material | null = null;
  /** GPU材质 */
  private gpuMaterial: THREE.ShaderMaterial | null = null;
  /** 混合形状纹理 */
  private blendShapeTexture: THREE.DataTexture | null = null;
  /** 混合形状数据 */
  private blendShapeData: Float32Array | null = null;
  /** 混合形状映射 */
  private blendShapeMap: Map<string, number> = new Map();
  /** 是否初始化 */
  private initialized: boolean = false;
  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /**
   * 构造函数
   * @param entity 实体
   */
  constructor(entity: Entity) {
    super(GPUFacialAnimationComponent.type);
    this.setEntity(entity);
  }

  /**
   * 初始化
   * @param mesh 骨骼网格
   * @param textureSize 纹理大小
   * @returns 是否成功初始化
   */
  public initialize(mesh: THREE.SkinnedMesh, textureSize: number = 16): boolean {
    if (!mesh.morphTargetDictionary || !mesh.morphTargetInfluences) {
      console.warn('网格没有混合形状');
      return false;
    }

    // 保存原始材质
    this.originalMaterial = Array.isArray(mesh.material) ? mesh.material[0] : mesh.material;

    // 创建混合形状映射
    this.createBlendShapeMap(mesh);

    // 创建混合形状纹理
    this.createBlendShapeTexture(mesh, textureSize);

    // 创建GPU材质
    this.createGPUMaterial(mesh);

    this.initialized = true;
    return true;
  }

  /**
   * 创建混合形状映射
   * @param mesh 骨骼网格
   */
  private createBlendShapeMap(mesh: THREE.SkinnedMesh): void {
    if (!mesh.morphTargetDictionary) return;

    // 清除现有映射
    this.blendShapeMap.clear();

    // 创建映射
    for (const [name, index] of Object.entries(mesh.morphTargetDictionary)) {
      this.blendShapeMap.set(name, index);
    }
  }

  /**
   * 创建混合形状纹理
   * @param mesh 骨骼网格
   * @param textureSize 纹理大小
   */
  private createBlendShapeTexture(mesh: THREE.SkinnedMesh, textureSize: number): void {
    if (!mesh.morphTargetInfluences) return;

    // 创建数据
    const size = textureSize * textureSize;
    this.blendShapeData = new Float32Array(size * 4);

    // 初始化数据
    for (let i = 0; i < size * 4; i++) {
      this.blendShapeData[i] = 0;
    }

    // 创建纹理
    this.blendShapeTexture = new THREE.DataTexture(
      this.blendShapeData,
      textureSize,
      textureSize,
      THREE.RGBAFormat,
      THREE.FloatType
    );

    this.blendShapeTexture.needsUpdate = true;
  }

  /**
   * 创建GPU材质
   * @param mesh 骨骼网格
   */
  private createGPUMaterial(mesh: THREE.SkinnedMesh): void {
    if (!this.originalMaterial || !this.blendShapeTexture) return;

    // 顶点着色器
    const vertexShader = `
      uniform sampler2D blendShapeTexture;
      uniform float blendShapeTextureSize;

      attribute vec4 morphTarget0;
      attribute vec4 morphTarget1;
      attribute vec4 morphTarget2;
      attribute vec4 morphTarget3;
      attribute vec4 morphTarget4;
      attribute vec4 morphTarget5;
      attribute vec4 morphTarget6;
      attribute vec4 morphTarget7;

      vec4 getBlendShapeWeight(int index) {
        float y = floor(float(index) / blendShapeTextureSize);
        float x = float(index) - y * blendShapeTextureSize;
        vec2 uv = vec2(x, y) / blendShapeTextureSize;
        return texture2D(blendShapeTexture, uv);
      }

      vec3 applyBlendShape(vec3 position) {
        vec3 result = position;

        // 应用混合形状
        for (int i = 0; i < 8; i++) {
          vec4 weight = getBlendShapeWeight(i);

          if (i == 0) result += morphTarget0.xyz * weight.x;
          else if (i == 1) result += morphTarget1.xyz * weight.x;
          else if (i == 2) result += morphTarget2.xyz * weight.x;
          else if (i == 3) result += morphTarget3.xyz * weight.x;
          else if (i == 4) result += morphTarget4.xyz * weight.x;
          else if (i == 5) result += morphTarget5.xyz * weight.x;
          else if (i == 6) result += morphTarget6.xyz * weight.x;
          else if (i == 7) result += morphTarget7.xyz * weight.x;
        }

        return result;
      }

      void main() {
        vec3 transformed = applyBlendShape(position);

        // 应用骨骼动画
        #include <skinning_vertex>

        // 投影
        gl_Position = projectionMatrix * modelViewMatrix * vec4(transformed, 1.0);
      }
    `;

    // 片段着色器
    const fragmentShader = `
      uniform vec3 diffuse;
      uniform float opacity;
      uniform sampler2D map;

      varying vec2 vUv;

      void main() {
        vec4 texColor = texture2D(map, vUv);
        gl_FragColor = vec4(diffuse, opacity) * texColor;
      }
    `;

    // 创建GPU材质
    this.gpuMaterial = new THREE.ShaderMaterial({
      uniforms: {
        blendShapeTexture: { value: this.blendShapeTexture },
        blendShapeTextureSize: { value: this.blendShapeTexture.image.width },
        diffuse: { value: new THREE.Color(0xffffff) },
        opacity: { value: 1.0 },
        map: { value: null }
      },
      vertexShader,
      fragmentShader,
      transparent: this.originalMaterial.transparent,
      side: this.originalMaterial.side
    });

    // 设置材质属性
    (this.gpuMaterial as any).skinning = true;
    (this.gpuMaterial as any).morphTargets = true;

    // 如果原始材质有纹理，设置纹理
    if ('map' in this.originalMaterial && this.originalMaterial.map) {
      this.gpuMaterial.uniforms.map.value = this.originalMaterial.map;
    }

    // 应用GPU材质
    if (Array.isArray(mesh.material)) {
      // 如果是材质数组，替换第一个材质
      mesh.material[0] = this.gpuMaterial;
    } else {
      // 直接替换材质
      mesh.material = this.gpuMaterial;
    }
  }

  /**
   * 设置混合形状权重
   * @param name 混合形状名称
   * @param weight 权重
   * @returns 是否成功设置
   */
  public setBlendShapeWeight(name: string, weight: number): boolean {
    if (!this.initialized || !this.blendShapeData || !this.blendShapeTexture) return false;

    // 获取混合形状索引
    const index = this.blendShapeMap.get(name);
    if (index === undefined) return false;

    // 设置权重
    const offset = index * 4;
    this.blendShapeData[offset] = weight;

    // 更新纹理
    this.blendShapeTexture.needsUpdate = true;

    return true;
  }

  /**
   * 设置表情
   * @param expression 表情类型
   * @param weight 权重
   * @returns 是否成功设置
   */
  public setExpression(expression: FacialExpressionType, weight: number): boolean {
    // 根据表情类型设置相应的混合形状
    switch (expression) {
      case FacialExpressionType.NEUTRAL:
        this.resetAllBlendShapes();
        return true;
      case FacialExpressionType.HAPPY:
        return this.setBlendShapeWeight('smile', weight);
      case FacialExpressionType.SAD:
        return this.setBlendShapeWeight('sad', weight);
      case FacialExpressionType.ANGRY:
        return this.setBlendShapeWeight('angry', weight);
      case FacialExpressionType.SURPRISED:
        return this.setBlendShapeWeight('surprised', weight);
      case FacialExpressionType.FEAR:
        return this.setBlendShapeWeight('fear', weight);
      case FacialExpressionType.DISGUST:
      case FacialExpressionType.DISGUSTED:
        return this.setBlendShapeWeight('disgust', weight);
      default:
        return false;
    }
  }

  /**
   * 设置口型
   * @param viseme 口型类型
   * @param weight 权重
   * @returns 是否成功设置
   */
  public setViseme(viseme: VisemeType, weight: number): boolean {
    // 根据口型类型设置相应的混合形状
    switch (viseme) {
      case VisemeType.SILENT:
        return this.setBlendShapeWeight('mouthClose', weight);
      case VisemeType.AA:
        return this.setBlendShapeWeight('mouthA', weight);
      case VisemeType.EE:
        return this.setBlendShapeWeight('mouthE', weight);
      case VisemeType.IH:
        return this.setBlendShapeWeight('mouthI', weight);
      case VisemeType.OH:
        return this.setBlendShapeWeight('mouthO', weight);
      case VisemeType.OU:
        return this.setBlendShapeWeight('mouthU', weight);
      case VisemeType.PP:
        return this.setBlendShapeWeight('mouthP', weight);
      case VisemeType.FF:
        return this.setBlendShapeWeight('mouthF', weight);
      case VisemeType.TH:
        return this.setBlendShapeWeight('mouthTh', weight);
      case VisemeType.DD:
        return this.setBlendShapeWeight('mouthD', weight);
      case VisemeType.KK:
        return this.setBlendShapeWeight('mouthK', weight);
      case VisemeType.CH:
        return this.setBlendShapeWeight('mouthCh', weight);
      case VisemeType.SS:
        return this.setBlendShapeWeight('mouthS', weight);
      case VisemeType.NN:
        return this.setBlendShapeWeight('mouthN', weight);
      case VisemeType.RR:
        return this.setBlendShapeWeight('mouthR', weight);
      default:
        return false;
    }
  }

  /**
   * 重置所有混合形状
   */
  public resetAllBlendShapes(): void {
    if (!this.initialized || !this.blendShapeData || !this.blendShapeTexture) return;

    // 重置所有权重
    for (let i = 0; i < this.blendShapeData.length; i++) {
      this.blendShapeData[i] = 0;
    }

    // 更新纹理
    this.blendShapeTexture.needsUpdate = true;
  }

  /**
   * 恢复原始材质
   * @param mesh 骨骼网格
   */
  public restoreOriginalMaterial(mesh: THREE.SkinnedMesh): void {
    if (!this.originalMaterial) return;

    // 恢复原始材质
    if (Array.isArray(mesh.material)) {
      // 如果是材质数组，恢复第一个材质
      mesh.material[0] = this.originalMaterial;
    } else {
      // 直接恢复材质
      mesh.material = this.originalMaterial;
    }
  }

  /**
   * 销毁GPU面部动画
   * @param mesh 骨骼网格
   */
  public disposeGPU(mesh: THREE.SkinnedMesh): void {
    // 恢复原始材质
    this.restoreOriginalMaterial(mesh);

    // 释放资源
    if (this.blendShapeTexture) {
      this.blendShapeTexture.dispose();
      this.blendShapeTexture = null;
    }

    if (this.gpuMaterial) {
      this.gpuMaterial.dispose();
      this.gpuMaterial = null;
    }

    this.blendShapeData = null;
    this.blendShapeMap.clear();
    this.initialized = false;
  }

  /**
   * 销毁组件
   */
  protected onDispose(): void {
    // 基础清理
    this.blendShapeMap.clear();
    this.initialized = false;
  }
}

/**
 * GPU面部动画系统
 */
export class GPUFacialAnimationSystem extends System {
  /** 系统类型 */
  static readonly type = 'GPUFacialAnimation';

  /** GPU面部动画组件 */
  private components: Map<Entity, GPUFacialAnimationComponent> = new Map();
  /** 配置 */
  private config: GPUFacialAnimationConfig;
  /** 是否支持GPU加速 */
  private supportsGPU: boolean = false;
  /** 是否支持计算着色器 */
  private supportsComputeShader: boolean = false;
  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /**
   * 构造函数
   * @param world 世界
   * @param config 配置
   */
  constructor(config: GPUFacialAnimationConfig = {}) {
    super(400); // 设置优先级

    this.config = {
      useComputeShader: config.useComputeShader !== undefined ? config.useComputeShader : false,
      debug: config.debug || false,
      maxBlendShapes: config.maxBlendShapes || 32,
      textureSize: config.textureSize || 16
    };

    // 检查GPU支持
    this.checkGPUSupport();
  }

  /**
   * 检查GPU支持
   */
  private checkGPUSupport(): void {
    // 检查WebGL2和相关扩展
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl2');

    if (gl) {
      // WebGL2支持
      this.supportsGPU = true;

      // 检查计算着色器支持
      const computeExt = gl.getExtension('WEBGL_compute_shader');
      this.supportsComputeShader = !!computeExt;

      if (this.config.debug) {
        console.log('GPU面部动画支持: WebGL2');
        console.log('计算着色器支持:', this.supportsComputeShader);
      }
    } else {
      // 检查WebGL1扩展
      const gl1 = canvas.getContext('webgl');
      if (gl1) {
        // 检查浮点纹理支持
        const floatExt = gl1.getExtension('OES_texture_float');
        this.supportsGPU = !!floatExt;
        this.supportsComputeShader = false;

        if (this.config.debug) {
          console.log('GPU面部动画支持: WebGL1 + OES_texture_float');
          console.log('计算着色器支持: false');
        }
      } else {
        this.supportsGPU = false;
        this.supportsComputeShader = false;

        if (this.config.debug) {
          console.warn('GPU面部动画不支持: WebGL不可用');
        }
      }
    }

    // 如果配置要求使用计算着色器但不支持，发出警告
    if (this.config.useComputeShader && !this.supportsComputeShader) {
      console.warn('计算着色器不可用，将使用标准GPU加速');
    }
  }

  /**
   * 创建GPU面部动画组件
   * @param entity 实体
   * @param mesh 骨骼网格
   * @returns GPU面部动画组件
   */
  public createGPUFacialAnimation(entity: Entity, mesh: THREE.SkinnedMesh): GPUFacialAnimationComponent {
    // 检查是否已存在组件
    if (this.components.has(entity)) {
      return this.components.get(entity)!;
    }

    // 创建新组件
    const component = new GPUFacialAnimationComponent(entity);
    this.components.set(entity, component);

    // 初始化组件
    if (this.supportsGPU) {
      component.initialize(mesh, this.config.textureSize);
    } else if (this.config.debug) {
      console.warn(`无法为实体 ${entity.id} 创建GPU面部动画: GPU加速不可用`);
    }

    return component;
  }

  /**
   * 移除GPU面部动画组件
   * @param entity 实体
   * @param mesh 骨骼网格
   */
  public removeGPUFacialAnimation(entity: Entity, mesh: THREE.SkinnedMesh): void {
    const component = this.components.get(entity);
    if (component) {
      // 销毁组件
      component.disposeGPU(mesh);

      // 移除组件
      this.components.delete(entity);

      if (this.config.debug) {
        console.log(`已移除实体 ${entity.id} 的GPU面部动画组件`);
      }
    }
  }

  /**
   * 获取GPU面部动画组件
   * @param entity 实体
   * @returns GPU面部动画组件，如果不存在则返回null
   */
  public getGPUFacialAnimation(entity: Entity): GPUFacialAnimationComponent | null {
    return this.components.get(entity) || null;
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    // GPU面部动画不需要每帧更新
    // 组件的更新由FacialAnimationSystem触发
  }
}
