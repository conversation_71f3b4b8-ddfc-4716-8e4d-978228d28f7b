# 植被系统

植被系统是IR引擎中用于在地形上生成和管理植被的系统。本文档将介绍植被系统的基本概念、使用方法以及高级功能。

## 目录

- [基本概念](#基本概念)
- [快速开始](#快速开始)
- [植被组件](#植被组件)
- [植被系统](#植被系统)
- [植被分布](#植被分布)
- [植被类型](#植被类型)
- [高级功能](#高级功能)
- [性能优化](#性能优化)
- [编辑器使用](#编辑器使用)
- [API参考](#api参考)

## 基本概念

植被系统由以下几个主要组件组成：

- **植被组件（VegetationComponent）**：附加到实体上，定义植被的类型、分布和属性。
- **植被系统（VegetationSystem）**：管理所有植被组件，处理植被的生成、更新和渲染。
- **植被项（VegetationItem）**：定义单个植被类型的属性，如模型、密度、高度范围等。
- **植被实例（VegetationInstance）**：表示场景中的单个植被对象，包含位置、旋转、缩放等信息。

植被系统与地形系统紧密集成，可以根据地形的高度、坡度和纹理分布植被。它还支持实例化渲染、LOD、视锥体剔除等优化技术，以提高性能。

## 快速开始

以下是使用植被系统的基本步骤：

1. 创建地形实体和组件
2. 创建植被实体和组件
3. 配置植被项
4. 生成植被

```typescript
// 创建地形实体
const terrainEntity = new Entity('terrain');
terrainEntity.addComponent(new TerrainComponent({
  width: 1000,
  height: 1000,
  resolution: 256,
  maxHeight: 100
}));
world.addEntity(terrainEntity);

// 创建植被实体
const vegetationEntity = new Entity('vegetation');
vegetationEntity.addComponent(new VegetationComponent({
  terrainEntity: terrainEntity.id,
  items: [
    {
      model: 'assets/models/tree1.glb',
      density: 0.001,
      minScale: 0.8,
      maxScale: 1.2,
      minHeight: 10,
      maxHeight: 50,
      slopeMin: 0,
      slopeMax: 30
    },
    {
      model: 'assets/models/grass1.glb',
      density: 0.05,
      minScale: 0.5,
      maxScale: 1.5,
      minHeight: 5,
      maxHeight: 40,
      slopeMin: 0,
      slopeMax: 45
    }
  ]
}));
world.addEntity(vegetationEntity);

// 添加植被系统
const vegetationSystem = new VegetationSystem();
world.addSystem(vegetationSystem);
```

## 植被组件

植被组件（VegetationComponent）是植被系统的核心，它定义了植被的类型、分布和属性。

### 植被组件选项

```typescript
interface VegetationComponentOptions {
  /** 地形实体ID */
  terrainEntity: string;
  /** 植被项列表 */
  items: VegetationItemConfig[];
  /** 是否自动生成 */
  autoGenerate?: boolean;
  /** 种子 */
  seed?: number;
  /** 是否使用实例化渲染 */
  useInstancing?: boolean;
  /** 是否使用LOD */
  useLOD?: boolean;
  /** LOD距离 */
  lodDistances?: number[];
  /** 是否使用视锥体剔除 */
  useFrustumCulling?: boolean;
  /** 是否使用八叉树 */
  useOctree?: boolean;
  /** 是否使用GPU实例化 */
  useGPUInstancing?: boolean;
  /** 是否使用阴影 */
  useShadow?: boolean;
  /** 是否使用风效果 */
  useWind?: boolean;
  /** 风力参数 */
  windParams?: {
    strength: number;
    direction: THREE.Vector2;
    frequency: number;
    turbulence: number;
  };
  /** 是否使用季节效果 */
  useSeasonal?: boolean;
  /** 季节参数 */
  seasonalParams?: {
    season: 'spring' | 'summer' | 'autumn' | 'winter';
    intensity: number;
  };
  /** 是否使用分布图 */
  useDistributionMap?: boolean;
  /** 分布图 */
  distributionMap?: string | THREE.Texture;
  /** 是否使用密度图 */
  useDensityMap?: boolean;
  /** 密度图 */
  densityMap?: string | THREE.Texture;
}
```

### 植被项配置

```typescript
interface VegetationItemConfig {
  /** 模型路径 */
  model: string;
  /** 密度 (0-1) */
  density: number;
  /** 最小缩放 */
  minScale: number;
  /** 最大缩放 */
  maxScale: number;
  /** 最小高度 (地形高度) */
  minHeight: number;
  /** 最大高度 (地形高度) */
  maxHeight: number;
  /** 最小坡度 (度) */
  slopeMin: number;
  /** 最大坡度 (度) */
  slopeMax: number;
  /** 随机旋转 (是否随机旋转Y轴) */
  randomRotation?: boolean;
  /** 随机偏移 (随机位置偏移量) */
  randomOffset?: number;
  /** 避开水体 */
  avoidWater?: boolean;
  /** 颜色变化 */
  colorVariation?: {
    enabled: boolean;
    hue: number;
    saturation: number;
    lightness: number;
  };
  /** 季节影响 */
  seasonalEffect?: boolean;
  /** 风力影响 */
  windEffect?: boolean;
  /** 自定义数据 */
  userData?: any;
}
```

## 植被系统

植被系统（VegetationSystem）管理所有植被组件，处理植被的生成、更新和渲染。

### 植被系统选项

```typescript
interface VegetationSystemOptions {
  /** 是否启用 */
  enabled?: boolean;
  /** 是否自动更新 */
  autoUpdate?: boolean;
  /** 更新频率 */
  updateFrequency?: number;
  /** 是否使用实例化渲染 */
  useInstancing?: boolean;
  /** 是否使用LOD */
  useLOD?: boolean;
  /** 是否使用视锥体剔除 */
  useFrustumCulling?: boolean;
  /** 是否使用八叉树 */
  useOctree?: boolean;
  /** 是否使用GPU实例化 */
  useGPUInstancing?: boolean;
  /** 是否使用阴影 */
  useShadow?: boolean;
  /** 是否使用风效果 */
  useWind?: boolean;
  /** 是否使用调试可视化 */
  useDebugVisualization?: boolean;
}
```

### 植被系统方法

- **generateVegetation(entity, component)**：生成植被
- **clearVegetation(entity, component)**：清除植被
- **updateVegetation(entity, component, camera)**：更新植被

## 植被分布

植被系统支持多种植被分布方式：

### 基于高度和坡度

最基本的分布方式是基于地形的高度和坡度。每个植被项都可以定义高度范围和坡度范围，只有在这些范围内的地形区域才会生成该类型的植被。

```typescript
{
  model: 'assets/models/tree1.glb',
  density: 0.001,
  minHeight: 10,
  maxHeight: 50,
  slopeMin: 0,
  slopeMax: 30
}
```

### 基于分布图

分布图是一种灰度图，用于控制植被的分布。白色区域表示可以生成植被，黑色区域表示不生成植被，灰色区域表示生成概率。

```typescript
{
  useDistributionMap: true,
  distributionMap: 'assets/textures/distribution_map.png'
}
```

### 基于密度图

密度图是一种灰度图，用于控制植被的密度。白色区域表示高密度，黑色区域表示低密度。

```typescript
{
  useDensityMap: true,
  densityMap: 'assets/textures/density_map.png'
}
```

## 植被类型

植被系统支持多种植被类型，每种类型都可以有不同的属性和行为。

### 树木

树木通常是大型的、稀疏分布的植被，适合使用LOD和实例化渲染。

```typescript
{
  model: 'assets/models/tree1.glb',
  density: 0.001,
  minScale: 0.8,
  maxScale: 1.2,
  minHeight: 10,
  maxHeight: 50,
  slopeMin: 0,
  slopeMax: 30,
  randomRotation: true,
  windEffect: true
}
```

### 草地

草地通常是小型的、密集分布的植被，适合使用实例化渲染和GPU实例化。

```typescript
{
  model: 'assets/models/grass1.glb',
  density: 0.05,
  minScale: 0.5,
  maxScale: 1.5,
  minHeight: 5,
  maxHeight: 40,
  slopeMin: 0,
  slopeMax: 45,
  randomRotation: true,
  windEffect: true
}
```

### 岩石

岩石通常是中型的、稀疏分布的植被，适合使用实例化渲染。

```typescript
{
  model: 'assets/models/rock1.glb',
  density: 0.002,
  minScale: 0.5,
  maxScale: 2.0,
  minHeight: 0,
  maxHeight: 100,
  slopeMin: 20,
  slopeMax: 90,
  randomRotation: true,
  windEffect: false
}
```

## 高级功能

植被系统支持多种高级功能，以提高植被的真实感和性能。

### 风效果

风效果可以使植被随风摆动，增加场景的动态感。

```typescript
{
  useWind: true,
  windParams: {
    strength: 0.1,
    direction: new THREE.Vector2(1, 0),
    frequency: 0.2,
    turbulence: 0.1
  }
}
```

### 季节效果

季节效果可以根据季节改变植被的颜色，模拟四季变化。

```typescript
{
  useSeasonal: true,
  seasonalParams: {
    season: 'autumn',
    intensity: 0.8
  }
}
```

### 颜色变化

颜色变化可以为植被添加随机的颜色变化，增加场景的多样性。

```typescript
{
  colorVariation: {
    enabled: true,
    hue: 0.1,
    saturation: 0.2,
    lightness: 0.2
  }
}
```

## 性能优化

植被系统支持多种性能优化技术，以提高渲染性能。

### 实例化渲染

实例化渲染可以大幅减少渲染调用次数，提高渲染性能。

```typescript
{
  useInstancing: true,
  useGPUInstancing: true
}
```

### LOD（细节层次）

LOD可以根据相机距离调整植被的细节层次，远处的植被使用简化模型，提高渲染性能。

```typescript
{
  useLOD: true,
  lodDistances: [50, 100, 200, 400]
}
```

### 视锥体剔除

视锥体剔除可以剔除不在相机视野内的植被，减少渲染负担。

```typescript
{
  useFrustumCulling: true
}
```

### 八叉树

八叉树可以加速空间查询，提高视锥体剔除和LOD计算的性能。

```typescript
{
  useOctree: true
}
```

## 编辑器使用

在编辑器中使用植被系统非常简单：

1. 创建地形实体
2. 创建植被实体
3. 添加植被组件
4. 配置植被项
5. 点击"生成植被"按钮

编辑器还提供了以下功能：

- 植被预览
- 植被编辑
- 植被导入/导出
- 植被预设

## API参考

详细的API参考请查看[API文档](../api/vegetation.md)。
