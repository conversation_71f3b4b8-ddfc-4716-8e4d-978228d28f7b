# IR引擎编辑器视频教程录制规范

本文档定义了IR引擎编辑器视频教程的录制规范，包括技术规范、视觉规范、内容规范和字幕规范，以确保所有教程保持一致的高质量标准。

## 1. 技术规范

### 1.1 视频规范

| 参数 | 规范 |
|------|------|
| 分辨率 | 1920x1080 (1080p) |
| 宽高比 | 16:9 |
| 帧率 | 30fps |
| 视频编码 | H.264 |
| 容器格式 | MP4 |
| 视频比特率 | 8Mbps (CBR) |
| 关键帧间隔 | 2秒 |
| 色彩空间 | sRGB |

### 1.2 音频规范

| 参数 | 规范 |
|------|------|
| 采样率 | 48kHz |
| 位深度 | 16-bit |
| 声道 | 单声道（旁白）|
| 音频编码 | AAC |
| 音频比特率 | 192Kbps |
| 音量标准 | -14 LUFS，峰值不超过-1dB |
| 背景噪音 | 低于-60dB |

### 1.3 文件命名规范

视频文件命名格式：`[系列代码]-[序号]-[英文短标题].mp4`

例如：
- `E01-editor-basics.mp4`（入门系列-01-编辑器基础）
- `F04-animation-state-machine.mp4`（功能系列-04-动画状态机）

### 1.4 文件存储位置

- 视频文件：`/assets/videos/tutorials/`
- 缩略图：`/assets/images/tutorials/`
- 字幕文件：`/assets/videos/tutorials/subtitles/`
- 练习文件：`/assets/examples/tutorials/`

## 2. 视觉规范

### 2.1 开场和结束画面

#### 开场画面（5秒）
- 显示IR引擎编辑器标志
- 显示教程标题和系列信息
- 显示教程编号和难度级别
- 使用统一的动画过渡效果

#### 结束画面（5秒）
- 显示"感谢观看"文字
- 显示相关教程推荐
- 显示官方网站和社区链接
- 显示版权信息

### 2.2 界面设置

- 编辑器主题：使用默认浅色主题
- 界面缩放：100%
- 字体大小：默认（确保清晰可读）
- 隐藏不必要的面板和工具栏
- 关闭可能分散注意力的通知和弹窗

### 2.3 视觉辅助元素

| 元素 | 规范 |
|------|------|
| 鼠标高亮 | 使用黄色圆圈突出显示鼠标位置 |
| 点击效果 | 使用红色圆圈表示鼠标点击 |
| 界面高亮 | 使用半透明黄色矩形突出显示界面元素 |
| 文字注释 | 使用统一的字体和样式（微软雅黑，白色文字，黑色描边） |
| 箭头指示 | 使用红色箭头指示操作方向或关注点 |
| 放大效果 | 对重要细节使用1.5-2倍放大，持续3-5秒 |

### 2.4 章节标题

- 在每个章节开始时显示章节标题（3秒）
- 使用统一的字体和样式
- 包含章节编号和标题
- 可选显示章节时长

## 3. 内容规范

### 3.1 教程结构

每个教程应包含以下部分：

1. **开场介绍**（30-60秒）
   - 问候语
   - 教程主题和目标
   - 学习成果预期
   - 前置知识要求

2. **主体内容**（按章节组织）
   - 每个章节开始时说明本章节目标
   - 清晰展示每个操作步骤
   - 解释操作的目的和原理
   - 提供替代方法和快捷键

3. **总结**（30-60秒）
   - 回顾学习内容
   - 提供应用建议
   - 指引下一步学习方向
   - 结束语

### 3.2 语音和旁白规范

- 语速：中等，约每分钟150-180个字
- 语调：专业、友好、有活力
- 发音：清晰准确，特别是专业术语
- 停顿：在重要概念前后适当停顿
- 背景：安静的录音环境，无回音和背景噪音

### 3.3 操作演示规范

- 操作节奏：中等，给观众足够时间理解
- 鼠标移动：平滑、有目的，避免快速或无意义的移动
- 复杂操作：使用慢动作或分步骤展示
- 错误处理：展示常见错误及解决方法
- 快捷键：同时在屏幕上显示使用的快捷键

### 3.4 专业术语使用规范

- 首次出现专业术语时提供解释
- 保持术语使用的一致性
- 可在屏幕上显示重要术语的文字说明
- 避免使用过于技术性的行话，除非必要

## 4. 字幕规范

### 4.1 字幕格式

- 使用WebVTT格式（.vtt文件）
- 每行不超过42个汉字或84个英文字符
- 每个字幕块显示时间为1-7秒
- 字幕与音频同步，误差不超过0.3秒

### 4.2 字幕样式

- 字体：微软雅黑（中文）/Arial（英文）
- 大小：视频高度的1/18
- 颜色：白色文字，黑色描边
- 位置：屏幕底部居中
- 背景：半透明黑色背景（可选）

### 4.3 多语言字幕

- 默认提供中文字幕
- 可选提供英文字幕
- 字幕文件命名：`[视频ID]-[语言代码].vtt`
  例如：`E01-editor-basics-zh.vtt`，`E01-editor-basics-en.vtt`

## 5. 质量检查清单

每个视频教程完成后，使用以下清单进行质量检查：

### 5.1 技术质量检查

- [ ] 视频分辨率和帧率符合规范
- [ ] 音频清晰，无背景噪音
- [ ] 视频画面清晰，无模糊或压缩痕迹
- [ ] 音视频同步正常
- [ ] 文件格式和编码符合规范

### 5.2 内容质量检查

- [ ] 教程内容完整，覆盖所有计划主题
- [ ] 操作步骤清晰，易于理解和跟随
- [ ] 解释准确，无技术错误
- [ ] 专业术语使用正确且一致
- [ ] 内容组织逻辑清晰，结构合理

### 5.3 视觉质量检查

- [ ] 开场和结束画面符合规范
- [ ] 视觉辅助元素使用恰当
- [ ] 界面设置符合规范
- [ ] 鼠标操作清晰可见
- [ ] 章节标题和文字注释清晰可读

### 5.4 字幕质量检查

- [ ] 字幕与音频同步
- [ ] 字幕文本准确，无拼写或语法错误
- [ ] 字幕格式和样式符合规范
- [ ] 多语言字幕一致（如适用）

## 6. 录制工具推荐

### 6.1 屏幕录制软件

- OBS Studio（免费，开源）
- Camtasia（付费，功能全面）
- ScreenFlow（Mac，付费）

### 6.2 音频录制设备

- 麦克风：Blue Yeti、Rode NT-USB、Audio-Technica AT2020
- 耳机：监听级封闭式耳机
- 声卡：可选外置USB声卡

### 6.3 后期处理软件

- 视频编辑：Adobe Premiere Pro、DaVinci Resolve
- 音频处理：Audacity（免费）、Adobe Audition
- 字幕制作：Aegisub（免费）、Subtitle Edit
