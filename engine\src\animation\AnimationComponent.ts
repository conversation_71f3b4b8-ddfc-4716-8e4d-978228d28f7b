/**
 * 动画组件
 * 基础动画组件类
 */

import { Component } from '../core/Component';
import { Animator } from './Animator';

export class AnimationComponent extends Component {
  public static readonly type: string = 'AnimationComponent';

  private animator: Animator | null = null;
  private isPlaying: boolean = false;
  private currentClip: string | null = null;

  constructor(entity: any) {
    super(AnimationComponent.type);
    this.setEntity(entity);
  }

  public setAnimator(animator: Animator): void {
    this.animator = animator;
  }

  public getAnimator(): Animator | null {
    return this.animator;
  }

  public play(clipName: string): void {
    if (this.animator) {
      this.animator.play(clipName);
      this.isPlaying = true;
      this.currentClip = clipName;
    }
  }

  public stop(): void {
    if (this.animator) {
      this.animator.stop();
      this.isPlaying = false;
      this.currentClip = null;
    }
  }

  public pause(): void {
    if (this.animator) {
      this.animator.pause();
      this.isPlaying = false;
    }
  }

  public resume(): void {
    if (this.animator) {
      this.animator.resume();
      this.isPlaying = true;
    }
  }

  public update(deltaTime: number): void {
    if (this.animator && this.isPlaying) {
      this.animator.update(deltaTime);
    }
  }

  public getIsPlaying(): boolean {
    return this.isPlaying;
  }

  public getCurrentClip(): string | null {
    return this.currentClip;
  }

  public dispose(): void {
    if (this.animator) {
      this.animator.dispose();
    }
    super.dispose();
  }
}
