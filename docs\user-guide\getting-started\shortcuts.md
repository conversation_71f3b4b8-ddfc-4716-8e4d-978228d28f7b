# 快捷键参考

IR引擎编辑器提供了丰富的快捷键，帮助您提高工作效率。本文档列出了常用的快捷键，按功能分类整理。

> 注意：在macOS上，请将Ctrl替换为Command(⌘)，将Alt替换为Option(⌥)。

## 文件操作

| 快捷键 | 功能 |
|--------|------|
| Ctrl+N | 新建项目 |
| Ctrl+O | 打开项目 |
| Ctrl+S | 保存项目/场景 |
| Ctrl+Shift+S | 另存为 |
| Ctrl+W | 关闭当前场景 |
| Ctrl+Shift+W | 关闭项目 |
| Ctrl+I | 导入资产 |
| Ctrl+E | 导出选中对象 |
| Alt+F4 | 退出编辑器 |

## 编辑操作

| 快捷键 | 功能 |
|--------|------|
| Ctrl+Z | 撤销 |
| Ctrl+Y / Ctrl+Shift+Z | 重做 |
| Ctrl+X | 剪切 |
| Ctrl+C | 复制 |
| Ctrl+V | 粘贴 |
| Ctrl+D | 复制选中对象 |
| Delete | 删除选中对象 |
| Ctrl+A | 选择全部 |
| Esc | 取消选择 |
| F2 | 重命名选中对象 |
| Ctrl+G | 组合选中对象 |
| Ctrl+Shift+G | 解除组合 |
| Ctrl+L | 锁定选中对象 |
| Ctrl+Shift+L | 解锁选中对象 |
| Ctrl+H | 隐藏选中对象 |
| Ctrl+Shift+H | 显示隐藏对象 |

## 视图控制

| 快捷键 | 功能 |
|--------|------|
| F | 聚焦选中对象 |
| Alt+鼠标左键拖动 | 旋转视图 |
| Alt+鼠标中键拖动 / 鼠标中键拖动 | 平移视图 |
| 鼠标滚轮 | 缩放视图 |
| 数字键1 | 前视图 |
| 数字键3 | 右视图 |
| 数字键7 | 顶视图 |
| 数字键5 | 切换透视/正交视图 |
| Ctrl+数字键0 | 切换到场景相机视图 |
| ` (反引号) | 最大化当前视图 |
| Tab | 切换全屏模式 |

## 变换工具

| 快捷键 | 功能 |
|--------|------|
| W | 移动工具 |
| E | 旋转工具 |
| R | 缩放工具 |
| T | 矩形工具 |
| Y | 多变换工具 |
| X | 切换变换空间（世界/局部） |
| V | 切换顶点吸附 |
| Ctrl+Shift+4 | 切换网格吸附 |

## 场景控制

| 快捷键 | 功能 |
|--------|------|
| Ctrl+P | 播放/停止场景 |
| Ctrl+Shift+P | 暂停场景 |
| Ctrl+Alt+P | 单步执行 |
| Ctrl+1 | 正常播放速度 |
| Ctrl+2 | 2倍播放速度 |
| Ctrl+3 | 3倍播放速度 |
| Ctrl+0 | 暂停播放 |

## 面板和窗口

| 快捷键 | 功能 |
|--------|------|
| Ctrl+1 | 显示/隐藏场景面板 |
| Ctrl+2 | 显示/隐藏层次面板 |
| Ctrl+3 | 显示/隐藏属性面板 |
| Ctrl+4 | 显示/隐藏资产面板 |
| Ctrl+5 | 显示/隐藏动画面板 |
| Ctrl+6 | 显示/隐藏控制台面板 |
| Ctrl+7 | 显示/隐藏协作面板 |
| Ctrl+8 | 显示/隐藏性能面板 |
| Ctrl+9 | 显示/隐藏调试面板 |
| Ctrl+0 | 显示/隐藏设置面板 |
| Ctrl+Space | 命令面板 |
| F1 | 帮助 |
| F11 | 全屏模式 |
| Alt+1-9 | 切换工作区布局 |

## 对象创建

| 快捷键 | 功能 |
|--------|------|
| Ctrl+Shift+N | 创建空对象 |
| Ctrl+Alt+C | 创建立方体 |
| Ctrl+Alt+S | 创建球体 |
| Ctrl+Alt+Y | 创建圆柱体 |
| Ctrl+Alt+P | 创建平面 |
| Ctrl+Alt+L | 创建点光源 |
| Ctrl+Alt+D | 创建方向光 |
| Ctrl+Alt+A | 创建相机 |
| Ctrl+Alt+E | 创建空对象作为父级 |

## 组件操作

| 快捷键 | 功能 |
|--------|------|
| Ctrl+Shift+C | 复制组件 |
| Ctrl+Shift+V | 粘贴组件值 |
| Ctrl+Shift+R | 重置组件 |
| Ctrl+Shift+D | 禁用/启用组件 |
| Alt+C | 添加组件 |
| Alt+R | 移除组件 |

## 动画编辑

| 快捷键 | 功能 |
|--------|------|
| K | 在当前帧添加关键帧 |
| I | 在当前帧添加位置关键帧 |
| O | 在当前帧添加旋转关键帧 |
| P | 在当前帧添加缩放关键帧 |
| , (逗号) | 前一帧 |
| . (句号) | 后一帧 |
| Home | 跳到第一帧 |
| End | 跳到最后一帧 |
| Space | 播放/暂停动画 |
| Alt+, | 跳到前一个关键帧 |
| Alt+. | 跳到后一个关键帧 |
| Shift+D | 复制关键帧 |
| Shift+拖动 | 移动关键帧 |

## 材质编辑

| 快捷键 | 功能 |
|--------|------|
| Ctrl+Shift+M | 创建新材质 |
| Ctrl+B | 分配材质到选中对象 |
| Ctrl+Shift+B | 从选中对象提取材质 |
| Ctrl+R | 刷新材质 |
| Ctrl+Shift+R | 重置材质 |

## 协作编辑

| 快捷键 | 功能 |
|--------|------|
| Ctrl+Shift+J | 创建协作会话 |
| Ctrl+J | 加入协作会话 |
| Ctrl+Shift+K | 离开协作会话 |
| Ctrl+K | 打开聊天面板 |
| Ctrl+Shift+U | 显示/隐藏用户列表 |
| Ctrl+Shift+H | 显示/隐藏历史记录 |
| Ctrl+Shift+P | 显示/隐藏权限面板 |

## 调试和性能

| 快捷键 | 功能 |
|--------|------|
| Ctrl+F1 | 显示/隐藏统计信息 |
| Ctrl+F2 | 显示/隐藏性能图表 |
| Ctrl+F3 | 显示/隐藏内存使用 |
| Ctrl+F4 | 显示/隐藏渲染信息 |
| Ctrl+F5 | 开始/停止性能记录 |
| Ctrl+F6 | 显示/隐藏碰撞体 |
| Ctrl+F7 | 显示/隐藏边界框 |
| Ctrl+F8 | 显示/隐藏网格线框 |
| Ctrl+F9 | 显示/隐藏光源指示器 |
| Ctrl+F10 | 显示/隐藏相机视锥 |
| Ctrl+F11 | 重新加载着色器 |
| Ctrl+F12 | 清除缓存 |

## 搜索和导航

| 快捷键 | 功能 |
|--------|------|
| Ctrl+F | 查找对象 |
| Ctrl+Shift+F | 高级查找 |
| Ctrl+G | 跳转到对象 |
| Ctrl+T | 快速打开资产 |
| Ctrl+P | 命令面板 |
| Alt+左箭头 | 导航历史后退 |
| Alt+右箭头 | 导航历史前进 |
| Ctrl+Tab | 切换最近使用的面板 |

## 自定义快捷键

您可以自定义编辑器的快捷键：

1. 点击顶部菜单栏的"编辑 > 首选项"
2. 在首选项对话框中，选择"快捷键"选项卡
3. 找到要修改的命令，双击快捷键列
4. 按下新的快捷键组合
5. 点击"应用"或"确定"保存更改

![自定义快捷键](../../assets/images/customize-shortcuts.png)

## 快捷键冲突

如果快捷键发生冲突，编辑器会显示冲突警告。您可以：

1. 选择保留新快捷键，并清除冲突命令的快捷键
2. 保留现有快捷键，不更改
3. 为两个命令都分配不同的快捷键

## 快捷键备份和恢复

您可以备份和恢复自定义快捷键设置：

1. 在快捷键首选项页面，点击"导出"按钮保存快捷键配置
2. 要恢复，点击"导入"按钮并选择之前保存的配置文件

## 快捷键提示

- 在编辑器中，将鼠标悬停在按钮和控件上可以查看相关快捷键
- 使用命令面板（Ctrl+Space）可以快速访问命令，无需记忆所有快捷键
- 最常用的工具快捷键是W（移动）、E（旋转）和R（缩放）
- 使用F键快速聚焦选中对象
- 使用数字键1、3、7快速切换到标准视图
