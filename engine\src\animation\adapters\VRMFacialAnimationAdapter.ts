/**
 * VRM面部动画适配器
 * 专门用于处理VRM模型的面部动画
 */
import * as THREE from 'three';
import { Entity } from '../../core/Entity';

import { FacialExpressionType, VisemeType } from '../FacialAnimation';
import { FacialAnimationModelAdapterComponent, FacialAnimationModelType } from './FacialAnimationModelAdapter';

/**
 * VRM面部动画适配器配置
 */
export interface VRMFacialAnimationAdapterConfig {
  /** 是否启用调试 */
  debug?: boolean;
  /** 是否自动检测混合形状 */
  autoDetectBlendShapes?: boolean;
}

/**
 * VRM面部动画适配器组件
 */
export class VRMFacialAnimationAdapter extends FacialAnimationModelAdapterComponent {
  /** 组件类型 */
  static readonly type = 'FacialAnimationModelAdapter';

  /** VRM模型 */
  private vrmModel: any = null; // 实际应该使用VRM类型
  /** 混合形状代理 */
  private blendShapeProxy: any = null; // 实际应该使用VRMBlendShapeProxy类型
  /** 是否启用调试 */
  private vrmDebug: boolean;

  /**
   * 构造函数
   * @param entity 实体
   * @param config 配置
   */
  constructor(entity: Entity, config: VRMFacialAnimationAdapterConfig = {}) {
    super(entity, {
      modelType: FacialAnimationModelType.VRM,
      autoDetectBlendShapes: config.autoDetectBlendShapes,
      debug: config.debug
    });

    this.vrmDebug = config.debug || false;
  }

  /**
   * 设置VRM模型
   * @param vrm VRM模型
   */
  public setVRMModel(vrm: any): void {
    this.vrmModel = vrm;

    // 获取混合形状代理
    if (vrm && vrm.blendShapeProxy) {
      this.blendShapeProxy = vrm.blendShapeProxy;
    } else if (vrm && vrm.expressionManager) {
      // VRM 1.0
      this.blendShapeProxy = vrm.expressionManager;
    }

    // 如果有骨骼网格，设置骨骼网格
    if (vrm && vrm.scene) {
      const skinnedMesh = this.findSkinnedMesh(vrm.scene);
      if (skinnedMesh) {
        this.setSkinnedMesh(skinnedMesh);
      }
    }

    if (this.vrmDebug) {
      console.log('VRM模型已设置:', vrm);
      console.log('混合形状代理:', this.blendShapeProxy);
    }
  }

  /**
   * 查找骨骼网格
   * @param object 对象
   * @returns 骨骼网格
   */
  private findSkinnedMesh(object: THREE.Object3D): THREE.SkinnedMesh | null {
    if (object instanceof THREE.SkinnedMesh) {
      return object;
    }

    for (const child of object.children) {
      const result = this.findSkinnedMesh(child);
      if (result) {
        return result;
      }
    }

    return null;
  }

  /**
   * 应用表情
   * @param expression 表情类型
   * @param weight 权重
   * @returns 是否成功应用
   */
  public override applyExpression(expression: FacialExpressionType, weight: number): boolean {
    // 如果有混合形状代理，优先使用混合形状代理
    if (this.blendShapeProxy) {
      const vrmExpression = this.mapExpressionToVRM(expression);
      if (vrmExpression) {
        // VRM 0.x
        if (typeof this.blendShapeProxy.setValue === 'function') {
          this.blendShapeProxy.setValue(vrmExpression, weight);
          return true;
        }
        // VRM 1.0
        else if (typeof this.blendShapeProxy.setWeight === 'function') {
          this.blendShapeProxy.setWeight(vrmExpression, weight);
          return true;
        }
      }
    }

    // 如果没有混合形状代理或者应用失败，使用基类方法
    return super.applyExpression(expression, weight);
  }

  /**
   * 应用口型
   * @param viseme 口型类型
   * @param weight 权重
   * @returns 是否成功应用
   */
  public override applyViseme(viseme: VisemeType, weight: number): boolean {
    // 如果有混合形状代理，优先使用混合形状代理
    if (this.blendShapeProxy) {
      const vrmViseme = this.mapVisemeToVRM(viseme);
      if (vrmViseme) {
        // VRM 0.x
        if (typeof this.blendShapeProxy.setValue === 'function') {
          this.blendShapeProxy.setValue(vrmViseme, weight);
          return true;
        }
        // VRM 1.0
        else if (typeof this.blendShapeProxy.setWeight === 'function') {
          this.blendShapeProxy.setWeight(vrmViseme, weight);
          return true;
        }
      }
    }

    // 如果没有混合形状代理或者应用失败，使用基类方法
    return super.applyViseme(viseme, weight);
  }

  /**
   * 重置所有表情和口型
   */
  public override resetAll(): void {
    // 如果有混合形状代理，优先使用混合形状代理
    if (this.blendShapeProxy) {
      // VRM 0.x
      if (typeof this.blendShapeProxy.setValue === 'function' && typeof this.blendShapeProxy.getBlendShapeKeys === 'function') {
        const keys = this.blendShapeProxy.getBlendShapeKeys();
        for (const key of keys) {
          this.blendShapeProxy.setValue(key, 0);
        }
        return;
      }
      // VRM 1.0
      else if (typeof this.blendShapeProxy.setWeight === 'function' && typeof this.blendShapeProxy.getExpressionNames === 'function') {
        const names = this.blendShapeProxy.getExpressionNames();
        for (const name of names) {
          this.blendShapeProxy.setWeight(name, 0);
        }
        return;
      }
    }

    // 如果没有混合形状代理或者重置失败，使用基类方法
    super.resetAll();
  }

  /**
   * 将表情映射到VRM表情
   * @param expression 表情类型
   * @returns VRM表情名称
   */
  private mapExpressionToVRM(expression: FacialExpressionType): string | null {
    // VRM表情映射
    const expressionMap: { [key in FacialExpressionType]?: string } = {
      [FacialExpressionType.NEUTRAL]: 'neutral',
      [FacialExpressionType.HAPPY]: 'happy',
      [FacialExpressionType.SAD]: 'sad',
      [FacialExpressionType.ANGRY]: 'angry',
      [FacialExpressionType.SURPRISED]: 'surprised',
      [FacialExpressionType.FEARFUL]: 'fear',
      [FacialExpressionType.DISGUSTED]: 'disgust',
      [FacialExpressionType.CONTEMPT]: 'angry'
    };

    return expressionMap[expression] || null;
  }

  /**
   * 将口型映射到VRM表情
   * @param viseme 口型类型
   * @returns VRM表情名称
   */
  private mapVisemeToVRM(viseme: VisemeType): string | null {
    // VRM口型映射
    const visemeMap: { [key in VisemeType]?: string } = {
      [VisemeType.AA]: 'aa',
      [VisemeType.EE]: 'ee',
      [VisemeType.IH]: 'ih',
      [VisemeType.OH]: 'oh',
      [VisemeType.OU]: 'ou',
      [VisemeType.SILENT]: 'neutral'
    };

    return visemeMap[viseme] || null;
  }
}
