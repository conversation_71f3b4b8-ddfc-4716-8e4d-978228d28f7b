/**
 * Cannon.js物理引擎封装
 * 用于物理驱动动画
 */
import * as THREE from 'three';
import * as CANNON from 'cannon-es';
import { EventEmitter } from '../../utils/EventEmitter';

/**
 * 物理对象类型
 */
export enum PhysicsObjectType {
  /** 刚体 */
  RIGID_BODY = 'rigid_body',
  /** 软体 */
  SOFT_BODY = 'soft_body',
  /** 布料 */
  CLOTH = 'cloth',
  /** 绳索 */
  ROPE = 'rope',
  /** 流体 */
  FLUID = 'fluid'
}

/**
 * 物理对象配置
 */
export interface PhysicsObjectConfig {
  /** 对象类型 */
  type: PhysicsObjectType;
  /** 质量 */
  mass?: number;
  /** 位置 */
  position?: THREE.Vector3;
  /** 旋转 */
  quaternion?: THREE.Quaternion;
  /** 形状 */
  shape?: CANNON.Shape;
  /** 材质 */
  material?: CANNON.Material;
  /** 是否固定 */
  isKinematic?: boolean;
  /** 线性阻尼 */
  linearDamping?: number;
  /** 角阻尼 */
  angularDamping?: number;
  /** 碰撞组 */
  collisionGroup?: number;
  /** 碰撞掩码 */
  collisionMask?: number;
  /** 用户数据 */
  userData?: any;
}

/**
 * 物理约束类型
 */
export enum PhysicsConstraintType {
  /** 点对点 */
  POINT_TO_POINT = 'point_to_point',
  /** 铰链 */
  HINGE = 'hinge',
  /** 距离 */
  DISTANCE = 'distance',
  /** 锁定 */
  LOCK = 'lock',
  /** 弹簧 */
  SPRING = 'spring'
}

/**
 * 物理约束配置
 */
export interface PhysicsConstraintConfig {
  /** 约束类型 */
  type: PhysicsConstraintType;
  /** 物体A */
  bodyA: CANNON.Body;
  /** 物体B */
  bodyB: CANNON.Body;
  /** 局部点A */
  pivotA?: CANNON.Vec3;
  /** 局部点B */
  pivotB?: CANNON.Vec3;
  /** 局部轴A */
  axisA?: CANNON.Vec3;
  /** 局部轴B */
  axisB?: CANNON.Vec3;
  /** 最大力 */
  maxForce?: number;
  /** 碰撞启用 */
  collideConnected?: boolean;
  /** 用户数据 */
  userData?: any;
}

/**
 * 物理引擎配置
 */
export interface PhysicsEngineConfig {
  /** 重力 */
  gravity?: CANNON.Vec3;
  /** 默认材质 */
  defaultMaterial?: CANNON.Material;
  /** 是否允许休眠 */
  allowSleep?: boolean;
  /** 迭代次数 */
  iterations?: number;
  /** 是否使用四元数归一化 */
  quatNormalizeFast?: boolean;
  /** 是否使用四元数插值 */
  quatNormalizeSkip?: number;
  /** 是否启用调试 */
  debug?: boolean;
}

/**
 * Cannon.js物理引擎封装
 */
export class CannonPhysicsEngine {
  /** 物理世界 */
  private world: CANNON.World;
  /** 物理对象映射 */
  private bodies: Map<string, CANNON.Body> = new Map();
  /** 物理约束映射 */
  private constraints: Map<string, CANNON.Constraint> = new Map();
  /** 物理材质映射 */
  private materials: Map<string, CANNON.Material> = new Map();
  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();
  /** 是否启用调试 */
  private debug: boolean;
  /** 累积时间 */
  private accumulator: number = 0;
  /** 固定时间步长 */
  private fixedTimeStep: number = 1 / 60;
  /** 最大子步数 */
  private maxSubSteps: number = 10;

  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config: PhysicsEngineConfig = {}) {
    // 创建物理世界
    this.world = new CANNON.World();
    
    // 设置重力
    this.world.gravity.set(
      config.gravity?.x ?? 0,
      config.gravity?.y ?? -9.81,
      config.gravity?.z ?? 0
    );
    
    // 设置求解器迭代次数
    this.world.solver.iterations = config.iterations || 10;
    
    // 设置四元数归一化
    this.world.quatNormalizeFast = config.quatNormalizeFast || false;
    this.world.quatNormalizeSkip = config.quatNormalizeSkip || 0;
    
    // 设置休眠
    this.world.allowSleep = config.allowSleep || false;
    
    // 创建默认材质
    const defaultMaterial = config.defaultMaterial || new CANNON.Material('default');
    this.world.defaultMaterial = defaultMaterial;
    this.materials.set('default', defaultMaterial);
    
    // 设置默认接触材质
    const defaultContactMaterial = new CANNON.ContactMaterial(
      defaultMaterial,
      defaultMaterial,
      {
        friction: 0.3,
        restitution: 0.3
      }
    );
    this.world.addContactMaterial(defaultContactMaterial);
    
    // 设置调试
    this.debug = config.debug || false;
  }

  /**
   * 创建物理对象
   * @param id 对象ID
   * @param config 配置
   * @returns 物理对象
   */
  public createBody(id: string, config: PhysicsObjectConfig): CANNON.Body {
    // 检查是否已存在
    if (this.bodies.has(id)) {
      return this.bodies.get(id)!;
    }
    
    // 创建形状
    const shape = config.shape || new CANNON.Box(new CANNON.Vec3(0.5, 0.5, 0.5));
    
    // 创建材质
    const material = config.material || this.world.defaultMaterial;
    
    // 创建物理对象
    const body = new CANNON.Body({
      mass: config.mass || 0,
      position: new CANNON.Vec3(
        config.position?.x || 0,
        config.position?.y || 0,
        config.position?.z || 0
      ),
      quaternion: new CANNON.Quaternion(
        config.quaternion?.x || 0,
        config.quaternion?.y || 0,
        config.quaternion?.z || 0,
        config.quaternion?.w || 1
      ),
      shape,
      material,
      type: config.isKinematic ? CANNON.BODY_TYPES.KINEMATIC : undefined,
      linearDamping: config.linearDamping || 0.01,
      angularDamping: config.angularDamping || 0.01,
      collisionFilterGroup: config.collisionGroup || 1,
      collisionFilterMask: config.collisionMask || -1
    });
    
    // 设置用户数据
    body.userData = config.userData || {};
    
    // 添加到世界
    this.world.addBody(body);
    
    // 存储物理对象
    this.bodies.set(id, body);
    
    if (this.debug) {
      console.log(`创建物理对象: ${id}`, body);
    }
    
    return body;
  }

  /**
   * 移除物理对象
   * @param id 对象ID
   * @returns 是否成功移除
   */
  public removeBody(id: string): boolean {
    const body = this.bodies.get(id);
    if (!body) return false;
    
    // 从世界中移除
    this.world.removeBody(body);
    
    // 从映射中移除
    this.bodies.delete(id);
    
    if (this.debug) {
      console.log(`移除物理对象: ${id}`);
    }
    
    return true;
  }

  /**
   * 获取物理对象
   * @param id 对象ID
   * @returns 物理对象
   */
  public getBody(id: string): CANNON.Body | null {
    return this.bodies.get(id) || null;
  }

  /**
   * 创建物理约束
   * @param id 约束ID
   * @param config 配置
   * @returns 物理约束
   */
  public createConstraint(id: string, config: PhysicsConstraintConfig): CANNON.Constraint {
    // 检查是否已存在
    if (this.constraints.has(id)) {
      return this.constraints.get(id)!;
    }
    
    // 创建约束
    let constraint: CANNON.Constraint;
    
    switch (config.type) {
      case PhysicsConstraintType.POINT_TO_POINT:
        constraint = new CANNON.PointToPointConstraint(
          config.bodyA,
          config.pivotA || new CANNON.Vec3(),
          config.bodyB,
          config.pivotB || new CANNON.Vec3(),
          config.maxForce || undefined
        );
        break;
      
      case PhysicsConstraintType.HINGE:
        constraint = new CANNON.HingeConstraint(
          config.bodyA,
          config.bodyB,
          {
            pivotA: config.pivotA || new CANNON.Vec3(),
            pivotB: config.pivotB || new CANNON.Vec3(),
            axisA: config.axisA || new CANNON.Vec3(1, 0, 0),
            axisB: config.axisB || new CANNON.Vec3(1, 0, 0),
            maxForce: config.maxForce || undefined,
            collideConnected: config.collideConnected || false
          }
        );
        break;
      
      case PhysicsConstraintType.DISTANCE:
        constraint = new CANNON.DistanceConstraint(
          config.bodyA,
          config.bodyB,
          undefined,
          config.maxForce || undefined
        );
        break;
      
      case PhysicsConstraintType.LOCK:
        constraint = new CANNON.LockConstraint(
          config.bodyA,
          config.bodyB,
          {
            maxForce: config.maxForce || undefined
          }
        );
        break;
      
      case PhysicsConstraintType.SPRING:
        // Spring不是Constraint的子类，需要特殊处理
        const spring = new CANNON.Spring(config.bodyA, config.bodyB, {
          localAnchorA: config.pivotA || new CANNON.Vec3(),
          localAnchorB: config.pivotB || new CANNON.Vec3(),
          restLength: 0,
          stiffness: 50,
          damping: 1
        });
        
        // 添加到更新列表
        this.world.addEventListener('preStep', () => {
          spring.applyForce();
        });
        
        // 创建一个假约束
        constraint = new CANNON.PointToPointConstraint(
          config.bodyA,
          config.pivotA || new CANNON.Vec3(),
          config.bodyB,
          config.pivotB || new CANNON.Vec3(),
          0 // 不施加约束力
        );
        
        // 禁用约束
        constraint.enable = false;
        
        break;
      
      default:
        throw new Error(`不支持的约束类型: ${config.type}`);
    }
    
    // 设置用户数据
    (constraint as any).userData = config.userData || {};
    
    // 添加到世界
    this.world.addConstraint(constraint);
    
    // 存储约束
    this.constraints.set(id, constraint);
    
    if (this.debug) {
      console.log(`创建物理约束: ${id}`, constraint);
    }
    
    return constraint;
  }

  /**
   * 移除物理约束
   * @param id 约束ID
   * @returns 是否成功移除
   */
  public removeConstraint(id: string): boolean {
    const constraint = this.constraints.get(id);
    if (!constraint) return false;
    
    // 从世界中移除
    this.world.removeConstraint(constraint);
    
    // 从映射中移除
    this.constraints.delete(id);
    
    if (this.debug) {
      console.log(`移除物理约束: ${id}`);
    }
    
    return true;
  }

  /**
   * 获取物理约束
   * @param id 约束ID
   * @returns 物理约束
   */
  public getConstraint(id: string): CANNON.Constraint | null {
    return this.constraints.get(id) || null;
  }

  /**
   * 创建物理材质
   * @param id 材质ID
   * @param friction 摩擦系数
   * @param restitution 弹性系数
   * @returns 物理材质
   */
  public createMaterial(id: string, friction: number = 0.3, restitution: number = 0.3): CANNON.Material {
    // 检查是否已存在
    if (this.materials.has(id)) {
      return this.materials.get(id)!;
    }
    
    // 创建材质
    const material = new CANNON.Material(id);
    
    // 创建接触材质
    const contactMaterial = new CANNON.ContactMaterial(
      material,
      this.world.defaultMaterial,
      {
        friction,
        restitution
      }
    );
    
    // 添加到世界
    this.world.addContactMaterial(contactMaterial);
    
    // 存储材质
    this.materials.set(id, material);
    
    if (this.debug) {
      console.log(`创建物理材质: ${id}`, material);
    }
    
    return material;
  }

  /**
   * 获取物理材质
   * @param id 材质ID
   * @returns 物理材质
   */
  public getMaterial(id: string): CANNON.Material | null {
    return this.materials.get(id) || null;
  }

  /**
   * 更新物理世界
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    // 使用固定时间步长
    this.accumulator += deltaTime;
    
    // 限制累积时间，防止时间步长过大
    if (this.accumulator > this.fixedTimeStep * this.maxSubSteps) {
      this.accumulator = this.fixedTimeStep * this.maxSubSteps;
    }
    
    // 计算子步数
    const subSteps = Math.floor(this.accumulator / this.fixedTimeStep);
    
    // 更新物理世界
    this.world.step(this.fixedTimeStep, deltaTime, Math.min(subSteps, this.maxSubSteps));
    
    // 更新累积时间
    this.accumulator -= subSteps * this.fixedTimeStep;
    
    // 发送更新事件
    this.eventEmitter.emit('update', { deltaTime });
  }

  /**
   * 添加事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  public addEventListener(event: string, callback: (data: any) => void): void {
    this.eventEmitter.on(event, callback);
  }

  /**
   * 移除事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  public removeEventListener(event: string, callback: (data: any) => void): void {
    this.eventEmitter.off(event, callback);
  }

  /**
   * 获取物理世界
   * @returns 物理世界
   */
  public getWorld(): CANNON.World {
    return this.world;
  }

  /**
   * 清理物理世界
   */
  public clear(): void {
    // 移除所有约束
    for (const id of this.constraints.keys()) {
      this.removeConstraint(id);
    }
    
    // 移除所有物体
    for (const id of this.bodies.keys()) {
      this.removeBody(id);
    }
    
    // 清空材质映射
    this.materials.clear();
    
    // 重新创建默认材质
    const defaultMaterial = new CANNON.Material('default');
    this.world.defaultMaterial = defaultMaterial;
    this.materials.set('default', defaultMaterial);
    
    if (this.debug) {
      console.log('物理世界已清理');
    }
  }

  /**
   * 销毁物理引擎
   */
  public dispose(): void {
    this.clear();
    this.eventEmitter.removeAllListeners();
    
    if (this.debug) {
      console.log('物理引擎已销毁');
    }
  }
}
