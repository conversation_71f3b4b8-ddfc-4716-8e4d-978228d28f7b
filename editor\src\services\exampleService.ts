/**
 * 示例项目服务
 * 提供示例项目的获取、导入、导出等功能
 */
import axios from 'axios';
import { message } from 'antd';
import { Example, ExampleCategory } from '../types/example';

/**
 * 获取示例项目列表
 * @returns 示例项目列表
 */
export const fetchExamples = async (): Promise<Example[]> => {
  try {
    // TODO: 替换为实际的API调用
    // const response = await axios.get('/api/examples');
    // return response.data;

    // 模拟数据
    return mockExamples;
  } catch (error) {
    console.error('获取示例项目失败:', error);
    message.error('获取示例项目失败，请稍后重试');
    return [];
  }
};

/**
 * 获取示例项目详情
 * @param id 示例项目ID
 * @returns 示例项目详情
 */
export const fetchExampleById = async (id: string): Promise<Example | null> => {
  try {
    // TODO: 替换为实际的API调用
    // const response = await axios.get(`/api/examples/${id}`);
    // return response.data;

    // 模拟数据
    const example = mockExamples.find(example => example.id === id);
    return example || null;
  } catch (error) {
    console.error('获取示例项目详情失败:', error);
    message.error('获取示例项目详情失败，请稍后重试');
    return null;
  }
};

/**
 * 获取示例项目标签列表
 * @returns 标签列表
 */
export const fetchExampleTags = async (): Promise<string[]> => {
  try {
    // TODO: 替换为实际的API调用
    // const response = await axios.get('/api/examples/tags');
    // return response.data;

    // 模拟数据
    const tags = new Set<string>();
    mockExamples.forEach(example => {
      example.tags.forEach(tag => tags.add(tag));
    });
    return Array.from(tags);
  } catch (error) {
    console.error('获取标签列表失败:', error);
    message.error('获取标签列表失败，请稍后重试');
    return [];
  }
};

/**
 * 导入示例项目
 * @param exampleId 示例项目ID
 * @param projectName 项目名称
 * @param workspaceId 工作空间ID
 * @param options 导入选项
 * @returns 导入结果
 */
export const importExample = async (
  exampleId: string,
  projectName: string,
  workspaceId: string,
  options: {
    importOption: 'copy' | 'reference';
    includeAssets: boolean;
  }
): Promise<boolean> => {
  try {
    // TODO: 替换为实际的API调用
    // const response = await axios.post('/api/examples/import', {
    //   exampleId,
    //   projectName,
    //   workspaceId,
    //   options
    // });
    // return response.data.success;

    // 模拟导入
    console.log('导入示例项目:', {
      exampleId,
      projectName,
      workspaceId,
      options
    });
    return true;
  } catch (error) {
    console.error('导入示例项目失败:', error);
    message.error('导入示例项目失败，请稍后重试');
    return false;
  }
};

/**
 * 导出项目为示例项目
 * @param projectId 项目ID
 * @param exampleInfo 示例项目信息
 * @returns 导出结果
 */
export const exportAsExample = async (
  projectId: string,
  exampleInfo: {
    title: string;
    description: string;
    category: ExampleCategory;
    tags: string[];
    previewImage: File;
  }
): Promise<boolean> => {
  try {
    // TODO: 替换为实际的API调用
    // const formData = new FormData();
    // formData.append('projectId', projectId);
    // formData.append('title', exampleInfo.title);
    // formData.append('description', exampleInfo.description);
    // formData.append('category', exampleInfo.category);
    // formData.append('tags', JSON.stringify(exampleInfo.tags));
    // formData.append('previewImage', exampleInfo.previewImage);
    // 
    // const response = await axios.post('/api/examples/export', formData, {
    //   headers: {
    //     'Content-Type': 'multipart/form-data'
    //   }
    // });
    // return response.data.success;

    // 模拟导出
    console.log('导出为示例项目:', {
      projectId,
      exampleInfo
    });
    return true;
  } catch (error) {
    console.error('导出为示例项目失败:', error);
    message.error('导出为示例项目失败，请稍后重试');
    return false;
  }
};

/**
 * 收藏示例项目
 * @param exampleId 示例项目ID
 * @param favorited 是否收藏
 * @returns 操作结果
 */
export const favoriteExample = async (
  exampleId: string,
  favorited: boolean
): Promise<boolean> => {
  try {
    // TODO: 替换为实际的API调用
    // const response = await axios.post('/api/examples/favorite', {
    //   exampleId,
    //   favorited
    // });
    // return response.data.success;

    // 模拟收藏
    console.log('收藏示例项目:', {
      exampleId,
      favorited
    });
    return true;
  } catch (error) {
    console.error('收藏示例项目失败:', error);
    message.error('收藏示例项目失败，请稍后重试');
    return false;
  }
};

// 模拟数据
const mockExamples: Example[] = [
  {
    id: '1',
    title: '基础编辑器功能演示',
    description: '展示编辑器的基本功能，包括场景创建、对象操作和属性编辑。',
    category: 'basic',
    tags: ['基础功能', '编辑器', '入门'],
    previewImage: '/examples/assets/images/previews/editor-basics.jpg',
    images: [
      '/examples/assets/images/previews/editor-basics.jpg',
      '/examples/assets/images/previews/editor-basics-2.jpg',
      '/examples/assets/images/previews/editor-basics-3.jpg',
    ],
    author: 'IR引擎团队',
    createdAt: '2023-01-15',
    updatedAt: '2023-03-20',
    popularity: 128,
    difficulty: 'beginner',
    content: `
# 基础编辑器功能演示

## 简介

本示例项目展示了IR引擎编辑器的基本功能，包括场景创建、对象操作、属性编辑、材质设置和灯光调整。通过本示例，您可以快速了解和掌握编辑器的核心功能和基本操作流程。

## 功能特性

- **场景创建与管理**：创建新场景、设置场景属性、保存和加载场景
- **对象操作**：选择、移动、旋转和缩放3D对象
- **属性编辑**：通过属性面板修改对象的各种属性
- **材质编辑**：创建和编辑基本材质属性
- **灯光设置**：添加和调整不同类型的灯光
- **相机控制**：设置和调整场景相机

## 使用说明

1. 打开示例项目
2. 按照界面上的步骤指引操作
3. 使用鼠标左键选择对象
4. 使用鼠标右键拖动旋转视图
5. 使用键盘快捷键进行操作：
   - W: 移动工具
   - E: 旋转工具
   - R: 缩放工具
6. 在右侧属性面板中修改选中对象的属性
7. 尝试修改材质和灯光设置
    `,
    features: [
      { title: '场景创建与管理', description: '创建新场景、设置场景属性、保存和加载场景' },
      { title: '对象操作', description: '选择、移动、旋转和缩放3D对象' },
      { title: '属性编辑', description: '通过属性面板修改对象的各种属性' },
      { title: '材质编辑', description: '创建和编辑基本材质属性' },
      { title: '灯光设置', description: '添加和调整不同类型的灯光' },
      { title: '相机控制', description: '设置和调整场景相机' },
    ],
    files: [
      { name: 'index.html', size: '2.5KB', updatedAt: '2023-03-20' },
      { name: 'README.md', size: '4.2KB', updatedAt: '2023-03-20' },
      { name: 'scripts/main.js', size: '8.7KB', updatedAt: '2023-03-20' },
      { name: 'styles/main.css', size: '1.8KB', updatedAt: '2023-03-20' },
      { name: 'scenes/main.json', size: '12.4KB', updatedAt: '2023-03-20' },
    ],
    tutorials: [
      { title: '创建第一个场景', description: '学习如何创建和设置基本场景' },
      { title: '添加和操作对象', description: '学习如何添加对象并进行基本操作' },
      { title: '设置材质和灯光', description: '学习如何设置材质属性和添加灯光' },
    ],
    requirements: {
      engineVersion: '1.0.0+',
      editorVersion: '1.0.0+',
      dependencies: [],
    },
    relatedExamples: [
      {
        id: '2',
        title: '材质编辑器演示',
        description: '展示如何创建和编辑各种材质类型。',
        previewImage: '/examples/assets/images/previews/material-editor.jpg',
      },
      {
        id: '3',
        title: '动画系统演示',
        description: '展示关键帧动画、骨骼动画和动画混合功能。',
        previewImage: '/examples/assets/images/previews/animation-demo.jpg',
      },
    ],
    previewUrl: '/examples/editor-basics/index.html',
    favorited: false,
  },
  // 更多示例项目...
];
