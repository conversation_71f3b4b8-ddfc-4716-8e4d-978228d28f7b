<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
  <style>
    .box {
      fill: #f0f0f0;
      stroke: #333;
      stroke-width: 2;
      rx: 10;
      ry: 10;
    }
    .core-box {
      fill: #e6f7ff;
      stroke: #1890ff;
      stroke-width: 2;
      rx: 10;
      ry: 10;
    }
    .rendering-box {
      fill: #fff0f6;
      stroke: #eb2f96;
      stroke-width: 2;
      rx: 10;
      ry: 10;
    }
    .physics-box {
      fill: #f6ffed;
      stroke: #52c41a;
      stroke-width: 2;
      rx: 10;
      ry: 10;
    }
    .animation-box {
      fill: #fffbe6;
      stroke: #faad14;
      stroke-width: 2;
      rx: 10;
      ry: 10;
    }
    .audio-box {
      fill: #f9f0ff;
      stroke: #722ed1;
      stroke-width: 2;
      rx: 10;
      ry: 10;
    }
    .ui-box {
      fill: #e6fffb;
      stroke: #13c2c2;
      stroke-width: 2;
      rx: 10;
      ry: 10;
    }
    .network-box {
      fill: #fcffe6;
      stroke: #a0d911;
      stroke-width: 2;
      rx: 10;
      ry: 10;
    }
    .arrow {
      stroke: #666;
      stroke-width: 2;
      marker-end: url(#arrowhead);
    }
    .label {
      font-family: 'Arial', sans-serif;
      font-size: 14px;
      text-anchor: middle;
      dominant-baseline: middle;
    }
    .title {
      font-family: 'Arial', sans-serif;
      font-size: 24px;
      font-weight: bold;
      text-anchor: middle;
    }
    .subtitle {
      font-family: 'Arial', sans-serif;
      font-size: 16px;
      text-anchor: middle;
    }
    .module-title {
      font-family: 'Arial', sans-serif;
      font-size: 16px;
      font-weight: bold;
      text-anchor: middle;
      dominant-baseline: middle;
    }
    .component {
      font-family: 'Arial', sans-serif;
      font-size: 12px;
      text-anchor: middle;
      dominant-baseline: middle;
    }
  </style>
  
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#666" />
    </marker>
  </defs>
  
  <!-- Title -->
  <text x="400" y="40" class="title">IR引擎架构</text>
  <text x="400" y="70" class="subtitle">核心系统和模块关系图</text>
  
  <!-- Core Engine -->
  <rect x="300" y="100" width="200" height="100" class="core-box" />
  <text x="400" y="150" class="module-title">核心引擎</text>
  
  <!-- Core Components -->
  <rect x="320" y="120" width="70" height="25" class="box" />
  <text x="355" y="132" class="component">ECS系统</text>
  
  <rect x="410" y="120" width="70" height="25" class="box" />
  <text x="445" y="132" class="component">事件系统</text>
  
  <rect x="320" y="155" width="70" height="25" class="box" />
  <text x="355" y="167" class="component">资源管理</text>
  
  <rect x="410" y="155" width="70" height="25" class="box" />
  <text x="445" y="167" class="component">时间系统</text>
  
  <!-- Rendering System -->
  <rect x="550" y="150" width="150" height="100" class="rendering-box" />
  <text x="625" y="170" class="module-title">渲染系统</text>
  
  <rect x="570" y="190" width="50" height="20" class="box" />
  <text x="595" y="200" class="component">材质</text>
  
  <rect x="630" y="190" width="50" height="20" class="box" />
  <text x="655" y="200" class="component">着色器</text>
  
  <rect x="570" y="220" width="50" height="20" class="box" />
  <text x="595" y="230" class="component">光照</text>
  
  <rect x="630" y="220" width="50" height="20" class="box" />
  <text x="655" y="230" class="component">相机</text>
  
  <!-- Physics System -->
  <rect x="550" y="300" width="150" height="100" class="physics-box" />
  <text x="625" y="320" class="module-title">物理系统</text>
  
  <rect x="570" y="340" width="50" height="20" class="box" />
  <text x="595" y="350" class="component">碰撞检测</text>
  
  <rect x="630" y="340" width="50" height="20" class="box" />
  <text x="655" y="350" class="component">刚体</text>
  
  <rect x="570" y="370" width="50" height="20" class="box" />
  <text x="595" y="380" class="component">关节</text>
  
  <rect x="630" y="370" width="50" height="20" class="box" />
  <text x="655" y="380" class="component">软体</text>
  
  <!-- Animation System -->
  <rect x="550" y="450" width="150" height="100" class="animation-box" />
  <text x="625" y="470" class="module-title">动画系统</text>
  
  <rect x="570" y="490" width="50" height="20" class="box" />
  <text x="595" y="500" class="component">骨骼动画</text>
  
  <rect x="630" y="490" width="50" height="20" class="box" />
  <text x="655" y="500" class="component">混合树</text>
  
  <rect x="570" y="520" width="50" height="20" class="box" />
  <text x="595" y="530" class="component">状态机</text>
  
  <rect x="630" y="520" width="50" height="20" class="box" />
  <text x="655" y="530" class="component">IK系统</text>
  
  <!-- Audio System -->
  <rect x="100" y="150" width="150" height="100" class="audio-box" />
  <text x="175" y="170" class="module-title">音频系统</text>
  
  <rect x="120" y="190" width="50" height="20" class="box" />
  <text x="145" y="200" class="component">音源</text>
  
  <rect x="180" y="190" width="50" height="20" class="box" />
  <text x="205" y="200" class="component">音效</text>
  
  <rect x="120" y="220" width="50" height="20" class="box" />
  <text x="145" y="230" class="component">混音器</text>
  
  <rect x="180" y="220" width="50" height="20" class="box" />
  <text x="205" y="230" class="component">3D音频</text>
  
  <!-- UI System -->
  <rect x="100" y="300" width="150" height="100" class="ui-box" />
  <text x="175" y="320" class="module-title">UI系统</text>
  
  <rect x="120" y="340" width="50" height="20" class="box" />
  <text x="145" y="350" class="component">布局</text>
  
  <rect x="180" y="340" width="50" height="20" class="box" />
  <text x="205" y="350" class="component">控件</text>
  
  <rect x="120" y="370" width="50" height="20" class="box" />
  <text x="145" y="380" class="component">事件</text>
  
  <rect x="180" y="370" width="50" height="20" class="box" />
  <text x="205" y="380" class="component">动画</text>
  
  <!-- Network System -->
  <rect x="100" y="450" width="150" height="100" class="network-box" />
  <text x="175" y="470" class="module-title">网络系统</text>
  
  <rect x="120" y="490" width="50" height="20" class="box" />
  <text x="145" y="500" class="component">同步</text>
  
  <rect x="180" y="490" width="50" height="20" class="box" />
  <text x="205" y="500" class="component">消息</text>
  
  <rect x="120" y="520" width="50" height="20" class="box" />
  <text x="145" y="530" class="component">RPC</text>
  
  <rect x="180" y="520" width="50" height="20" class="box" />
  <text x="205" y="530" class="component">房间</text>
  
  <!-- Connections -->
  <line x1="400" y1="200" x2="400" y2="300" class="arrow" />
  <text x="410" y="250" class="label">控制流</text>
  
  <line x1="500" y1="150" x2="550" y2="150" class="arrow" />
  <line x1="500" y1="150" x2="550" y2="300" class="arrow" />
  <line x1="500" y1="150" x2="550" y2="450" class="arrow" />
  
  <line x1="300" y1="150" x2="250" y2="150" class="arrow" />
  <line x1="300" y1="150" x2="250" y2="300" class="arrow" />
  <line x1="300" y1="150" x2="250" y2="450" class="arrow" />
  
  <!-- Input System -->
  <rect x="300" y="300" width="200" height="100" class="box" />
  <text x="400" y="320" class="module-title">输入系统</text>
  
  <rect x="320" y="340" width="70" height="25" class="box" />
  <text x="355" y="352" class="component">键盘/鼠标</text>
  
  <rect x="410" y="340" width="70" height="25" class="box" />
  <text x="445" y="352" class="component">触摸</text>
  
  <rect x="320" y="375" width="70" height="25" class="box" />
  <text x="355" y="387" class="component">手柄</text>
  
  <rect x="410" y="375" width="70" height="25" class="box" />
  <text x="445" y="387" class="component">VR控制器</text>
  
  <!-- Scripting System -->
  <rect x="300" y="450" width="200" height="100" class="box" />
  <text x="400" y="470" class="module-title">脚本系统</text>
  
  <rect x="320" y="490" width="70" height="25" class="box" />
  <text x="355" y="502" class="component">JavaScript</text>
  
  <rect x="410" y="490" width="70" height="25" class="box" />
  <text x="445" y="502" class="component">TypeScript</text>
  
  <rect x="320" y="525" width="70" height="25" class="box" />
  <text x="355" y="537" class="component">视觉脚本</text>
  
  <rect x="410" y="525" width="70" height="25" class="box" />
  <text x="445" y="537" class="component">热重载</text>
  
  <!-- Connections to Input and Scripting -->
  <line x1="400" y1="400" x2="400" y2="450" class="arrow" />
  <line x1="500" y1="350" x2="550" y2="350" class="arrow" />
  <line x1="500" y1="500" x2="550" y2="500" class="arrow" />
  <line x1="300" y1="350" x2="250" y2="350" class="arrow" />
  <line x1="300" y1="500" x2="250" y2="500" class="arrow" />
</svg>
