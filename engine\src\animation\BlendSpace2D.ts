/**
 * 二维混合空间
 * 用于在二维参数空间中混合多个动画
 */
import * as THREE from 'three';
import { AnimationClip } from './AnimationClip';

/**
 * 混合空间节点
 */
export interface BlendSpaceNode {
  /** 动画片段 */
  clip: AnimationClip;
  /** 位置 */
  position: THREE.Vector2;
  /** 权重 */
  weight: number;
  /** 用户数据 */
  userData?: any;
}

/**
 * 混合空间配置
 */
export interface BlendSpace2DConfig {
  /** X轴最小值 */
  minX: number;
  /** X轴最大值 */
  maxX: number;
  /** Y轴最小值 */
  minY: number;
  /** Y轴最大值 */
  maxY: number;
  /** 是否规范化输入值 */
  normalizeInput?: boolean;
  /** 是否使用三角形混合 */
  useTriangulation?: boolean;
}

/**
 * 二维混合空间
 */
export class BlendSpace2D {
  /** 配置 */
  private config: BlendSpace2DConfig;
  /** 节点列表 */
  private nodes: BlendSpaceNode[] = [];
  /** 当前位置 */
  private position: THREE.Vector2 = new THREE.Vector2();
  /** 临时向量 */
  private tempVec2: THREE.Vector2 = new THREE.Vector2();
  /** 三角形列表（用于三角形混合） */
  private triangles: number[][] = [];
  /** 是否已三角化 */
  private triangulated: boolean = false;

  /**
   * 构造函数
   * @param config 混合空间配置
   */
  constructor(config: BlendSpace2DConfig) {
    this.config = {
      minX: config.minX,
      maxX: config.maxX,
      minY: config.minY,
      maxY: config.maxY,
      normalizeInput: config.normalizeInput !== undefined ? config.normalizeInput : true,
      useTriangulation: config.useTriangulation !== undefined ? config.useTriangulation : true
    };
  }

  /**
   * 添加节点
   * @param clip 动画片段
   * @param position 位置
   * @param userData 用户数据
   * @returns 添加的节点
   */
  public addNode(clip: AnimationClip, position: THREE.Vector2, userData?: any): BlendSpaceNode {
    // 检查位置是否在范围内
    if (
      position.x < this.config.minX || position.x > this.config.maxX ||
      position.y < this.config.minY || position.y > this.config.maxY
    ) {
      console.warn('BlendSpace2D: 节点位置超出范围', position);
    }

    const node: BlendSpaceNode = {
      clip,
      position: position.clone(),
      weight: 0,
      userData
    };

    this.nodes.push(node);
    this.triangulated = false;

    return node;
  }

  /**
   * 移除节点
   * @param node 要移除的节点
   * @returns 是否成功移除
   */
  public removeNode(node: BlendSpaceNode): boolean {
    const index = this.nodes.indexOf(node);
    if (index === -1) return false;

    this.nodes.splice(index, 1);
    this.triangulated = false;
    return true;
  }

  /**
   * 设置当前位置
   * @param x X坐标
   * @param y Y坐标
   */
  public setPosition(x: number, y: number): void {
    // 限制在范围内
    const clampedX = Math.max(this.config.minX, Math.min(this.config.maxX, x));
    const clampedY = Math.max(this.config.minY, Math.min(this.config.maxY, y));
    this.position.set(clampedX, clampedY);
  }

  /**
   * 获取当前位置
   * @returns 当前位置
   */
  public getPosition(): THREE.Vector2 {
    return this.position.clone();
  }

  /**
   * 更新混合权重
   */
  public update(): void {
    // 如果没有节点，则不更新
    if (this.nodes.length === 0) return;

    // 如果只有一个节点，则权重为1
    if (this.nodes.length === 1) {
      this.nodes[0].weight = 1;
      return;
    }

    // 如果使用三角形混合且有足够的节点
    if (this.config.useTriangulation && this.nodes.length >= 3) {
      this.updateTriangulationWeights();
    } else {
      this.updateDistanceWeights();
    }
  }

  /**
   * 使用三角形混合更新权重
   */
  private updateTriangulationWeights(): void {
    // 如果还没有三角化，则进行三角化
    if (!this.triangulated) {
      this.triangulate();
    }

    // 重置所有节点的权重
    for (const node of this.nodes) {
      node.weight = 0;
    }

    // 找到包含当前位置的三角形
    for (const triangle of this.triangles) {
      const a = this.nodes[triangle[0]].position;
      const b = this.nodes[triangle[1]].position;
      const c = this.nodes[triangle[2]].position;

      // 检查点是否在三角形内
      if (this.pointInTriangle(this.position, a, b, c)) {
        // 计算重心坐标
        const weights = this.calculateBarycentricWeights(this.position, a, b, c);

        // 设置节点权重
        this.nodes[triangle[0]].weight = weights.x;
        this.nodes[triangle[1]].weight = weights.y;
        this.nodes[triangle[2]].weight = weights.z;
        break;
      }
    }
  }

  /**
   * 使用距离加权更新权重
   */
  private updateDistanceWeights(): void {
    // 计算到每个节点的距离
    const distances: number[] = [];
    let totalWeight = 0;

    for (const node of this.nodes) {
      const distance = this.position.distanceTo(node.position);
      // 使用距离的倒数作为权重
      const weight = distance < 0.0001 ? 1000000 : 1 / distance;
      distances.push(weight);
      totalWeight += weight;
    }

    // 归一化权重
    for (let i = 0; i < this.nodes.length; i++) {
      this.nodes[i].weight = distances[i] / totalWeight;
    }
  }

  /**
   * 三角化节点
   */
  private triangulate(): void {
    // 简单的三角化算法
    // 注意：这是一个简化的实现，实际应用中可能需要更复杂的算法
    this.triangles = [];

    // 如果节点数量少于3，无法三角化
    if (this.nodes.length < 3) return;

    // 找到一个初始三角形
    this.triangles.push([0, 1, 2]);

    // 对于剩余的点，尝试添加到三角形网格中
    for (let i = 3; i < this.nodes.length; i++) {
      const newTriangles: number[][] = [];

      // 检查每个现有三角形
      for (const triangle of this.triangles) {
        const a = this.nodes[triangle[0]].position;
        const b = this.nodes[triangle[1]].position;
        const c = this.nodes[triangle[2]].position;

        // 如果点在三角形内部，则分割三角形
        if (this.pointInTriangle(this.nodes[i].position, a, b, c)) {
          newTriangles.push([triangle[0], triangle[1], i]);
          newTriangles.push([triangle[1], triangle[2], i]);
          newTriangles.push([triangle[2], triangle[0], i]);
        } else {
          // 否则保留原三角形
          newTriangles.push(triangle);
        }
      }

      this.triangles = newTriangles;
    }

    this.triangulated = true;
  }

  /**
   * 检查点是否在三角形内
   * @param p 点
   * @param a 三角形顶点A
   * @param b 三角形顶点B
   * @param c 三角形顶点C
   * @returns 是否在三角形内
   */
  private pointInTriangle(p: THREE.Vector2, a: THREE.Vector2, b: THREE.Vector2, c: THREE.Vector2): boolean {
    const v0 = new THREE.Vector2().subVectors(c, a);
    const v1 = new THREE.Vector2().subVectors(b, a);
    const v2 = new THREE.Vector2().subVectors(p, a);

    const dot00 = v0.dot(v0);
    const dot01 = v0.dot(v1);
    const dot02 = v0.dot(v2);
    const dot11 = v1.dot(v1);
    const dot12 = v1.dot(v2);

    const invDenom = 1 / (dot00 * dot11 - dot01 * dot01);
    const u = (dot11 * dot02 - dot01 * dot12) * invDenom;
    const v = (dot00 * dot12 - dot01 * dot02) * invDenom;

    return (u >= 0) && (v >= 0) && (u + v <= 1);
  }

  /**
   * 计算重心坐标
   * @param p 点
   * @param a 三角形顶点A
   * @param b 三角形顶点B
   * @param c 三角形顶点C
   * @returns 重心坐标 (u, v, w)
   */
  private calculateBarycentricWeights(p: THREE.Vector2, a: THREE.Vector2, b: THREE.Vector2, c: THREE.Vector2): THREE.Vector3 {
    const v0 = new THREE.Vector2().subVectors(b, a);
    const v1 = new THREE.Vector2().subVectors(c, a);
    const v2 = new THREE.Vector2().subVectors(p, a);

    const d00 = v0.dot(v0);
    const d01 = v0.dot(v1);
    const d11 = v1.dot(v1);
    const d20 = v2.dot(v0);
    const d21 = v2.dot(v1);

    const denom = d00 * d11 - d01 * d01;
    const v = (d11 * d20 - d01 * d21) / denom;
    const w = (d00 * d21 - d01 * d20) / denom;
    const u = 1.0 - v - w;

    return new THREE.Vector3(u, v, w);
  }

  /**
   * 获取所有节点
   * @returns 节点列表
   */
  public getNodes(): BlendSpaceNode[] {
    return this.nodes;
  }

  /**
   * 获取活跃节点（权重大于0的节点）
   * @returns 活跃节点列表
   */
  public getActiveNodes(): BlendSpaceNode[] {
    return this.nodes.filter(node => node.weight > 0);
  }
}
