/**
 * 动画系统
 * 管理和更新场景中的所有动画
 */

import * as THREE from 'three';
import { System } from '../core/System';
import { Entity } from '../core/Entity';
import { World } from '../core/World';
import { AnimationClip } from './AnimationClip';
import { Animator } from './Animator';
import { EventEmitter } from '../utils/EventEmitter';
import { Time } from '../utils/Time';

/**
 * 动画系统配置
 */
export interface AnimationSystemConfig {
  /** 是否启用 */
  enabled?: boolean;
  /** 是否启用调试 */
  debug?: boolean;
  /** 更新频率（毫秒） */
  updateFrequency?: number;
  /** 是否使用缓存 */
  useCache?: boolean;
  /** 缓存大小 */
  cacheSize?: number;
  /** 是否使用对象池 */
  useObjectPool?: boolean;
  /** 对象池大小 */
  objectPoolSize?: number;
  /** 是否使用批处理 */
  useBatchProcessing?: boolean;
  /** 批处理大小 */
  batchSize?: number;
  /** 是否使用GPU加速 */
  useGPUAcceleration?: boolean;
  /** 是否使用工作线程 */
  useWorker?: boolean;
}

/**
 * 动画系统
 * 管理和更新场景中的所有动画
 */
export class AnimationSystem extends System {
  /** 系统名称 */
  public static readonly systemName = 'AnimationSystem';
  
  /** 配置 */
  private config: AnimationSystemConfig;
  
  /** 动画控制器映射 */
  private animators: Map<Entity, Animator> = new Map();
  
  /** 动画混合器映射 */
  private mixers: Map<Entity, THREE.AnimationMixer> = new Map();
  
  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();
  
  /** 上次更新时间 */
  private lastUpdateTime: number = 0;
  
  /** 动画缓存 */
  private clipCache: Map<string, AnimationClip> = new Map();
  
  /** 对象池 */
  private objectPool: {
    keyframes: THREE.KeyframeTrack[];
    clips: AnimationClip[];
    mixers: THREE.AnimationMixer[];
  } = {
    keyframes: [],
    clips: [],
    mixers: []
  };
  
  /** 性能统计 */
  private stats = {
    updateTime: 0,
    activeAnimators: 0,
    activeMixers: 0,
    cacheHits: 0,
    cacheMisses: 0,
    objectPoolHits: 0,
    objectPoolMisses: 0
  };
  
  /**
   * 构造函数
   * @param world 世界
   * @param config 配置
   */
  constructor(world: World, config: AnimationSystemConfig = {}) {
    super(world);
    
    // 设置配置
    this.config = {
      enabled: config.enabled !== undefined ? config.enabled : true,
      debug: config.debug || false,
      updateFrequency: config.updateFrequency || 16, // 约60fps
      useCache: config.useCache !== undefined ? config.useCache : true,
      cacheSize: config.cacheSize || 100,
      useObjectPool: config.useObjectPool !== undefined ? config.useObjectPool : true,
      objectPoolSize: config.objectPoolSize || 50,
      useBatchProcessing: config.useBatchProcessing !== undefined ? config.useBatchProcessing : true,
      batchSize: config.batchSize || 10,
      useGPUAcceleration: config.useGPUAcceleration !== undefined ? config.useGPUAcceleration : true,
      useWorker: config.useWorker !== undefined ? config.useWorker : false
    };
    
    // 设置优先级
    this.setPriority(200); // 动画系统应该在渲染系统之前更新
    
    // 初始化对象池
    if (this.config.useObjectPool) {
      this.initObjectPool();
    }
  }
  
  /**
   * 初始化对象池
   */
  private initObjectPool(): void {
    const size = this.config.objectPoolSize || 50;
    
    for (let i = 0; i < size; i++) {
      this.objectPool.clips.push(new AnimationClip());
      this.objectPool.mixers.push(new THREE.AnimationMixer(new THREE.Object3D()));
    }
  }
  
  /**
   * 创建动画控制器
   * @param entity 实体
   * @param clips 动画片段
   * @returns 动画控制器
   */
  public createAnimator(entity: Entity, clips: AnimationClip[] = []): Animator {
    // 检查是否已存在
    if (this.animators.has(entity)) {
      return this.animators.get(entity)!;
    }
    
    // 创建动画控制器
    const animator = new Animator({
      entity,
      clips
    });
    
    // 添加到映射
    this.animators.set(entity, animator);
    
    // 创建混合器
    const object3D = entity.getComponent('Transform')?.object3D || new THREE.Object3D();
    const mixer = new THREE.AnimationMixer(object3D);
    this.mixers.set(entity, mixer);
    
    return animator;
  }
  
  /**
   * 获取动画控制器
   * @param entity 实体
   * @returns 动画控制器
   */
  public getAnimator(entity: Entity): Animator | null {
    return this.animators.get(entity) || null;
  }
  
  /**
   * 移除动画控制器
   * @param entity 实体
   */
  public removeAnimator(entity: Entity): void {
    this.animators.delete(entity);
    this.mixers.delete(entity);
  }
  
  /**
   * 更新系统
   * @param deltaTime 时间增量
   */
  public update(deltaTime: number): void {
    if (!this.config.enabled) return;
    
    // 检查是否需要更新
    const currentTime = Time.now();
    if (currentTime - this.lastUpdateTime < this.config.updateFrequency!) {
      return;
    }
    this.lastUpdateTime = currentTime;
    
    // 更新性能统计
    this.stats.activeAnimators = this.animators.size;
    this.stats.activeMixers = this.mixers.size;
    
    // 更新所有混合器
    for (const [entity, mixer] of this.mixers) {
      mixer.update(deltaTime);
    }
    
    // 更新所有动画控制器
    for (const [entity, animator] of this.animators) {
      animator.update(deltaTime);
    }
    
    // 发出更新事件
    this.eventEmitter.emit('update', deltaTime);
  }
  
  /**
   * 获取性能统计
   * @returns 性能统计
   */
  public getStats(): any {
    return this.stats;
  }
  
  /**
   * 清除缓存
   */
  public clearCache(): void {
    this.clipCache.clear();
    this.stats.cacheHits = 0;
    this.stats.cacheMisses = 0;
  }
  
  /**
   * 重置对象池
   */
  public resetObjectPool(): void {
    this.objectPool.keyframes = [];
    this.objectPool.clips = [];
    this.objectPool.mixers = [];
    this.initObjectPool();
    this.stats.objectPoolHits = 0;
    this.stats.objectPoolMisses = 0;
  }
}
