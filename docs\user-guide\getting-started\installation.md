# 安装指南

本文档将指导您完成IR引擎编辑器的安装和初始配置过程。

## 系统要求

在安装IR引擎编辑器之前，请确保您的系统满足以下要求：

### 最低配置

- **操作系统**：
  - Windows 10/11 (64位)
  - macOS 10.15+
  - Ubuntu 20.04+ (部分功能可能受限)
- **处理器**：Intel Core i5-6600 或 AMD Ryzen 5 1600
- **内存**：8GB RAM
- **显卡**：支持WebGL 2.0的显卡，2GB显存
- **存储**：5GB可用空间
- **网络**：宽带互联网连接

### 推荐配置

- **操作系统**：
  - Windows 10/11 (64位)
  - macOS 12+
  - Ubuntu 22.04+
- **处理器**：Intel Core i7-9700 或 AMD Ryzen 7 3700X
- **内存**：16GB RAM
- **显卡**：NVIDIA GTX 1660 或 AMD RX 5600 XT，6GB显存
- **存储**：10GB SSD可用空间
- **网络**：高速宽带互联网连接

## 下载IR引擎编辑器

1. 访问IR引擎官方网站：[https://ir-engine.example.com/download](https://ir-engine.example.com/download)
2. 选择适合您操作系统的版本
3. 点击"下载"按钮
4. 如果您有账号，请登录；如果没有，可以注册一个新账号或选择"以后再说"
5. 下载将自动开始

## 安装步骤

### Windows安装

1. 找到下载的安装文件（通常是`IR-Engine-Setup-x.x.x.exe`）
2. 双击安装文件启动安装向导
3. 如果出现安全警告，点击"是"或"运行"
4. 在欢迎界面，点击"下一步"
5. 阅读并接受许可协议，点击"下一步"
6. 选择安装位置，默认为`C:\Program Files\IR Engine`，点击"下一步"
7. 选择要安装的组件：
   - IR引擎编辑器（必选）
   - 示例项目
   - 文档
   - 开发工具包
8. 点击"安装"开始安装过程
9. 安装完成后，点击"完成"

![Windows安装向导](../../assets/images/windows-installation.png)

### macOS安装

1. 找到下载的磁盘映像文件（通常是`IR-Engine-x.x.x.dmg`）
2. 双击打开磁盘映像
3. 将IR引擎应用程序图标拖动到"应用程序"文件夹
4. 等待复制完成
5. 弹出磁盘映像
6. 从"应用程序"文件夹或Launchpad启动IR引擎
7. 首次启动时，可能会出现安全警告，点击"打开"

![macOS安装](../../assets/images/macos-installation.png)

### Linux安装

#### 使用AppImage（推荐）

1. 找到下载的AppImage文件（通常是`IR-Engine-x.x.x.AppImage`）
2. 右键点击文件，选择"属性"
3. 在"权限"选项卡中，勾选"允许作为程序执行文件"
4. 关闭属性对话框
5. 双击AppImage文件启动IR引擎

#### 使用Debian/Ubuntu包

1. 找到下载的.deb文件（通常是`ir-engine_x.x.x_amd64.deb`）
2. 双击文件，使用软件安装器安装
3. 或者在终端中执行：
   ```bash
   sudo dpkg -i ir-engine_x.x.x_amd64.deb
   sudo apt-get install -f
   ```
4. 安装完成后，从应用程序菜单启动IR引擎

## 初始配置

### 首次启动

1. 启动IR引擎编辑器
2. 首次启动时，会显示欢迎界面
3. 选择界面语言（默认为中文）
4. 选择颜色主题（亮色/暗色）
5. 选择是否发送匿名使用数据（可选）
6. 点击"开始使用"

![首次启动向导](../../assets/images/first-launch.png)

### 登录账号

使用IR引擎账号登录可以获取更多功能，如云存储、资产库访问等：

1. 在欢迎界面或点击编辑器右上角的用户图标
2. 点击"登录"
3. 输入您的IR引擎账号和密码
4. 点击"登录"按钮
5. 如果没有账号，可以点击"注册"创建一个新账号

### 配置工作目录

设置默认工作目录，用于存储项目文件：

1. 点击顶部菜单栏的"编辑 > 首选项"
2. 在首选项对话框中，选择"常规"选项卡
3. 在"工作目录"部分，点击"浏览"按钮
4. 选择您希望存储项目的文件夹
5. 点击"选择文件夹"
6. 点击"应用"或"确定"保存更改

![配置工作目录](../../assets/images/workspace-config.png)

### 性能设置

根据您的硬件配置优化性能设置：

1. 点击顶部菜单栏的"编辑 > 首选项"
2. 在首选项对话框中，选择"性能"选项卡
3. 根据您的硬件配置，选择预设配置（低、中、高、超高）
4. 或手动调整以下设置：
   - 渲染质量
   - 纹理质量
   - 阴影质量
   - 抗锯齿
   - 后期处理效果
5. 点击"应用"或"确定"保存更改

![性能设置](../../assets/images/performance-settings.png)

## 安装示例项目

示例项目可以帮助您快速了解IR引擎的功能：

1. 启动IR引擎编辑器
2. 点击顶部菜单栏的"文件 > 示例"
3. 在示例浏览器中，选择要安装的示例项目
4. 点击"安装"按钮
5. 选择安装位置
6. 点击"确定"
7. 等待下载和安装完成
8. 安装完成后，点击"打开"按钮直接打开示例项目

![示例项目浏览器](../../assets/images/example-browser.png)

## 安装扩展

IR引擎支持通过扩展增强功能：

1. 点击顶部菜单栏的"编辑 > 扩展"
2. 在扩展管理器中，切换到"浏览"选项卡
3. 浏览或搜索可用扩展
4. 点击扩展卡片上的"安装"按钮
5. 等待下载和安装完成
6. 某些扩展可能需要重启编辑器才能生效

![扩展管理器](../../assets/images/extension-manager.png)

## 更新IR引擎编辑器

保持编辑器更新可以获取最新功能和修复：

1. 启动IR引擎编辑器
2. 点击顶部菜单栏的"帮助 > 检查更新"
3. 如果有可用更新，会显示更新对话框
4. 点击"下载并安装"按钮
5. 等待下载和安装完成
6. 安装完成后，编辑器会自动重启

您也可以启用自动更新：

1. 点击顶部菜单栏的"编辑 > 首选项"
2. 在首选项对话框中，选择"更新"选项卡
3. 勾选"自动检查更新"
4. 选择更新频率（每次启动、每天、每周）
5. 点击"应用"或"确定"保存更改

## 卸载IR引擎编辑器

如果需要卸载IR引擎编辑器，请按照以下步骤操作：

### Windows卸载

1. 打开控制面板
2. 选择"程序和功能"或"卸载程序"
3. 找到"IR引擎编辑器"
4. 右键点击并选择"卸载"
5. 按照卸载向导的提示完成卸载

或者：

1. 打开开始菜单
2. 找到IR引擎编辑器文件夹
3. 右键点击并选择"卸载"

### macOS卸载

1. 打开"应用程序"文件夹
2. 找到"IR引擎编辑器"应用
3. 将其拖到垃圾桶
4. 清空垃圾桶

### Linux卸载

#### AppImage卸载

1. 删除AppImage文件
2. 删除配置文件（通常位于`~/.config/ir-engine/`）

#### Debian/Ubuntu包卸载

在终端中执行：
```bash
sudo apt-get remove ir-engine
```

## 故障排除

### 安装问题

#### 安装失败

- 确保您有管理员/超级用户权限
- 暂时禁用防病毒软件
- 检查磁盘空间是否充足
- 尝试以兼容模式运行安装程序（Windows）

#### 启动失败

- 更新显卡驱动程序
- 检查系统是否满足最低要求
- 尝试以管理员/超级用户身份运行
- 检查日志文件（位于`%APPDATA%\IR Engine\Logs\`或`~/Library/Logs/IR Engine/`）

### 获取帮助

如果您在安装过程中遇到问题，可以通过以下方式获取帮助：

- 访问[官方论坛](https://ir-engine.example.com/forum)
- 查阅[常见问题解答](../faq/general.md)
- 联系[技术支持](mailto:<EMAIL>)

## 下一步

现在您已经成功安装了IR引擎编辑器，可以继续学习：

- [界面介绍](./interface.md)
- [基本操作](./basic-operations.md)
- [快捷键](./shortcuts.md)
- [创建第一个项目](../tutorials/first-project.md)
