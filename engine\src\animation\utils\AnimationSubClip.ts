/**
 * 动画子片段
 * 用于从完整的动画片段中提取部分时间段
 */

import * as THREE from 'three';

export interface SubClipConfig {
  /** 子片段名称 */
  name: string;
  /** 开始时间（秒） */
  startTime: number;
  /** 结束时间（秒） */
  endTime: number;
  /** 是否循环 */
  loop?: boolean;
  /** 播放速度 */
  speed?: number;
}

export class AnimationSubClip {
  /** 子片段名称 */
  public readonly name: string;
  
  /** 原始动画片段 */
  private originalClip: THREE.AnimationClip | null = null;
  
  /** 开始时间 */
  private startTime: number;
  
  /** 结束时间 */
  private endTime: number;
  
  /** 是否循环 */
  private loop: boolean;
  
  /** 播放速度 */
  private speed: number;
  
  /** 缓存的子片段 */
  private cachedSubClip: THREE.AnimationClip | null = null;

  constructor(config: SubClipConfig) {
    this.name = config.name;
    this.startTime = config.startTime;
    this.endTime = config.endTime;
    this.loop = config.loop || false;
    this.speed = config.speed || 1.0;
  }

  /**
   * 设置原始动画片段
   */
  public setOriginalClip(clip: THREE.AnimationClip): void {
    this.originalClip = clip;
    this.cachedSubClip = null; // 清除缓存
  }

  /**
   * 获取原始动画片段
   */
  public getOriginalClip(): THREE.AnimationClip | null {
    return this.originalClip;
  }

  /**
   * 设置时间范围
   */
  public setTimeRange(startTime: number, endTime: number): void {
    this.startTime = startTime;
    this.endTime = endTime;
    this.cachedSubClip = null; // 清除缓存
  }

  /**
   * 获取开始时间
   */
  public getStartTime(): number {
    return this.startTime;
  }

  /**
   * 获取结束时间
   */
  public getEndTime(): number {
    return this.endTime;
  }

  /**
   * 获取持续时间
   */
  public getDuration(): number {
    return this.endTime - this.startTime;
  }

  /**
   * 设置循环模式
   */
  public setLoop(loop: boolean): void {
    this.loop = loop;
  }

  /**
   * 获取循环模式
   */
  public getLoop(): boolean {
    return this.loop;
  }

  /**
   * 设置播放速度
   */
  public setSpeed(speed: number): void {
    this.speed = speed;
  }

  /**
   * 获取播放速度
   */
  public getSpeed(): number {
    return this.speed;
  }

  /**
   * 创建子片段
   */
  public createFromClip(clip: THREE.AnimationClip): THREE.AnimationClip {
    this.setOriginalClip(clip);
    return this.getSubClip()!;
  }

  /**
   * 获取子片段
   */
  public getSubClip(): THREE.AnimationClip | null {
    if (!this.originalClip) {
      console.warn('没有设置原始动画片段');
      return null;
    }

    // 如果有缓存且参数没有变化，直接返回缓存
    if (this.cachedSubClip) {
      return this.cachedSubClip;
    }

    // 创建新的轨道数组
    const subTracks: THREE.KeyframeTrack[] = [];

    for (const track of this.originalClip.tracks) {
      const subTrack = this.extractTrackSegment(track);
      if (subTrack) {
        subTracks.push(subTrack);
      }
    }

    // 创建子片段
    this.cachedSubClip = new THREE.AnimationClip(
      this.name,
      this.getDuration(),
      subTracks,
      this.originalClip.blendMode
    );

    return this.cachedSubClip;
  }

  /**
   * 提取轨道片段
   */
  private extractTrackSegment(track: THREE.KeyframeTrack): THREE.KeyframeTrack | null {
    const times = track.times;
    const values = track.values;
    const valueSize = track.getValueSize();

    // 找到时间范围内的关键帧索引
    let startIndex = -1;
    let endIndex = -1;

    for (let i = 0; i < times.length; i++) {
      if (times[i] >= this.startTime && startIndex === -1) {
        startIndex = i;
      }
      if (times[i] <= this.endTime) {
        endIndex = i;
      }
    }

    // 如果没有找到有效的关键帧，返回null
    if (startIndex === -1 || endIndex === -1 || startIndex > endIndex) {
      return null;
    }

    // 提取时间和值
    const subTimes: number[] = [];
    const subValues: number[] = [];

    // 如果开始时间不在关键帧上，需要插值
    if (startIndex > 0 && times[startIndex] > this.startTime) {
      subTimes.push(0); // 相对时间从0开始
      const interpolatedValue = this.interpolateValue(track, this.startTime);
      subValues.push(...interpolatedValue);
    }

    // 添加范围内的关键帧
    for (let i = startIndex; i <= endIndex; i++) {
      subTimes.push(times[i] - this.startTime); // 转换为相对时间
      const startValueIndex = i * valueSize;
      for (let j = 0; j < valueSize; j++) {
        subValues.push(values[startValueIndex + j]);
      }
    }

    // 如果结束时间不在关键帧上，需要插值
    if (endIndex < times.length - 1 && times[endIndex] < this.endTime) {
      subTimes.push(this.getDuration()); // 相对时间的结束点
      const interpolatedValue = this.interpolateValue(track, this.endTime);
      subValues.push(...interpolatedValue);
    }

    // 创建新的轨道
    const TrackConstructor = track.constructor as any;
    return new TrackConstructor(track.name, subTimes, subValues);
  }

  /**
   * 插值计算指定时间的值
   */
  private interpolateValue(track: THREE.KeyframeTrack, time: number): number[] {
    const times = track.times;
    const values = track.values;
    const valueSize = track.getValueSize();

    // 找到时间点前后的关键帧
    let beforeIndex = -1;
    let afterIndex = -1;

    for (let i = 0; i < times.length - 1; i++) {
      if (times[i] <= time && times[i + 1] >= time) {
        beforeIndex = i;
        afterIndex = i + 1;
        break;
      }
    }

    // 如果找不到合适的关键帧，返回最近的值
    if (beforeIndex === -1 || afterIndex === -1) {
      const closestIndex = time < times[0] ? 0 : times.length - 1;
      const result: number[] = [];
      const startValueIndex = closestIndex * valueSize;
      for (let i = 0; i < valueSize; i++) {
        result.push(values[startValueIndex + i]);
      }
      return result;
    }

    // 计算插值因子
    const t = (time - times[beforeIndex]) / (times[afterIndex] - times[beforeIndex]);

    // 线性插值
    const result: number[] = [];
    const beforeValueIndex = beforeIndex * valueSize;
    const afterValueIndex = afterIndex * valueSize;

    for (let i = 0; i < valueSize; i++) {
      const beforeValue = values[beforeValueIndex + i];
      const afterValue = values[afterValueIndex + i];
      result.push(beforeValue + (afterValue - beforeValue) * t);
    }

    return result;
  }

  /**
   * 克隆子片段
   */
  public clone(): AnimationSubClip {
    const cloned = new AnimationSubClip({
      name: this.name,
      startTime: this.startTime,
      endTime: this.endTime,
      loop: this.loop,
      speed: this.speed
    });

    if (this.originalClip) {
      cloned.setOriginalClip(this.originalClip);
    }

    return cloned;
  }
}
