/**
 * 基础渲染示例
 * 展示IR引擎渲染系统的基本功能
 */
import { Engine } from '../../src/core/Engine';
import { World } from '../../src/core/World';
import { Entity } from '../../src/core/Entity';
import { Scene } from '../../src/scene/Scene';
import { Transform } from '../../src/scene/Transform';
import { RenderSystem } from '../../src/rendering/RenderSystem';
import { Renderer } from '../../src/rendering/Renderer';
import { Camera, CameraType } from '../../src/rendering/Camera';
import { Light, LightType } from '../../src/rendering/Light';
import { MaterialSystem } from '../../src/rendering/materials/MaterialSystem';
import { PBRMaterial } from '../../src/rendering/materials/PBRMaterial';
import { PostProcessingSystem } from '../../src/rendering/postprocessing/PostProcessingSystem';
import { BloomEffect } from '../../src/rendering/postprocessing/effects/BloomEffect';
import { ToneMappingEffect, ToneMappingType } from '../../src/rendering/postprocessing/effects/ToneMappingEffect';
import * as THREE from 'three';

/**
 * 基础渲染示例类
 */
export class BasicRenderingExample {
  /** 引擎实例 */
  private engine: Engine;
  /** 世界实例 */
  private world: World;
  /** 渲染系统 */
  private renderSystem: RenderSystem;
  /** 场景 */
  private scene: Scene;
  /** 相机 */
  private camera: Camera;
  /** 材质系统 */
  private materialSystem: MaterialSystem;
  /** 旋转速度 */
  private rotationSpeed: number = 0.5;
  /** 立方体实体 */
  private cubeEntity: Entity;
  /** 球体实体 */
  private sphereEntity: Entity;
  /** 圆柱体实体 */
  private cylinderEntity: Entity;
  /** 环境光实体 */
  private ambientLightEntity: Entity;
  /** 方向光实体 */
  private directionalLightEntity: Entity;
  /** 点光源实体 */
  private pointLightEntity: Entity;
  /** 聚光灯实体 */
  private spotLightEntity: Entity;
  /** 后处理系统实体 */
  private postProcessingEntity: Entity;
  /** 后处理系统 */
  private postProcessingSystem: PostProcessingSystem;
  /** 泛光效果 */
  private bloomEffect: BloomEffect;
  /** 色调映射效果 */
  private toneMappingEffect: ToneMappingEffect;
  /** 是否启用后处理 */
  private postProcessingEnabled: boolean = true;
  /** 是否启用阴影 */
  private shadowsEnabled: boolean = true;
  /** 是否启用旋转 */
  private rotationEnabled: boolean = true;
  /** 是否显示网格线 */
  private gridEnabled: boolean = true;
  /** 网格线实体 */
  private gridEntity: Entity;

  /**
   * 创建基础渲染示例
   */
  constructor() {
    // 创建引擎
    this.engine = new Engine();
    
    // 创建世界
    this.world = new World();
    this.engine.setWorld(this.world);
    
    // 创建渲染器
    const renderer = new Renderer({
      width: window.innerWidth,
      height: window.innerHeight,
      antialias: true,
      shadows: true
    });
    
    // 创建渲染系统
    this.renderSystem = new RenderSystem(renderer, {
      enableShadows: true,
      enablePostProcessing: true,
      clearColor: { r: 0.1, g: 0.1, b: 0.1 },
      clearAlpha: 1.0
    });
    
    // 添加渲染系统到世界
    this.world.addSystem(this.renderSystem);
    
    // 创建材质系统
    this.materialSystem = new MaterialSystem();
    
    // 创建场景
    this.scene = new Scene();
    
    // 创建相机
    const cameraEntity = new Entity('相机');
    this.camera = new Camera({
      type: CameraType.PERSPECTIVE,
      fov: 75,
      aspect: window.innerWidth / window.innerHeight,
      near: 0.1,
      far: 1000,
      position: { x: 0, y: 5, z: 10 },
      lookAt: { x: 0, y: 0, z: 0 }
    });
    cameraEntity.addComponent(this.camera);
    this.scene.addEntity(cameraEntity);
    
    // 设置活跃场景和相机
    this.renderSystem.setActiveScene(this.scene);
    this.renderSystem.setActiveCamera(this.camera);
    
    // 创建场景对象
    this.createSceneObjects();
    
    // 创建光源
    this.createLights();
    
    // 创建后处理效果
    this.createPostProcessing();
    
    // 处理窗口大小变化
    window.addEventListener('resize', this.onResize.bind(this));
    
    // 处理键盘输入
    window.addEventListener('keydown', this.onKeyDown.bind(this));
  }

  /**
   * 创建场景对象
   */
  private createSceneObjects(): void {
    // 创建地面
    const groundEntity = new Entity('地面');
    groundEntity.addComponent(new Transform({
      position: { x: 0, y: 0, z: 0 },
      rotation: { x: 0, y: 0, z: 0 },
      scale: { x: 20, y: 1, z: 20 }
    }));
    
    // 创建地面几何体和材质
    const groundGeometry = new THREE.PlaneGeometry(1, 1);
    const groundMaterial = new THREE.MeshStandardMaterial({
      color: 0x808080,
      roughness: 0.7,
      metalness: 0.0
    });
    
    // 创建地面网格
    const groundMesh = new THREE.Mesh(groundGeometry, groundMaterial);
    groundMesh.rotation.x = -Math.PI / 2;
    groundMesh.receiveShadow = true;
    
    // 添加网格到变换组件
    const groundTransform = groundEntity.getComponent('Transform') as Transform;
    groundTransform.getObject3D().add(groundMesh);
    
    // 添加地面实体到场景
    this.scene.addEntity(groundEntity);
    
    // 创建网格线
    this.gridEntity = new Entity('网格线');
    this.gridEntity.addComponent(new Transform({
      position: { x: 0, y: 0.01, z: 0 },
      rotation: { x: 0, y: 0, z: 0 },
      scale: { x: 1, y: 1, z: 1 }
    }));
    
    // 创建网格线
    const gridHelper = new THREE.GridHelper(20, 20, 0x404040, 0x404040);
    
    // 添加网格线到变换组件
    const gridTransform = this.gridEntity.getComponent('Transform') as Transform;
    gridTransform.getObject3D().add(gridHelper);
    
    // 添加网格线实体到场景
    this.scene.addEntity(this.gridEntity);
    
    // 创建立方体
    this.cubeEntity = new Entity('立方体');
    this.cubeEntity.addComponent(new Transform({
      position: { x: -2.5, y: 1, z: 0 },
      rotation: { x: 0, y: Math.PI / 4, z: 0 },
      scale: { x: 1, y: 1, z: 1 }
    }));
    
    // 创建立方体几何体和材质
    const cubeGeometry = new THREE.BoxGeometry(1, 1, 1);
    const cubeMaterial = new THREE.MeshStandardMaterial({
      color: 0x0000ff,
      roughness: 0.5,
      metalness: 0.5
    });
    
    // 创建立方体网格
    const cubeMesh = new THREE.Mesh(cubeGeometry, cubeMaterial);
    cubeMesh.castShadow = true;
    cubeMesh.receiveShadow = true;
    
    // 添加网格到变换组件
    const cubeTransform = this.cubeEntity.getComponent('Transform') as Transform;
    cubeTransform.getObject3D().add(cubeMesh);
    
    // 添加立方体实体到场景
    this.scene.addEntity(this.cubeEntity);
    
    // 创建球体
    this.sphereEntity = new Entity('球体');
    this.sphereEntity.addComponent(new Transform({
      position: { x: 0, y: 1, z: 0 },
      rotation: { x: 0, y: 0, z: 0 },
      scale: { x: 1, y: 1, z: 1 }
    }));
    
    // 创建PBR材质
    const sphereMaterial = new PBRMaterial({
      name: '金属材质',
      baseColor: { r: 0.8, g: 0.8, b: 0.8 },
      metalness: 1.0,
      roughness: 0.2
    });
    
    // 创建球体几何体
    const sphereGeometry = new THREE.SphereGeometry(0.5, 32, 32);
    
    // 创建球体网格
    const sphereMesh = new THREE.Mesh(sphereGeometry, sphereMaterial.getMaterial());
    sphereMesh.castShadow = true;
    sphereMesh.receiveShadow = true;
    
    // 添加网格到变换组件
    const sphereTransform = this.sphereEntity.getComponent('Transform') as Transform;
    sphereTransform.getObject3D().add(sphereMesh);
    
    // 添加球体实体到场景
    this.scene.addEntity(this.sphereEntity);
    
    // 创建圆柱体
    this.cylinderEntity = new Entity('圆柱体');
    this.cylinderEntity.addComponent(new Transform({
      position: { x: 2.5, y: 1, z: 0 },
      rotation: { x: 0, y: 0, z: 0 },
      scale: { x: 1, y: 1, z: 1 }
    }));
    
    // 创建圆柱体几何体和材质
    const cylinderGeometry = new THREE.CylinderGeometry(0.5, 0.5, 2, 32);
    const cylinderMaterial = new THREE.MeshStandardMaterial({
      color: 0xff0000,
      roughness: 0.3,
      metalness: 0.7
    });
    
    // 创建圆柱体网格
    const cylinderMesh = new THREE.Mesh(cylinderGeometry, cylinderMaterial);
    cylinderMesh.castShadow = true;
    cylinderMesh.receiveShadow = true;
    
    // 添加网格到变换组件
    const cylinderTransform = this.cylinderEntity.getComponent('Transform') as Transform;
    cylinderTransform.getObject3D().add(cylinderMesh);
    
    // 添加圆柱体实体到场景
    this.scene.addEntity(this.cylinderEntity);
  }

  /**
   * 创建光源
   */
  private createLights(): void {
    // 创建环境光
    this.ambientLightEntity = new Entity('环境光');
    
    // 创建环境光组件
    const ambientLight = new Light({
      type: LightType.AMBIENT,
      color: 0x404040,
      intensity: 0.5
    });
    this.ambientLightEntity.addComponent(ambientLight);
    
    // 添加环境光实体到场景
    this.scene.addEntity(this.ambientLightEntity);
    
    // 创建方向光
    this.directionalLightEntity = new Entity('方向光');
    this.directionalLightEntity.addComponent(new Transform({
      position: { x: 5, y: 5, z: 5 },
      rotation: { x: -Math.PI / 4, y: Math.PI / 4, z: 0 },
      scale: { x: 1, y: 1, z: 1 }
    }));
    
    // 创建方向光组件
    const directionalLight = new Light({
      type: LightType.DIRECTIONAL,
      color: 0xffffff,
      intensity: 1.0,
      castShadow: true,
      shadowMapSize: 2048,
      shadowBias: -0.0005
    });
    this.directionalLightEntity.addComponent(directionalLight);
    
    // 添加方向光实体到场景
    this.scene.addEntity(this.directionalLightEntity);
    
    // 创建点光源
    this.pointLightEntity = new Entity('点光源');
    this.pointLightEntity.addComponent(new Transform({
      position: { x: -3, y: 2, z: -3 },
      rotation: { x: 0, y: 0, z: 0 },
      scale: { x: 1, y: 1, z: 1 }
    }));
    
    // 创建点光源组件
    const pointLight = new Light({
      type: LightType.POINT,
      color: 0x00ff00,
      intensity: 1.0,
      castShadow: true,
      distance: 10,
      decay: 2
    });
    this.pointLightEntity.addComponent(pointLight);
    
    // 添加点光源实体到场景
    this.scene.addEntity(this.pointLightEntity);
    
    // 创建聚光灯
    this.spotLightEntity = new Entity('聚光灯');
    this.spotLightEntity.addComponent(new Transform({
      position: { x: 3, y: 5, z: -3 },
      rotation: { x: -Math.PI / 2, y: 0, z: 0 },
      scale: { x: 1, y: 1, z: 1 }
    }));
    
    // 创建聚光灯组件
    const spotLight = new Light({
      type: LightType.SPOT,
      color: 0xff00ff,
      intensity: 1.0,
      castShadow: true,
      distance: 15,
      angle: Math.PI / 6,
      penumbra: 0.2,
      decay: 2
    });
    this.spotLightEntity.addComponent(spotLight);
    
    // 添加聚光灯实体到场景
    this.scene.addEntity(this.spotLightEntity);
  }

  /**
   * 创建后处理效果
   */
  private createPostProcessing(): void {
    // 创建后处理系统实体
    this.postProcessingEntity = new Entity('后处理系统');
    
    // 创建后处理系统
    this.postProcessingSystem = new PostProcessingSystem({
      enabled: true,
      autoResize: true
    });
    
    // 创建泛光效果
    this.bloomEffect = new BloomEffect({
      name: 'Bloom',
      enabled: true,
      intensity: 1.0,
      threshold: 0.7,
      radius: 0.4
    });
    
    // 创建色调映射效果
    this.toneMappingEffect = new ToneMappingEffect({
      name: 'ToneMapping',
      enabled: true,
      type: ToneMappingType.ACES_FILMIC,
      exposure: 1.0
    });
    
    // 添加效果到后处理系统
    this.postProcessingSystem.addEffect(this.bloomEffect);
    this.postProcessingSystem.addEffect(this.toneMappingEffect);
    
    // 添加后处理系统到实体
    this.postProcessingEntity.addComponent(this.postProcessingSystem);
    
    // 添加后处理系统实体到场景
    this.scene.addEntity(this.postProcessingEntity);
  }

  /**
   * 启动示例
   */
  public start(): void {
    // 启动引擎
    this.engine.start();
    
    // 添加更新回调
    this.engine.addUpdateCallback(this.update.bind(this));
    
    console.log('基础渲染示例已启动');
    console.log('按键说明:');
    console.log('P: 切换后处理效果');
    console.log('S: 切换阴影');
    console.log('R: 切换旋转');
    console.log('G: 切换网格线');
  }

  /**
   * 更新回调
   * @param deltaTime 时间增量
   */
  private update(deltaTime: number): void {
    if (this.rotationEnabled) {
      // 旋转立方体
      const cubeTransform = this.cubeEntity.getComponent('Transform') as Transform;
      cubeTransform.rotate({ x: 0, y: this.rotationSpeed * deltaTime, z: 0 });
      
      // 旋转球体
      const sphereTransform = this.sphereEntity.getComponent('Transform') as Transform;
      sphereTransform.rotate({ x: 0, y: this.rotationSpeed * deltaTime, z: 0 });
      
      // 旋转圆柱体
      const cylinderTransform = this.cylinderEntity.getComponent('Transform') as Transform;
      cylinderTransform.rotate({ x: 0, y: this.rotationSpeed * deltaTime, z: 0 });
    }
  }

  /**
   * 窗口大小变化处理
   */
  private onResize(): void {
    // 更新相机宽高比
    this.camera.setAspect(window.innerWidth / window.innerHeight);
    
    // 更新渲染器大小
    this.renderSystem.resize(window.innerWidth, window.innerHeight);
  }

  /**
   * 键盘输入处理
   * @param event 键盘事件
   */
  private onKeyDown(event: KeyboardEvent): void {
    switch (event.key.toLowerCase()) {
      case 'p':
        // 切换后处理效果
        this.postProcessingEnabled = !this.postProcessingEnabled;
        this.postProcessingSystem.setEnabled(this.postProcessingEnabled);
        console.log(`后处理效果: ${this.postProcessingEnabled ? '开启' : '关闭'}`);
        break;
      
      case 's':
        // 切换阴影
        this.shadowsEnabled = !this.shadowsEnabled;
        this.renderSystem.setShadowsEnabled(this.shadowsEnabled);
        console.log(`阴影: ${this.shadowsEnabled ? '开启' : '关闭'}`);
        break;
      
      case 'r':
        // 切换旋转
        this.rotationEnabled = !this.rotationEnabled;
        console.log(`旋转: ${this.rotationEnabled ? '开启' : '关闭'}`);
        break;
      
      case 'g':
        // 切换网格线
        this.gridEnabled = !this.gridEnabled;
        this.gridEntity.setActive(this.gridEnabled);
        console.log(`网格线: ${this.gridEnabled ? '显示' : '隐藏'}`);
        break;
    }
  }
}

// 创建并启动示例
const example = new BasicRenderingExample();
example.start();
