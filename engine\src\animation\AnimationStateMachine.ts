/**
 * 动画状态机
 * 用于管理动画状态和状态之间的转换
 */
import { EventEmitter } from '../utils/EventEmitter';
import { AnimationClip } from './AnimationClip';
import { Animator } from './Animator';

/**
 * 动画状态接口
 */
export interface AnimationState {
  /** 状态名称 */
  name: string;
  /** 状态类型 */
  type: string;
  /** 状态数据 */
  [key: string]: any;
}

/**
 * 单一动画状态
 */
export interface SingleAnimationState extends AnimationState {
  /** 状态类型 */
  type: 'SingleAnimationState';
  /** 动画片段名称 */
  clipName: string;
  /** 是否循环 */
  loop: boolean;
  /** 播放完成后是否保持最后一帧 */
  clamp: boolean;
}

/**
 * 混合动画状态
 */
export interface BlendAnimationState extends AnimationState {
  /** 状态类型 */
  type: 'BlendAnimationState';
  /** 混合参数名称 */
  parameterName: string;
  /** 混合空间类型 */
  blendSpaceType: '1D' | '2D';
  /** 混合空间配置 */
  blendSpaceConfig: any;
}

/**
 * 状态转换规则
 */
export interface TransitionRule {
  /** 源状态名称 */
  from: string;
  /** 目标状态名称 */
  to: string;
  /** 转换条件 */
  condition: () => boolean;
  /** 条件表达式（用于序列化） */
  conditionExpression?: string;
  /** 转换持续时间（秒） */
  duration: number;
  /** 是否可以中断 */
  canInterrupt: boolean;
  /** 转换曲线类型 */
  curveType?: string;
  /** 优先级 */
  priority?: number;
}

/**
 * 动画状态机事件类型
 */
export enum AnimationStateMachineEventType {
  /** 状态进入 */
  STATE_ENTER = 'stateEnter',
  /** 状态退出 */
  STATE_EXIT = 'stateExit',
  /** 状态转换开始 */
  TRANSITION_START = 'transitionStart',
  /** 状态转换结束 */
  TRANSITION_END = 'transitionEnd'
}

/**
 * 参数元数据
 */
export interface ParameterMetadata {
  /** 最小值（仅适用于数值类型） */
  minValue?: number;
  /** 最大值（仅适用于数值类型） */
  maxValue?: number;
  /** 枚举值（仅适用于枚举类型） */
  enumValues?: string[];
  /** 描述 */
  description?: string;
}

/**
 * 动画状态机
 */
export class AnimationStateMachine {
  /** 状态映射 */
  private states: Map<string, AnimationState> = new Map();
  /** 转换规则列表 */
  private transitions: TransitionRule[] = [];
  /** 当前状态 */
  private currentState: AnimationState | null = null;
  /** 上一个状态 */
  private previousState: AnimationState | null = null;
  /** 是否正在转换 */
  private isTransitioning: boolean = false;
  /** 当前转换规则 */
  private currentTransition: TransitionRule | null = null;
  /** 转换开始时间 */
  private transitionStartTime: number = 0;
  /** 转换持续时间 */
  private transitionDuration: number = 0;
  /** 动画控制器 */
  private animator: Animator;
  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();
  /** 参数映射 */
  private parameters: Map<string, any> = new Map();
  /** 参数元数据映射 */
  private parameterMetadata: Map<string, ParameterMetadata> = new Map();
  /** 调试模式 */
  private debugMode: boolean = false;

  /**
   * 构造函数
   * @param animator 动画控制器
   */
  constructor(animator: Animator) {
    this.animator = animator;
  }

  /**
   * 添加状态
   * @param state 动画状态
   */
  public addState(state: AnimationState): void {
    this.states.set(state.name, state);
  }

  /**
   * 添加转换规则
   * @param rule 转换规则
   */
  public addTransition(rule: TransitionRule): void {
    this.transitions.push(rule);
  }

  /**
   * 设置参数
   * @param name 参数名称
   * @param value 参数值
   */
  public setParameter(name: string, value: any): void {
    this.parameters.set(name, value);
  }

  /**
   * 获取参数
   * @param name 参数名称
   * @returns 参数值
   */
  public getParameter(name: string): any {
    return this.parameters.get(name);
  }

  /**
   * 设置当前状态
   * @param stateName 状态名称
   */
  public setCurrentState(stateName: string): void {
    const state = this.states.get(stateName);
    if (!state) {
      console.warn(`状态 "${stateName}" 不存在`);
      return;
    }

    if (this.currentState) {
      this.previousState = this.currentState;
      this.eventEmitter.emit(AnimationStateMachineEventType.STATE_EXIT, this.currentState);
    }

    this.currentState = state;
    this.isTransitioning = false;
    this.currentTransition = null;

    this.enterState(state);
    this.eventEmitter.emit(AnimationStateMachineEventType.STATE_ENTER, state);
  }

  /**
   * 更新状态机
   * @param deltaTime 时间增量（秒）
   */
  public update(deltaTime: number): void {
    if (!this.currentState) return;

    // 如果正在转换中
    if (this.isTransitioning && this.currentTransition) {
      const transitionTime = this.animator.getTime() - this.transitionStartTime;
      const progress = Math.min(transitionTime / this.transitionDuration, 1.0);

      // 如果转换完成
      if (progress >= 1.0) {
        this.isTransitioning = false;
        this.setCurrentState(this.currentTransition.to);
        this.eventEmitter.emit(AnimationStateMachineEventType.TRANSITION_END, {
          from: this.currentTransition.from,
          to: this.currentTransition.to
        });
        this.currentTransition = null;
      }
    } else {
      // 检查是否有满足条件的转换规则
      for (const transition of this.transitions) {
        if (transition.from === this.currentState.name && transition.condition()) {
          this.startTransition(transition);
          break;
        }
      }
    }

    // 更新当前状态
    this.updateState(this.currentState, deltaTime);
  }

  /**
   * 开始转换
   * @param transition 转换规则
   */
  private startTransition(transition: TransitionRule): void {
    this.isTransitioning = true;
    this.currentTransition = transition;
    this.transitionStartTime = this.animator.getTime();
    this.transitionDuration = transition.duration;

    const targetState = this.states.get(transition.to);
    if (!targetState) {
      console.warn(`目标状态 "${transition.to}" 不存在`);
      return;
    }

    // 开始混合到目标状态
    this.blendToState(targetState, transition.duration);

    this.eventEmitter.emit(AnimationStateMachineEventType.TRANSITION_START, {
      from: transition.from,
      to: transition.to,
      duration: transition.duration
    });
  }

  /**
   * 进入状态
   * @param state 动画状态
   */
  private enterState(state: AnimationState): void {
    if (state.type === 'SingleAnimationState') {
      const singleState = state as SingleAnimationState;
      this.animator.play(singleState.clipName, 0);
      this.animator.setLoop(singleState.loop);
    } else if (state.type === 'BlendAnimationState') {
      const blendState = state as BlendAnimationState;
      // 处理混合状态
      this.updateBlendState(blendState);
    }
  }

  /**
   * 更新状态
   * @param state 动画状态
   * @param deltaTime 时间增量（秒）
   */
  private updateState(state: AnimationState, deltaTime: number): void {
    if (state.type === 'BlendAnimationState') {
      const blendState = state as BlendAnimationState;
      this.updateBlendState(blendState);
    }
  }

  /**
   * 更新混合状态
   * @param state 混合动画状态
   */
  private updateBlendState(state: BlendAnimationState): void {
    const paramValue = this.getParameter(state.parameterName);
    if (paramValue === undefined) return;

    if (state.blendSpaceType === '1D') {
      // 处理1D混合空间
      // 这里需要实现1D混合空间的逻辑
    } else if (state.blendSpaceType === '2D') {
      // 处理2D混合空间
      // 这里需要实现2D混合空间的逻辑
    }
  }

  /**
   * 混合到状态
   * @param state 目标状态
   * @param duration 混合持续时间（秒）
   */
  private blendToState(state: AnimationState, duration: number): void {
    if (state.type === 'SingleAnimationState') {
      const singleState = state as SingleAnimationState;
      this.animator.play(singleState.clipName, duration);
    } else if (state.type === 'BlendAnimationState') {
      // 处理混合到混合状态的逻辑
    }
  }

  /**
   * 添加事件监听器
   * @param type 事件类型
   * @param callback 回调函数
   */
  public addEventListener(type: AnimationStateMachineEventType, callback: (data: any) => void): void {
    this.eventEmitter.on(type, callback);
  }

  /**
   * 移除事件监听器
   * @param type 事件类型
   * @param callback 回调函数
   */
  public removeEventListener(type: AnimationStateMachineEventType, callback: (data: any) => void): void {
    this.eventEmitter.off(type, callback);
  }

  /**
   * 获取所有状态
   * @returns 状态数组
   */
  public getStates(): AnimationState[] {
    return Array.from(this.states.values());
  }

  /**
   * 获取状态
   * @param name 状态名称
   * @returns 状态，如果不存在则返回null
   */
  public getState(name: string): AnimationState | null {
    return this.states.get(name) || null;
  }

  /**
   * 移除状态
   * @param name 状态名称
   * @returns 是否成功移除
   */
  public removeState(name: string): boolean {
    // 检查是否存在
    if (!this.states.has(name)) {
      return false;
    }

    // 如果是当前状态，则清除当前状态
    if (this.currentState && this.currentState.name === name) {
      this.currentState = null;
    }

    // 移除相关的转换规则
    this.transitions = this.transitions.filter(
      (transition) => transition.from !== name && transition.to !== name
    );

    // 移除状态
    return this.states.delete(name);
  }

  /**
   * 获取所有转换规则
   * @returns 转换规则数组
   */
  public getTransitions(): TransitionRule[] {
    return [...this.transitions];
  }

  /**
   * 获取转换规则
   * @param fromState 源状态名称
   * @param toState 目标状态名称
   * @returns 转换规则，如果不存在则返回null
   */
  public getTransition(fromState: string, toState: string): TransitionRule | null {
    return this.transitions.find(
      (transition) => transition.from === fromState && transition.to === toState
    ) || null;
  }

  /**
   * 移除转换规则
   * @param fromState 源状态名称
   * @param toState 目标状态名称
   * @returns 是否成功移除
   */
  public removeTransition(fromState: string, toState: string): boolean {
    const index = this.transitions.findIndex(
      (transition) => transition.from === fromState && transition.to === toState
    );

    if (index === -1) {
      return false;
    }

    // 如果是当前转换，则清除当前转换
    if (
      this.currentTransition &&
      this.currentTransition.from === fromState &&
      this.currentTransition.to === toState
    ) {
      this.isTransitioning = false;
      this.currentTransition = null;
    }

    // 移除转换规则
    this.transitions.splice(index, 1);
    return true;
  }

  /**
   * 获取当前状态
   * @returns 当前状态，如果没有则返回null
   */
  public getCurrentState(): AnimationState | null {
    return this.currentState;
  }

  /**
   * 获取上一个状态
   * @returns 上一个状态，如果没有则返回null
   */
  public getPreviousState(): AnimationState | null {
    return this.previousState;
  }

  /**
   * 获取所有参数
   * @returns 参数映射
   */
  public getParameters(): Map<string, any> {
    return new Map(this.parameters);
  }

  /**
   * 移除参数
   * @param name 参数名称
   * @returns 是否成功移除
   */
  public removeParameter(name: string): boolean {
    // 移除参数元数据
    this.parameterMetadata.delete(name);

    // 移除参数
    return this.parameters.delete(name);
  }

  /**
   * 设置参数元数据
   * @param name 参数名称
   * @param metadata 参数元数据
   */
  public setParameterMetadata(name: string, metadata: ParameterMetadata): void {
    this.parameterMetadata.set(name, metadata);
  }

  /**
   * 获取参数元数据
   * @param name 参数名称
   * @returns 参数元数据，如果不存在则返回null
   */
  public getParameterMetadata(name: string): ParameterMetadata | null {
    return this.parameterMetadata.get(name) || null;
  }

  /**
   * 设置调试模式
   * @param enabled 是否启用
   */
  public setDebugMode(enabled: boolean): void {
    this.debugMode = enabled;
  }

  /**
   * 获取调试模式
   * @returns 是否启用调试模式
   */
  public isDebugMode(): boolean {
    return this.debugMode;
  }

  /**
   * 获取动画控制器
   * @returns 动画控制器
   */
  public getAnimator(): Animator {
    return this.animator;
  }

  /**
   * 重置状态机
   */
  public reset(): void {
    this.currentState = null;
    this.previousState = null;
    this.isTransitioning = false;
    this.currentTransition = null;
  }

  /**
   * 清空状态机
   */
  public clear(): void {
    this.states.clear();
    this.transitions = [];
    this.parameters.clear();
    this.parameterMetadata.clear();
    this.reset();
  }
}
