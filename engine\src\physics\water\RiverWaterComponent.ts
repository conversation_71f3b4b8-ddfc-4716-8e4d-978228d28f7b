/**
 * 河流水体组件
 * 用于模拟河流水体，包括流动、波动等特性
 */
import * as THREE from 'three';
import { WaterBodyComponent, WaterBodyType, WaterBodyShape, WaterBodyConfig } from './WaterBodyComponent';
import { Entity } from '../../core/Entity';
import { Debug } from '../../utils/Debug';

/**
 * 河流水体配置
 */
export interface RiverWaterConfig extends WaterBodyConfig {
  /** 河流路径点 */
  pathPoints?: THREE.Vector3[];
  /** 河流宽度 */
  width?: number;
  /** 河流深度 */
  depth?: number;
  /** 河流流速 */
  flowSpeed?: number;
  /** 河流弯曲度 */
  curvature?: number;
  /** 河床高度变化 */
  bedHeightVariation?: number;
  /** 河岸高度 */
  bankHeight?: number;
  /** 是否生成河岸 */
  generateBanks?: boolean;
  /** 是否使用样条曲线 */
  useSpline?: boolean;
}

/**
 * 河流水体组件
 */
export class RiverWaterComponent extends WaterBodyComponent {
  /** 河流路径点 */
  private pathPoints: THREE.Vector3[];
  /** 河流宽度 */
  private width: number;
  /** 河流深度 */
  private depth: number;
  /** 河流流速 */
  private flowSpeed: number;
  /** 河流弯曲度 */
  private curvature: number;
  /** 河床高度变化 */
  private bedHeightVariation: number;
  /** 河岸高度 */
  private bankHeight: number;
  /** 是否生成河岸 */
  private generateBanks: boolean;
  /** 是否使用样条曲线 */
  private useSpline: boolean;
  /** 河流曲线 */
  private riverCurve: THREE.CatmullRomCurve3 | null;
  /** 河流几何体 */
  private riverGeometry: THREE.BufferGeometry | null;
  /** 河岸几何体 */
  private bankGeometry: THREE.BufferGeometry | null;
  /** 河床几何体 */
  private bedGeometry: THREE.BufferGeometry | null;

  /**
   * 构造函数
   * @param entity 实体
   * @param config 配置
   */
  constructor(entity: Entity, config: RiverWaterConfig = {}) {
    // 设置默认配置
    const defaultConfig: RiverWaterConfig = {
      ...config,
      type: WaterBodyType.RIVER,
      shape: WaterBodyShape.CUSTOM,
      enableFlow: true,
      flowSpeed: config.flowSpeed || 1.0
    };

    // 调用父类构造函数
    super(entity, defaultConfig);

    // 设置河流特定属性
    this.pathPoints = config.pathPoints || [
      new THREE.Vector3(-50, 0, 0),
      new THREE.Vector3(0, 0, 0),
      new THREE.Vector3(50, 0, 0)
    ];
    this.width = config.width || 10;
    this.depth = config.depth || 2;
    this.flowSpeed = config.flowSpeed || 1.0;
    this.curvature = config.curvature || 0.5;
    this.bedHeightVariation = config.bedHeightVariation || 0.5;
    this.bankHeight = config.bankHeight || 1.0;
    this.generateBanks = config.generateBanks !== undefined ? config.generateBanks : true;
    this.useSpline = config.useSpline !== undefined ? config.useSpline : true;
    this.riverCurve = null;
    this.riverGeometry = null;
    this.bankGeometry = null;
    this.bedGeometry = null;

    // 设置流向
    this.setFlowDirection({ x: 1, y: 0, z: 0 });
    this.setFlowSpeed(this.flowSpeed);
  }

  /**
   * 初始化组件
   */
  public override initialize(): void {
    if (this.initialized) {
      return;
    }

    // 创建河流曲线
    this.createRiverCurve();

    // 创建河流几何体
    this.createRiverGeometry();

    // 如果需要生成河岸，创建河岸几何体
    if (this.generateBanks) {
      this.createBankGeometry();
    }

    // 创建河床几何体
    this.createBedGeometry();

    // 创建水体材质
    this.createWaterMaterial();

    // 初始化水面高度图
    this.initializeHeightMap();

    // 初始化水面法线图
    this.initializeNormalMap();

    // 初始化水流速度图
    this.initializeVelocityMap();

    // 如果启用粒子，初始化粒子系统
    if (this.isParticlesEnabled()) {
      this.initializeParticleSystem();
    }

    this.initialized = true;
    Debug.log('RiverWaterComponent', '河流水体组件初始化完成');
  }

  /**
   * 创建河流曲线
   */
  private createRiverCurve(): void {
    if (this.useSpline) {
      // 使用样条曲线
      this.riverCurve = new THREE.CatmullRomCurve3(this.pathPoints);
      this.riverCurve.curveType = 'centripetal';
      this.riverCurve.tension = this.curvature;
    } else {
      // 使用线性路径
      this.riverCurve = new THREE.CatmullRomCurve3(this.pathPoints);
      this.riverCurve.curveType = 'centripetal';
      this.riverCurve.tension = 0;
    }
  }

  /**
   * 创建河流几何体
   */
  private createRiverGeometry(): void {
    if (!this.riverCurve) {
      return;
    }

    // 创建河流几何体
    const segments = Math.max(this.pathPoints.length * 10, 50); // 分段数
    const points = this.riverCurve.getPoints(segments);
    const geometry = new THREE.BufferGeometry();
    const vertices: number[] = [];
    const uvs: number[] = [];
    const indices: number[] = [];

    // 计算河流宽度的一半
    const halfWidth = this.width / 2;

    // 计算河流方向向量和法向量
    for (let i = 0; i < points.length; i++) {
      const point = points[i];

      // 计算当前点的切线方向
      const tangent = this.riverCurve!.getTangent(i / segments);

      // 计算法向量（垂直于切线和上方向）
      const normal = new THREE.Vector3().crossVectors(
        new THREE.Vector3(0, 1, 0),
        tangent
      ).normalize();

      // 计算河流左右两侧的点
      const leftPoint = new THREE.Vector3().copy(point).add(
        new THREE.Vector3().copy(normal).multiplyScalar(halfWidth)
      );

      const rightPoint = new THREE.Vector3().copy(point).add(
        new THREE.Vector3().copy(normal).multiplyScalar(-halfWidth)
      );

      // 添加顶点
      vertices.push(
        leftPoint.x, point.y, leftPoint.z,
        rightPoint.x, point.y, rightPoint.z
      );

      // 添加UV坐标
      uvs.push(
        0, i / segments,
        1, i / segments
      );

      // 添加索引（创建三角形）
      if (i < points.length - 1) {
        const baseIndex = i * 2;
        indices.push(
          baseIndex, baseIndex + 1, baseIndex + 2,
          baseIndex + 1, baseIndex + 3, baseIndex + 2
        );
      }
    }

    // 设置几何体属性
    geometry.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3));
    geometry.setAttribute('uv', new THREE.Float32BufferAttribute(uvs, 2));
    geometry.setIndex(indices);

    // 计算法线
    geometry.computeVertexNormals();

    this.riverGeometry = geometry;
  }

  /**
   * 创建河岸几何体
   */
  private createBankGeometry(): void {
    if (!this.riverCurve) {
      return;
    }

    // 创建河岸几何体
    const segments = Math.max(this.pathPoints.length * 10, 50); // 分段数
    const points = this.riverCurve.getPoints(segments);
    const geometry = new THREE.BufferGeometry();
    const vertices: number[] = [];
    const uvs: number[] = [];
    const indices: number[] = [];

    // 计算河流宽度的一半
    const halfWidth = this.width / 2;
    // 计算河岸宽度（比河流宽一些）
    const bankWidth = halfWidth * 1.5;

    // 计算河流方向向量和法向量
    for (let i = 0; i < points.length; i++) {
      const point = points[i];

      // 计算当前点的切线方向
      const tangent = this.riverCurve!.getTangent(i / segments);

      // 计算法向量（垂直于切线和上方向）
      const normal = new THREE.Vector3().crossVectors(
        new THREE.Vector3(0, 1, 0),
        tangent
      ).normalize();

      // 计算河流左右两侧的点
      const leftRiverPoint = new THREE.Vector3().copy(point).add(
        new THREE.Vector3().copy(normal).multiplyScalar(halfWidth)
      );

      const rightRiverPoint = new THREE.Vector3().copy(point).add(
        new THREE.Vector3().copy(normal).multiplyScalar(-halfWidth)
      );

      // 计算河岸左右两侧的点
      const leftBankPoint = new THREE.Vector3().copy(point).add(
        new THREE.Vector3().copy(normal).multiplyScalar(bankWidth)
      );
      leftBankPoint.y += this.bankHeight;

      const rightBankPoint = new THREE.Vector3().copy(point).add(
        new THREE.Vector3().copy(normal).multiplyScalar(-bankWidth)
      );
      rightBankPoint.y += this.bankHeight;

      // 添加左侧河岸顶点
      vertices.push(
        leftRiverPoint.x, leftRiverPoint.y, leftRiverPoint.z,
        leftBankPoint.x, leftBankPoint.y, leftBankPoint.z
      );

      // 添加右侧河岸顶点
      vertices.push(
        rightRiverPoint.x, rightRiverPoint.y, rightRiverPoint.z,
        rightBankPoint.x, rightBankPoint.y, rightBankPoint.z
      );

      // 添加UV坐标
      uvs.push(
        0, i / segments,
        1, i / segments,
        0, i / segments,
        1, i / segments
      );

      // 添加索引（创建三角形）
      if (i < points.length - 1) {
        const baseIndex = i * 4;
        // 左侧河岸
        indices.push(
          baseIndex, baseIndex + 1, baseIndex + 4,
          baseIndex + 1, baseIndex + 5, baseIndex + 4
        );
        // 右侧河岸
        indices.push(
          baseIndex + 2, baseIndex + 3, baseIndex + 6,
          baseIndex + 3, baseIndex + 7, baseIndex + 6
        );
      }
    }

    // 设置几何体属性
    geometry.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3));
    geometry.setAttribute('uv', new THREE.Float32BufferAttribute(uvs, 2));
    geometry.setIndex(indices);

    // 计算法线
    geometry.computeVertexNormals();

    this.bankGeometry = geometry;
  }

  /**
   * 创建河床几何体
   */
  private createBedGeometry(): void {
    if (!this.riverCurve) {
      return;
    }

    // 创建河床几何体
    const segments = Math.max(this.pathPoints.length * 10, 50); // 分段数
    const points = this.riverCurve.getPoints(segments);
    const geometry = new THREE.BufferGeometry();
    const vertices: number[] = [];
    const uvs: number[] = [];
    const indices: number[] = [];

    // 计算河流宽度的一半
    const halfWidth = this.width / 2;

    // 计算河流方向向量和法向量
    for (let i = 0; i < points.length; i++) {
      const point = points[i];

      // 计算当前点的切线方向
      const tangent = this.riverCurve!.getTangent(i / segments);

      // 计算法向量（垂直于切线和上方向）
      const normal = new THREE.Vector3().crossVectors(
        new THREE.Vector3(0, 1, 0),
        tangent
      ).normalize();

      // 计算河流左右两侧的点
      const leftPoint = new THREE.Vector3().copy(point).add(
        new THREE.Vector3().copy(normal).multiplyScalar(halfWidth)
      );

      const rightPoint = new THREE.Vector3().copy(point).add(
        new THREE.Vector3().copy(normal).multiplyScalar(-halfWidth)
      );

      // 计算河床深度（可以根据位置变化）
      const bedDepth = this.depth + Math.sin(i / segments * Math.PI * 2) * this.bedHeightVariation;

      // 计算河床点
      const leftBedPoint = new THREE.Vector3(leftPoint.x, point.y - bedDepth, leftPoint.z);
      const rightBedPoint = new THREE.Vector3(rightPoint.x, point.y - bedDepth, rightPoint.z);

      // 添加顶点
      vertices.push(
        leftPoint.x, leftPoint.y, leftPoint.z,
        leftBedPoint.x, leftBedPoint.y, leftBedPoint.z,
        rightPoint.x, rightPoint.y, rightPoint.z,
        rightBedPoint.x, rightBedPoint.y, rightBedPoint.z
      );

      // 添加UV坐标
      uvs.push(
        0, i / segments,
        0, i / segments + 0.5,
        1, i / segments,
        1, i / segments + 0.5
      );

      // 添加索引（创建三角形）
      if (i < points.length - 1) {
        const baseIndex = i * 4;
        // 左侧河床
        indices.push(
          baseIndex, baseIndex + 1, baseIndex + 4,
          baseIndex + 1, baseIndex + 5, baseIndex + 4
        );
        // 右侧河床
        indices.push(
          baseIndex + 2, baseIndex + 3, baseIndex + 6,
          baseIndex + 3, baseIndex + 7, baseIndex + 6
        );
        // 河床底部
        indices.push(
          baseIndex + 1, baseIndex + 3, baseIndex + 5,
          baseIndex + 3, baseIndex + 7, baseIndex + 5
        );
      }
    }

    // 设置几何体属性
    geometry.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3));
    geometry.setAttribute('uv', new THREE.Float32BufferAttribute(uvs, 2));
    geometry.setIndex(indices);

    // 计算法线
    geometry.computeVertexNormals();

    this.bedGeometry = geometry;
  }

  /**
   * 更新组件
   * @param deltaTime 帧间隔时间（秒）
   */
  public override update(deltaTime: number): void {
    super.update(deltaTime);

    // 更新河流流动效果
    this.updateRiverFlow(deltaTime);
  }

  /**
   * 更新河流流动效果
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateRiverFlow(deltaTime: number): void {
    // 获取水体网格
    const mesh = this.getMesh();
    if (!mesh) {
      return;
    }

    // 获取水体材质
    const material = mesh.material as THREE.MeshStandardMaterial;
    if (!material) {
      return;
    }

    // 更新UV偏移，模拟水流动效果
    if (material.map) {
      material.map.offset.y -= deltaTime * this.flowSpeed * 0.1;
    }

    // 如果有法线贴图，也更新法线贴图的偏移
    if (material.normalMap) {
      material.normalMap.offset.y -= deltaTime * this.flowSpeed * 0.05;
    }
  }

  /**
   * 设置河流路径点
   * @param points 路径点
   */
  public setPathPoints(points: THREE.Vector3[]): void {
    this.pathPoints = points;

    // 重新创建河流
    this.createRiverCurve();
    this.createRiverGeometry();

    if (this.generateBanks) {
      this.createBankGeometry();
    }

    this.createBedGeometry();

    // 标记需要更新
    this.setNeedsUpdate(true);
  }

  /**
   * 设置河流宽度
   * @param width 宽度
   */
  public setWidth(width: number): void {
    this.width = width;

    // 重新创建河流几何体
    this.createRiverGeometry();

    if (this.generateBanks) {
      this.createBankGeometry();
    }

    this.createBedGeometry();

    // 标记需要更新
    this.setNeedsUpdate(true);
  }

  /**
   * 设置河流深度
   * @param depth 深度
   */
  public setDepth(depth: number): void {
    this.depth = depth;

    // 重新创建河床几何体
    this.createBedGeometry();

    // 标记需要更新
    this.setNeedsUpdate(true);
  }
}
