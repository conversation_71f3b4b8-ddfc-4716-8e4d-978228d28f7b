/**
 * 海洋水体组件
 * 用于模拟海洋水体，包括波浪、潮汐等特性
 */
import * as THREE from 'three';
import { WaterBodyComponent, WaterBodyType, WaterBodyShape, WaterBodyConfig } from './WaterBodyComponent';
import { Entity } from '../../core/Entity';
import { Debug } from '../../utils/Debug';

/**
 * 海洋波浪类型
 */
export enum OceanWaveType {
  /** 柔和波浪 */
  GENTLE = 'gentle',
  /** 中等波浪 */
  MODERATE = 'moderate',
  /** 剧烈波浪 */
  ROUGH = 'rough',
  /** 风暴波浪 */
  STORMY = 'stormy'
}

/**
 * 海洋水体配置
 */
export interface OceanWaterConfig extends WaterBodyConfig {
  /** 海洋尺寸 */
  size?: { width: number, depth: number };
  /** 海洋深度 */
  depth?: number;
  /** 海洋分辨率（几何体细分数） */
  resolution?: { width: number, depth: number };
  /** 波浪类型 */
  waveType?: OceanWaveType;
  /** 波浪高度 */
  waveHeight?: number;
  /** 波浪频率 */
  waveFrequency?: number;
  /** 波浪方向 */
  waveDirection?: { x: number, z: number };
  /** 是否启用FFT波浪模拟 */
  useFFTWaves?: boolean;
  /** 是否启用潮汐效果 */
  enableTides?: boolean;
  /** 潮汐周期（秒） */
  tidePeriod?: number;
  /** 潮汐高度 */
  tideHeight?: number;
  /** 是否启用海底地形 */
  enableSeabed?: boolean;
  /** 海底地形高度变化 */
  seabedHeightVariation?: number;
}

/**
 * 海洋水体组件
 */
export class OceanWaterComponent extends WaterBodyComponent {
  /** 海洋尺寸 */
  private size: { width: number, depth: number };
  /** 海洋深度 */
  private depth: number;
  /** 海洋分辨率 */
  private resolution: { width: number, depth: number };
  /** 波浪类型 */
  private waveType: OceanWaveType;
  /** 波浪高度 */
  private waveHeight: number;
  /** 波浪频率 */
  private waveFrequency: number;
  /** 波浪方向 */
  private waveDirection: { x: number, z: number };
  /** 是否启用FFT波浪模拟 */
  private useFFTWaves: boolean;
  /** 是否启用潮汐效果 */
  private enableTides: boolean;
  /** 潮汐周期 */
  private tidePeriod: number;
  /** 潮汐高度 */
  private tideHeight: number;
  /** 是否启用海底地形 */
  private enableSeabed: boolean;
  /** 海底地形高度变化 */
  private seabedHeightVariation: number;
  /** 海洋几何体 */
  private oceanGeometry: THREE.BufferGeometry | null;
  /** 海底几何体 */
  private seabedGeometry: THREE.BufferGeometry | null;
  /** 潮汐时间 */
  private tideTime: number;
  /** 波浪时间 */
  private waveTime: number;
  /** 波浪顶点位置 */
  private waveVertices: Float32Array | null;
  /** 波浪法线 */
  private waveNormals: Float32Array | null;

  /**
   * 构造函数
   * @param entity 实体
   * @param config 配置
   */
  constructor(entity: Entity, config: OceanWaterConfig = {}) {
    // 设置默认配置
    const defaultConfig: OceanWaterConfig = {
      ...config,
      type: WaterBodyType.OCEAN,
      shape: WaterBodyShape.PLANE,
      enableFlow: false
    };

    // 调用父类构造函数
    super(entity, defaultConfig);

    // 设置海洋特定属性
    this.size = config.size || { width: 1000, depth: 1000 };
    this.depth = config.depth || 100;
    this.resolution = config.resolution || { width: 64, depth: 64 };
    this.waveType = config.waveType || OceanWaveType.MODERATE;
    this.waveHeight = config.waveHeight || 1.0;
    this.waveFrequency = config.waveFrequency || 1.0;
    this.waveDirection = config.waveDirection || { x: 1.0, z: 0.5 };
    this.useFFTWaves = config.useFFTWaves !== undefined ? config.useFFTWaves : false;
    this.enableTides = config.enableTides !== undefined ? config.enableTides : true;
    this.tidePeriod = config.tidePeriod || 600; // 10分钟
    this.tideHeight = config.tideHeight || 2.0;
    this.enableSeabed = config.enableSeabed !== undefined ? config.enableSeabed : true;
    this.seabedHeightVariation = config.seabedHeightVariation || 10.0;
    this.oceanGeometry = null;
    this.seabedGeometry = null;
    this.tideTime = 0;
    this.waveTime = 0;
    this.waveVertices = null;
    this.waveNormals = null;

    // 根据波浪类型设置波浪参数
    this.setWaveParametersByType(this.waveType);
  }

  /**
   * 根据波浪类型设置波浪参数
   * @param waveType 波浪类型
   */
  private setWaveParametersByType(waveType: OceanWaveType): void {
    switch (waveType) {
      case OceanWaveType.GENTLE:
        this.waveHeight = 0.5;
        this.waveFrequency = 0.5;
        break;
      case OceanWaveType.MODERATE:
        this.waveHeight = 1.0;
        this.waveFrequency = 1.0;
        break;
      case OceanWaveType.ROUGH:
        this.waveHeight = 2.0;
        this.waveFrequency = 1.5;
        break;
      case OceanWaveType.STORMY:
        this.waveHeight = 3.0;
        this.waveFrequency = 2.0;
        break;
      default:
        this.waveHeight = 1.0;
        this.waveFrequency = 1.0;
        break;
    }
  }

  /**
   * 初始化组件
   */
  public override initialize(): void {
    if (this.initialized) {
      return;
    }

    // 创建海洋几何体
    this.createOceanGeometry();

    // 如果启用海底地形，创建海底几何体
    if (this.enableSeabed) {
      this.createSeabedGeometry();
    }

    // 创建水体材质
    this.createWaterMaterial();

    // 初始化水面高度图
    this.initializeHeightMap();

    // 初始化水面法线图
    this.initializeNormalMap();

    // 初始化水流速度图
    this.initializeVelocityMap();

    // 如果启用粒子，初始化粒子系统
    if (this.isParticlesEnabled()) {
      this.initializeParticleSystem();
    }

    // 初始化波浪顶点和法线数组
    this.initializeWaveArrays();

    this.initialized = true;
    Debug.log('OceanWaterComponent', '海洋水体组件初始化完成');
  }

  /**
   * 创建海洋几何体
   */
  private createOceanGeometry(): void {
    // 创建平面几何体
    const geometry = new THREE.PlaneGeometry(
      this.size.width,
      this.size.depth,
      this.resolution.width,
      this.resolution.depth
    );

    // 将几何体旋转为水平面
    geometry.rotateX(-Math.PI / 2);

    // 将几何体中心移到原点
    geometry.translate(0, 0, 0);

    this.oceanGeometry = geometry;
  }

  /**
   * 创建海底几何体
   */
  private createSeabedGeometry(): void {
    // 创建平面几何体
    const geometry = new THREE.PlaneGeometry(
      this.size.width,
      this.size.depth,
      this.resolution.width / 2, // 海底分辨率可以低一些
      this.resolution.depth / 2
    );

    // 将几何体旋转为水平面
    geometry.rotateX(-Math.PI / 2);

    // 将几何体移动到海底
    geometry.translate(0, -this.depth, 0);

    // 获取顶点位置
    const positions = geometry.getAttribute('position').array;

    // 添加随机高度变化，模拟海底地形
    for (let i = 0; i < positions.length; i += 3) {
      // 跳过边缘顶点，保持边缘平滑
      const x = positions[i];
      const z = positions[i + 2];
      const distanceFromCenter = Math.sqrt(x * x + z * z);
      const normalizedDistance = distanceFromCenter / (Math.min(this.size.width, this.size.depth) / 2);

      if (normalizedDistance < 0.9) {
        // 使用柏林噪声或简单的正弦函数模拟地形
        const noise = Math.sin(x * 0.05) * Math.cos(z * 0.05) * this.seabedHeightVariation;
        positions[i + 1] += noise;
      }
    }

    // 更新几何体
    geometry.getAttribute('position').needsUpdate = true;

    // 重新计算法线
    geometry.computeVertexNormals();

    this.seabedGeometry = geometry;
  }

  /**
   * 初始化波浪数组
   */
  private initializeWaveArrays(): void {
    if (!this.oceanGeometry) {
      return;
    }

    // 获取顶点数量
    const positions = this.oceanGeometry.getAttribute('position').array;
    const vertexCount = positions.length / 3;

    // 创建波浪顶点和法线数组
    this.waveVertices = new Float32Array(positions);
    this.waveNormals = new Float32Array(vertexCount * 3);

    // 初始化法线
    for (let i = 0; i < vertexCount; i++) {
      this.waveNormals[i * 3 + 1] = 1; // 默认法线向上
    }
  }

  /**
   * 更新组件
   * @param deltaTime 帧间隔时间（秒）
   */
  public override update(deltaTime: number): void {
    super.update(deltaTime);

    // 更新波浪
    this.updateWaves(deltaTime);

    // 更新潮汐
    if (this.enableTides) {
      this.updateTides(deltaTime);
    }
  }

  /**
   * 更新波浪
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateWaves(deltaTime: number): void {
    if (!this.oceanGeometry || !this.waveVertices || !this.waveNormals) {
      return;
    }

    // 更新波浪时间
    this.waveTime += deltaTime * this.waveFrequency;

    // 获取几何体属性
    const positionAttribute = this.oceanGeometry.getAttribute('position');
    const normalAttribute = this.oceanGeometry.getAttribute('normal');
    const positions = positionAttribute.array;
    const vertexCount = positions.length / 3;

    // 如果使用FFT波浪模拟
    if (this.useFFTWaves) {
      this.updateFFTWaves(deltaTime, positions, vertexCount);
    } else {
      this.updateSimpleWaves(deltaTime, positions, vertexCount);
    }

    // 更新几何体
    positionAttribute.needsUpdate = true;
    normalAttribute.needsUpdate = true;

    // 更新边界球
    this.oceanGeometry.computeBoundingSphere();
  }

  /**
   * 更新简单波浪
   * @param deltaTime 帧间隔时间（秒）
   * @param positions 顶点位置数组
   * @param vertexCount 顶点数量
   */
  private updateSimpleWaves(deltaTime: number, positions: ArrayLike<number>, vertexCount: number): void {
    // 获取原始顶点位置
    const originalPositions = this.waveVertices;

    // 更新每个顶点的位置
    for (let i = 0; i < vertexCount; i++) {
      const index = i * 3;
      const x = originalPositions[index];
      const z = originalPositions[index + 2];

      // 计算波浪高度
      let height = 0;

      // 添加多个正弦波，模拟复杂的波浪
      // 波浪1：主要波浪
      height += Math.sin(x * 0.1 * this.waveDirection.x + z * 0.1 * this.waveDirection.z + this.waveTime) * this.waveHeight;

      // 波浪2：次要波浪，频率更高
      height += Math.sin(x * 0.2 * this.waveDirection.z - z * 0.2 * this.waveDirection.x + this.waveTime * 0.7) * this.waveHeight * 0.3;

      // 波浪3：小波浪，频率更高
      height += Math.sin(x * 0.4 + z * 0.4 + this.waveTime * 1.3) * this.waveHeight * 0.1;

      // 更新顶点位置
      positions[index + 1] = originalPositions[index + 1] + height;

      // 计算法线
      // 使用中心差分法计算法线
      const epsilon = 0.01;
      const heightX1 = Math.sin((x + epsilon) * 0.1 * this.waveDirection.x + z * 0.1 * this.waveDirection.z + this.waveTime) * this.waveHeight;
      const heightX2 = Math.sin((x - epsilon) * 0.1 * this.waveDirection.x + z * 0.1 * this.waveDirection.z + this.waveTime) * this.waveHeight;
      const heightZ1 = Math.sin(x * 0.1 * this.waveDirection.x + (z + epsilon) * 0.1 * this.waveDirection.z + this.waveTime) * this.waveHeight;
      const heightZ2 = Math.sin(x * 0.1 * this.waveDirection.x + (z - epsilon) * 0.1 * this.waveDirection.z + this.waveTime) * this.waveHeight;

      const gradientX = (heightX1 - heightX2) / (2 * epsilon);
      const gradientZ = (heightZ1 - heightZ2) / (2 * epsilon);

      // 法线是梯度的负方向
      const normal = new THREE.Vector3(-gradientX, 1, -gradientZ).normalize();

      // 更新法线
      this.waveNormals[index] = normal.x;
      this.waveNormals[index + 1] = normal.y;
      this.waveNormals[index + 2] = normal.z;

      // 更新几何体法线
      const normalAttribute = this.oceanGeometry!.getAttribute('normal');
      normalAttribute.setXYZ(i, normal.x, normal.y, normal.z);
    }
  }

  /**
   * 更新FFT波浪
   * @param deltaTime 帧间隔时间（秒）
   * @param positions 顶点位置数组
   * @param vertexCount 顶点数量
   */
  private updateFFTWaves(deltaTime: number, positions: ArrayLike<number>, vertexCount: number): void {
    // FFT波浪模拟需要更复杂的实现
    // 这里只是一个简化版本，实际应用中可能需要使用WebWorker或GPU计算

    // 获取原始顶点位置
    const originalPositions = this.waveVertices;

    // 更新每个顶点的位置
    for (let i = 0; i < vertexCount; i++) {
      const index = i * 3;
      const x = originalPositions[index];
      const z = originalPositions[index + 2];

      // 计算波浪高度
      let height = 0;

      // 使用更多的正弦波，模拟FFT波浪
      for (let j = 1; j <= 5; j++) {
        const frequency = j * 0.05;
        const amplitude = this.waveHeight / j;
        const phase = this.waveTime * j * 0.2;

        height += Math.sin(x * frequency * this.waveDirection.x + z * frequency * this.waveDirection.z + phase) * amplitude;
      }

      // 更新顶点位置
      positions[index + 1] = originalPositions[index + 1] + height;

      // 计算法线
      // 使用中心差分法计算法线
      const epsilon = 0.01;
      let heightX1 = 0;
      let heightX2 = 0;
      let heightZ1 = 0;
      let heightZ2 = 0;

      for (let j = 1; j <= 5; j++) {
        const frequency = j * 0.05;
        const amplitude = this.waveHeight / j;
        const phase = this.waveTime * j * 0.2;

        heightX1 += Math.sin((x + epsilon) * frequency * this.waveDirection.x + z * frequency * this.waveDirection.z + phase) * amplitude;
        heightX2 += Math.sin((x - epsilon) * frequency * this.waveDirection.x + z * frequency * this.waveDirection.z + phase) * amplitude;
        heightZ1 += Math.sin(x * frequency * this.waveDirection.x + (z + epsilon) * frequency * this.waveDirection.z + phase) * amplitude;
        heightZ2 += Math.sin(x * frequency * this.waveDirection.x + (z - epsilon) * frequency * this.waveDirection.z + phase) * amplitude;
      }

      const gradientX = (heightX1 - heightX2) / (2 * epsilon);
      const gradientZ = (heightZ1 - heightZ2) / (2 * epsilon);

      // 法线是梯度的负方向
      const normal = new THREE.Vector3(-gradientX, 1, -gradientZ).normalize();

      // 更新法线
      this.waveNormals[index] = normal.x;
      this.waveNormals[index + 1] = normal.y;
      this.waveNormals[index + 2] = normal.z;

      // 更新几何体法线
      const normalAttribute = this.oceanGeometry!.getAttribute('normal');
      normalAttribute.setXYZ(i, normal.x, normal.y, normal.z);
    }
  }

  /**
   * 更新潮汐
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateTides(deltaTime: number): void {
    if (!this.oceanGeometry || !this.waveVertices) {
      return;
    }

    // 更新潮汐时间
    this.tideTime += deltaTime;

    // 计算潮汐高度
    const tidePhase = (this.tideTime % this.tidePeriod) / this.tidePeriod;
    const tideHeight = Math.sin(tidePhase * Math.PI * 2) * this.tideHeight;

    // 获取几何体属性
    const positionAttribute = this.oceanGeometry.getAttribute('position');
    const positions = positionAttribute.array;
    const vertexCount = positions.length / 3;

    // 更新每个顶点的位置
    for (let i = 0; i < vertexCount; i++) {
      const index = i * 3;
      positions[index + 1] += tideHeight;
    }

    // 更新几何体
    positionAttribute.needsUpdate = true;
  }

  /**
   * 设置波浪类型
   * @param waveType 波浪类型
   */
  public setWaveType(waveType: OceanWaveType): void {
    this.waveType = waveType;
    this.setWaveParametersByType(waveType);
  }

  /**
   * 设置波浪参数
   * @param height 波浪高度
   * @param frequency 波浪频率
   * @param direction 波浪方向
   */
  public setWaveParameters(height?: number, frequency?: number, direction?: { x: number, z: number }): void {
    if (height !== undefined) {
      this.waveHeight = height;
    }

    if (frequency !== undefined) {
      this.waveFrequency = frequency;
    }

    if (direction !== undefined) {
      this.waveDirection = direction;
    }
  }

  /**
   * 设置海洋尺寸
   * @param width 宽度
   * @param depth 深度
   */
  public setSize(width: number, depth: number): void {
    this.size = { width, depth };

    // 重新创建海洋几何体
    this.createOceanGeometry();

    // 如果启用海底地形，重新创建海底几何体
    if (this.enableSeabed) {
      this.createSeabedGeometry();
    }

    // 重新初始化波浪数组
    this.initializeWaveArrays();

    // 标记需要更新
    this.setNeedsUpdate(true);
  }

  /**
   * 设置海洋深度
   * @param depth 深度
   */
  public setDepth(depth: number): void {
    this.depth = depth;

    // 如果启用海底地形，重新创建海底几何体
    if (this.enableSeabed) {
      this.createSeabedGeometry();
    }

    // 标记需要更新
    this.setNeedsUpdate(true);
  }

  /**
   * 设置潮汐参数
   * @param enable 是否启用潮汐
   * @param period 潮汐周期
   * @param height 潮汐高度
   */
  public setTideParameters(enable: boolean, period?: number, height?: number): void {
    this.enableTides = enable;

    if (period !== undefined) {
      this.tidePeriod = period;
    }

    if (height !== undefined) {
      this.tideHeight = height;
    }
  }

  /**
   * 设置海底参数
   * @param enable 是否启用海底地形
   * @param heightVariation 高度变化
   */
  public setSeabedParameters(enable: boolean, heightVariation?: number): void {
    this.enableSeabed = enable;

    if (heightVariation !== undefined) {
      this.seabedHeightVariation = heightVariation;
    }

    // 如果启用海底地形，重新创建海底几何体
    if (this.enableSeabed) {
      this.createSeabedGeometry();
    }

    // 标记需要更新
    this.setNeedsUpdate(true);
  }

  /**
   * 设置FFT波浪模拟
   * @param useFFT 是否使用FFT波浪模拟
   */
  public setUseFFTWaves(useFFT: boolean): void {
    this.useFFTWaves = useFFT;
  }
}