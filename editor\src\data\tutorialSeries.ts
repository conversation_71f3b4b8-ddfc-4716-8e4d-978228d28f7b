/**
 * 教程系列数据
 * 定义编辑器中的教程系列
 */
import { TutorialSeries, TutorialDifficulty, TutorialType } from '../services/TutorialRecommendationService';

/**
 * 获取教程项的唯一键
 */
function getItemKey(type: TutorialType, id: string): string {
  return `${type}:${id}`;
}

/**
 * 教程系列数据
 */
export const tutorialSeries: TutorialSeries[] = [
  // 新手入门系列
  {
    id: 'getting-started',
    title: '新手入门系列',
    description: '适合初次使用IR引擎编辑器的用户，介绍基本界面和操作，帮助您快速上手。',
    category: 'getting-started',
    difficulty: TutorialDifficulty.BEGINNER,
    thumbnailUrl: '/assets/images/tutorials/series/getting-started.jpg',
    tags: ['入门', '基础', '界面', '操作'],
    tutorials: [
      getItemKey(TutorialType.VIDEO, 'editor-basics'),
      getItemKey(TutorialType.INTERACTIVE, 'editor-basics'),
      getItem<PERSON>ey(TutorialType.VIDEO, 'project-creation'),
      getItemKey(TutorialType.EXAMPLE, 'editor-basics'),
      getItemKey(TutorialType.VIDEO, 'scene-editing'),
      getItemKey(TutorialType.INTERACTIVE, 'scene-navigation'),
    ],
    featured: true,
    popularity: 95,
  },
  
  // 场景构建系列
  {
    id: 'scene-building',
    title: '场景构建系列',
    description: '学习如何从零开始构建完整的3D场景，包括地形、建筑、植被、光照和后期处理效果。',
    category: 'scene-building',
    difficulty: TutorialDifficulty.INTERMEDIATE,
    thumbnailUrl: '/assets/images/tutorials/series/scene-building.jpg',
    tags: ['场景', '地形', '建筑', '植被', '光照', '后期处理'],
    tutorials: [
      getItemKey(TutorialType.VIDEO, 'terrain-creation'),
      getItemKey(TutorialType.EXAMPLE, 'scene-building-tutorial'),
      getItemKey(TutorialType.VIDEO, 'vegetation-system'),
      getItemKey(TutorialType.INTERACTIVE, 'lighting-setup'),
      getItemKey(TutorialType.VIDEO, 'post-processing'),
      getItemKey(TutorialType.INTERACTIVE, 'scene-optimization'),
    ],
    featured: true,
    popularity: 85,
  },
  
  // 角色创建系列
  {
    id: 'character-creation',
    title: '角色创建系列',
    description: '学习如何导入和设置3D角色模型，配置骨骼和动画系统，添加控制器和交互功能。',
    category: 'character',
    difficulty: TutorialDifficulty.INTERMEDIATE,
    thumbnailUrl: '/assets/images/tutorials/series/character-creation.jpg',
    tags: ['角色', '动画', '骨骼', '控制器', '交互'],
    tutorials: [
      getItemKey(TutorialType.VIDEO, 'character-import'),
      getItemKey(TutorialType.EXAMPLE, 'character-creation-tutorial'),
      getItemKey(TutorialType.VIDEO, 'rigging-basics'),
      getItemKey(TutorialType.INTERACTIVE, 'animation-setup'),
      getItemKey(TutorialType.VIDEO, 'animation-state-machine'),
      getItemKey(TutorialType.INTERACTIVE, 'character-controller'),
      getItemKey(TutorialType.VIDEO, 'character-interaction'),
    ],
    featured: true,
    popularity: 90,
  },
  
  // 材质编辑系列
  {
    id: 'material-editing',
    title: '材质编辑系列',
    description: '深入学习IR引擎的材质编辑系统，掌握PBR材质、着色器编辑和高级材质技巧。',
    category: 'materials',
    difficulty: TutorialDifficulty.INTERMEDIATE,
    thumbnailUrl: '/assets/images/tutorials/series/material-editing.jpg',
    tags: ['材质', 'PBR', '着色器', '纹理'],
    tutorials: [
      getItemKey(TutorialType.VIDEO, 'material-basics'),
      getItemKey(TutorialType.INTERACTIVE, 'material-editor'),
      getItemKey(TutorialType.VIDEO, 'pbr-workflow'),
      getItemKey(TutorialType.EXAMPLE, 'material-editor'),
      getItemKey(TutorialType.VIDEO, 'shader-editing'),
      getItemKey(TutorialType.INTERACTIVE, 'advanced-materials'),
    ],
    popularity: 80,
  },
  
  // 物理系统系列
  {
    id: 'physics-system',
    title: '物理系统系列',
    description: '学习IR引擎的物理系统，包括刚体物理、碰撞检测、物理约束和高级物理模拟。',
    category: 'physics',
    difficulty: TutorialDifficulty.ADVANCED,
    thumbnailUrl: '/assets/images/tutorials/series/physics-system.jpg',
    tags: ['物理', '碰撞', '约束', '刚体', '软体'],
    tutorials: [
      getItemKey(TutorialType.VIDEO, 'physics-basics'),
      getItemKey(TutorialType.INTERACTIVE, 'rigid-body-physics'),
      getItemKey(TutorialType.VIDEO, 'collision-detection'),
      getItemKey(TutorialType.EXAMPLE, 'physics-demo'),
      getItemKey(TutorialType.VIDEO, 'physics-constraints'),
      getItemKey(TutorialType.INTERACTIVE, 'soft-body-physics'),
      getItemKey(TutorialType.VIDEO, 'advanced-physics'),
    ],
    popularity: 75,
  },
  
  // 视觉脚本系列
  {
    id: 'visual-scripting',
    title: '视觉脚本系列',
    description: '学习IR引擎的视觉脚本系统，无需编程即可创建交互式内容和游戏逻辑。',
    category: 'scripting',
    difficulty: TutorialDifficulty.INTERMEDIATE,
    thumbnailUrl: '/assets/images/tutorials/series/visual-scripting.jpg',
    tags: ['脚本', '逻辑', '交互', '事件', '节点'],
    tutorials: [
      getItemKey(TutorialType.VIDEO, 'visualscript-basics'),
      getItemKey(TutorialType.INTERACTIVE, 'visualscript-editor'),
      getItemKey(TutorialType.VIDEO, 'event-handling'),
      getItemKey(TutorialType.EXAMPLE, 'visualscript-demo'),
      getItemKey(TutorialType.VIDEO, 'logic-nodes'),
      getItemKey(TutorialType.INTERACTIVE, 'advanced-visualscript'),
    ],
    popularity: 85,
  },
  
  // 网络多人系列
  {
    id: 'multiplayer',
    title: '网络多人系列',
    description: '学习如何创建多人网络场景，实现实体同步、状态同步和多人交互功能。',
    category: 'networking',
    difficulty: TutorialDifficulty.ADVANCED,
    thumbnailUrl: '/assets/images/tutorials/series/multiplayer.jpg',
    tags: ['网络', '多人', '同步', '交互', '实时'],
    tutorials: [
      getItemKey(TutorialType.VIDEO, 'networking-basics'),
      getItemKey(TutorialType.INTERACTIVE, 'network-setup'),
      getItemKey(TutorialType.VIDEO, 'entity-synchronization'),
      getItemKey(TutorialType.EXAMPLE, 'multiplayer-tutorial'),
      getItemKey(TutorialType.VIDEO, 'state-synchronization'),
      getItemKey(TutorialType.INTERACTIVE, 'multiplayer-interaction'),
    ],
    popularity: 70,
  },
  
  // 性能优化系列
  {
    id: 'performance-optimization',
    title: '性能优化系列',
    description: '学习IR引擎的性能优化技术，提高应用的帧率和响应速度，优化资源使用。',
    category: 'optimization',
    difficulty: TutorialDifficulty.ADVANCED,
    thumbnailUrl: '/assets/images/tutorials/series/performance-optimization.jpg',
    tags: ['优化', '性能', '帧率', 'LOD', '实例化', '剔除'],
    tutorials: [
      getItemKey(TutorialType.VIDEO, 'performance-analysis'),
      getItemKey(TutorialType.INTERACTIVE, 'profiling-tools'),
      getItemKey(TutorialType.VIDEO, 'rendering-optimization'),
      getItemKey(TutorialType.EXAMPLE, 'performance-optimization'),
      getItemKey(TutorialType.VIDEO, 'memory-optimization'),
      getItemKey(TutorialType.INTERACTIVE, 'advanced-optimization'),
    ],
    popularity: 65,
  },
  
  // UI设计系列
  {
    id: 'ui-design',
    title: 'UI设计系列',
    description: '学习IR引擎的UI系统，创建2D和3D用户界面，实现交互和动画效果。',
    category: 'ui',
    difficulty: TutorialDifficulty.INTERMEDIATE,
    thumbnailUrl: '/assets/images/tutorials/series/ui-design.jpg',
    tags: ['UI', '界面', '交互', '动画', '响应式'],
    tutorials: [
      getItemKey(TutorialType.VIDEO, 'ui-basics'),
      getItemKey(TutorialType.INTERACTIVE, 'ui-editor'),
      getItemKey(TutorialType.VIDEO, '2d-ui-design'),
      getItemKey(TutorialType.EXAMPLE, 'ui-design'),
      getItemKey(TutorialType.VIDEO, '3d-ui-integration'),
      getItemKey(TutorialType.INTERACTIVE, 'ui-animation'),
    ],
    popularity: 75,
  },
  
  // 高级动画系列
  {
    id: 'advanced-animation',
    title: '高级动画系列',
    description: '深入学习IR引擎的动画系统，包括动画混合、IK系统、程序化动画和面部动画。',
    category: 'animation',
    difficulty: TutorialDifficulty.EXPERT,
    thumbnailUrl: '/assets/images/tutorials/series/advanced-animation.jpg',
    tags: ['动画', '混合', 'IK', '程序化', '面部'],
    tutorials: [
      getItemKey(TutorialType.VIDEO, 'animation-blending'),
      getItemKey(TutorialType.INTERACTIVE, 'animation-state-machine-advanced'),
      getItemKey(TutorialType.VIDEO, 'ik-system'),
      getItemKey(TutorialType.EXAMPLE, 'advanced-facial-animation'),
      getItemKey(TutorialType.VIDEO, 'procedural-animation'),
      getItemKey(TutorialType.INTERACTIVE, 'facial-animation'),
    ],
    popularity: 60,
  },
];
