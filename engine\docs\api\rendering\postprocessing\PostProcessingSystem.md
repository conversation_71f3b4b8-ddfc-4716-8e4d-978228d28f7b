# 后处理系统 (PostProcessingSystem)

后处理系统是IR引擎的高级渲染功能之一，用于在场景渲染完成后应用各种视觉效果。它支持多种效果，如泛光、环境光遮蔽、屏幕空间反射等，并允许自定义效果链。

## 基本用法

```typescript
import { Entity } from '../../../core/Entity';
import { PostProcessingSystem } from '../../../rendering/postprocessing/PostProcessingSystem';
import { BloomEffect } from '../../../rendering/postprocessing/effects/BloomEffect';
import { SSAOEffect } from '../../../rendering/postprocessing/effects/SSAOEffect';

// 创建后处理系统实体
const postProcessingEntity = new Entity('后处理系统');

// 创建后处理系统
const postProcessingSystem = new PostProcessingSystem({
  enabled: true,
  autoResize: true
});

// 创建泛光效果
const bloomEffect = new BloomEffect({
  name: 'Bloom',
  enabled: true,
  intensity: 1.5,
  threshold: 0.6,
  radius: 0.5
});

// 创建SSAO效果
const ssaoEffect = new SSAOEffect({
  name: 'SSAO',
  enabled: true,
  radius: 0.1825,
  bias: 0.025,
  intensity: 1.0,
  samples: 16
});

// 添加效果到后处理系统
postProcessingSystem.addEffect(bloomEffect);
postProcessingSystem.addEffect(ssaoEffect);

// 添加后处理系统到实体
postProcessingEntity.addComponent(postProcessingSystem);

// 添加后处理系统实体到场景
scene.addEntity(postProcessingEntity);
```

## 构造函数

```typescript
constructor(options?: PostProcessingSystemOptions)
```

### 参数

- `options?: PostProcessingSystemOptions` - 后处理系统选项

### PostProcessingSystemOptions

```typescript
interface PostProcessingSystemOptions {
  enabled?: boolean;    // 是否启用后处理系统
  autoResize?: boolean; // 是否自动调整大小
  width?: number;       // 渲染宽度
  height?: number;      // 渲染高度
  renderToScreen?: boolean; // 是否直接渲染到屏幕
}
```

## 属性

| 属性名 | 类型 | 描述 |
|--------|------|------|
| enabled | boolean | 是否启用后处理系统 |
| autoResize | boolean | 是否自动调整大小 |
| width | number | 渲染宽度 |
| height | number | 渲染高度 |
| renderToScreen | boolean | 是否直接渲染到屏幕 |
| effects | Effect[] | 效果列表 |

## 方法

### addEffect

添加后处理效果。

```typescript
addEffect(effect: Effect): void
```

#### 参数

- `effect: Effect` - 要添加的后处理效果

#### 示例

```typescript
// 创建泛光效果
const bloomEffect = new BloomEffect({
  name: 'Bloom',
  enabled: true,
  intensity: 1.5,
  threshold: 0.6,
  radius: 0.5
});

// 添加效果到后处理系统
postProcessingSystem.addEffect(bloomEffect);
```

### removeEffect

移除后处理效果。

```typescript
removeEffect(effectOrName: Effect | string): boolean
```

#### 参数

- `effectOrName: Effect | string` - 要移除的后处理效果或效果名称

#### 返回值

- `boolean` - 是否成功移除效果

#### 示例

```typescript
// 通过效果实例移除
postProcessingSystem.removeEffect(bloomEffect);

// 通过效果名称移除
postProcessingSystem.removeEffect('Bloom');
```

### getEffect

获取后处理效果。

```typescript
getEffect<T extends Effect>(name: string): T | undefined
```

#### 参数

- `name: string` - 效果名称

#### 返回值

- `T | undefined` - 找到的效果或undefined

#### 示例

```typescript
// 获取泛光效果
const bloomEffect = postProcessingSystem.getEffect<BloomEffect>('Bloom');

if (bloomEffect) {
  // 调整泛光效果参数
  bloomEffect.setIntensity(2.0);
  bloomEffect.setThreshold(0.7);
}
```

### setEnabled

设置是否启用后处理系统。

```typescript
setEnabled(enabled: boolean): void
```

#### 参数

- `enabled: boolean` - 是否启用后处理系统

#### 示例

```typescript
// 启用后处理系统
postProcessingSystem.setEnabled(true);

// 禁用后处理系统
postProcessingSystem.setEnabled(false);
```

### setSize

设置后处理系统的渲染大小。

```typescript
setSize(width: number, height: number): void
```

#### 参数

- `width: number` - 渲染宽度
- `height: number` - 渲染高度

#### 示例

```typescript
// 设置后处理系统大小
postProcessingSystem.setSize(1920, 1080);

// 设置为窗口大小
postProcessingSystem.setSize(window.innerWidth, window.innerHeight);
```

### setAutoResize

设置是否自动调整大小。

```typescript
setAutoResize(autoResize: boolean): void
```

#### 参数

- `autoResize: boolean` - 是否自动调整大小

#### 示例

```typescript
// 启用自动调整大小
postProcessingSystem.setAutoResize(true);

// 禁用自动调整大小
postProcessingSystem.setAutoResize(false);
```

### setRenderToScreen

设置是否直接渲染到屏幕。

```typescript
setRenderToScreen(renderToScreen: boolean): void
```

#### 参数

- `renderToScreen: boolean` - 是否直接渲染到屏幕

#### 示例

```typescript
// 直接渲染到屏幕
postProcessingSystem.setRenderToScreen(true);

// 渲染到中间缓冲区
postProcessingSystem.setRenderToScreen(false);
```

### render

渲染后处理效果。

```typescript
render(renderer: THREE.WebGLRenderer, scene: THREE.Scene, camera: THREE.Camera): void
```

#### 参数

- `renderer: THREE.WebGLRenderer` - Three.js渲染器
- `scene: THREE.Scene` - Three.js场景
- `camera: THREE.Camera` - Three.js相机

#### 示例

```typescript
// 在渲染循环中渲染后处理效果
function render() {
  // 渲染场景到后处理系统
  postProcessingSystem.render(renderer, scene, camera);
  
  requestAnimationFrame(render);
}

render();
```

### dispose

释放后处理系统资源。

```typescript
dispose(): void
```

#### 示例

```typescript
// 释放后处理系统资源
postProcessingSystem.dispose();
```

## 支持的效果

后处理系统支持以下效果：

| 效果名 | 类 | 描述 |
|--------|------|------|
| 泛光 | BloomEffect | 为明亮区域添加光晕效果 |
| 环境光遮蔽 | SSAOEffect | 模拟环境光遮蔽，增强深度感 |
| 屏幕空间反射 | SSREffect | 模拟表面反射，增强真实感 |
| 屏幕空间全局光照 | SSGIEffect | 模拟全局光照，增强光照真实感 |
| 运动模糊 | MotionBlurEffect | 模拟运动物体的模糊效果 |
| 景深 | DepthOfFieldEffect | 模拟相机景深效果 |
| 色调映射 | ToneMappingEffect | 将HDR颜色映射到LDR范围 |
| 颜色校正 | ColorCorrectionEffect | 调整颜色、对比度、饱和度等 |
| 抗锯齿 | FXAAEffect | 快速近似抗锯齿 |
| 色差 | ChromaticAberrationEffect | 模拟镜头色差效果 |
| 噪点 | NoiseEffect | 添加胶片颗粒或数字噪点 |
| 体积光 | VolumetricLightEffect | 模拟光线散射效果 |

## 性能优化

为了获得最佳性能，请考虑以下建议：

1. 仅启用必要的效果
2. 对于移动设备，降低效果质量或禁用部分效果
3. 考虑使用半分辨率渲染某些效果
4. 监控帧率，动态调整效果质量
5. 对于静态场景，考虑使用预渲染或缓存

## 自定义效果

您可以通过继承`Effect`基类来创建自定义效果：

```typescript
import { Effect, EffectOptions } from '../../../rendering/postprocessing/Effect';
import * as THREE from 'three';

// 自定义效果选项
interface CustomEffectOptions extends EffectOptions {
  intensity?: number;
}

// 自定义效果
class CustomEffect extends Effect {
  private intensity: number;
  private material: THREE.ShaderMaterial;
  
  constructor(options?: CustomEffectOptions) {
    super(options);
    
    this.intensity = options?.intensity ?? 1.0;
    
    // 创建着色器材质
    this.material = new THREE.ShaderMaterial({
      uniforms: {
        tDiffuse: { value: null },
        intensity: { value: this.intensity }
      },
      vertexShader: /* 顶点着色器代码 */,
      fragmentShader: /* 片段着色器代码 */
    });
  }
  
  // 设置强度
  setIntensity(intensity: number): void {
    this.intensity = intensity;
    this.material.uniforms.intensity.value = intensity;
  }
  
  // 渲染效果
  render(renderer: THREE.WebGLRenderer, inputBuffer: THREE.WebGLRenderTarget, outputBuffer: THREE.WebGLRenderTarget): void {
    this.material.uniforms.tDiffuse.value = inputBuffer.texture;
    this.renderPass(renderer, this.material, outputBuffer);
  }
}
```
