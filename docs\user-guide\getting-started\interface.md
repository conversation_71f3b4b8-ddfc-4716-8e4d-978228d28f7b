# 编辑器界面介绍

IR引擎编辑器提供了直观、高效的用户界面，帮助您轻松创建和编辑3D场景。本文将介绍编辑器的界面布局和主要组件。

## 界面概览

IR引擎编辑器界面由以下几个主要部分组成：

![编辑器界面概览](../../assets/images/editor-interface-overview.png)

1. **菜单栏** - 位于顶部，包含各种功能菜单
2. **工具栏** - 位于菜单栏下方，提供常用工具按钮
3. **场景视图** - 中央区域，显示3D场景
4. **面板区域** - 周围区域，包含各种功能面板
5. **状态栏** - 底部区域，显示当前状态和信息

## 菜单栏

菜单栏包含以下主要菜单：

### 文件菜单

- **新建项目** - 创建新项目
- **打开项目** - 打开现有项目
- **保存项目** - 保存当前项目
- **另存为** - 将项目保存为新文件
- **导入** - 导入资产或场景
- **导出** - 导出场景或资产
- **示例** - 打开示例项目
- **退出** - 退出编辑器

### 编辑菜单

- **撤销** - 撤销上一步操作
- **重做** - 重做上一步操作
- **复制** - 复制选中对象
- **粘贴** - 粘贴复制的对象
- **删除** - 删除选中对象
- **复制** - 复制选中对象
- **选择全部** - 选择场景中所有对象
- **取消选择** - 取消当前选择
- **查找** - 查找对象或资产

### 视图菜单

- **视图模式** - 切换不同的视图模式（透视、正交等）
- **显示网格** - 显示/隐藏网格
- **显示坐标轴** - 显示/隐藏坐标轴
- **显示面板** - 显示/隐藏各种面板
- **全屏** - 切换全屏模式
- **重置视图** - 重置视图位置和角度

### 工具菜单

- **变换工具** - 移动、旋转、缩放工具
- **吸附工具** - 网格吸附、对象吸附等
- **测量工具** - 测量距离、角度等
- **注释工具** - 添加场景注释
- **截图工具** - 截取场景截图

## 工具栏

工具栏提供了常用工具的快速访问按钮：

![工具栏](../../assets/images/editor-toolbar.png)

- **变换工具** - 移动(W)、旋转(E)、缩放(R)
- **变换空间** - 世界空间/局部空间切换
- **吸附控制** - 开启/关闭网格吸附
- **播放/停止** - 预览场景动画
- **保存** - 快速保存项目

## 面板区域

编辑器提供了多个功能面板，可以根据需要显示、隐藏或重新排列：

### 场景层次面板

显示场景中所有对象的层次结构，允许您选择、重命名、重新排列对象。

![场景层次面板](../../assets/images/hierarchy-panel.png)

主要功能：
- 显示场景对象层次结构
- 选择、重命名对象
- 创建父子关系
- 显示/隐藏对象
- 锁定/解锁对象

### 属性面板

显示当前选中对象的属性，允许您修改这些属性。

![属性面板](../../assets/images/inspector-panel.png)

主要功能：
- 显示对象变换信息（位置、旋转、缩放）
- 显示和编辑组件属性
- 添加/移除组件
- 预设管理

### 资产面板

管理项目中的所有资产，如模型、材质、纹理、脚本等。

![资产面板](../../assets/images/assets-panel.png)

主要功能：
- 浏览项目资产
- 导入新资产
- 创建资产文件夹
- 搜索资产
- 预览资产

### 场景面板

管理项目中的场景文件。

![场景面板](../../assets/images/scene-panel.png)

主要功能：
- 浏览项目场景
- 创建新场景
- 打开场景
- 保存场景
- 场景设置

### 控制台面板

显示日志信息和错误消息。

![控制台面板](../../assets/images/console-panel.png)

主要功能：
- 显示日志信息
- 显示错误和警告
- 过滤消息
- 清除日志

### 协作面板

管理多人协作编辑功能。

![协作面板](../../assets/images/collaboration-panel.png)

主要功能：
- 显示在线用户
- 聊天功能
- 权限管理
- 冲突解决

## 场景视图

场景视图是编辑器的核心部分，显示当前场景的3D视图。

![场景视图](../../assets/images/scene-view.png)

主要功能：
- 显示3D场景
- 选择和操作对象
- 预览光照和材质
- 调整视角和视图模式

### 导航控制

- **旋转视图** - 按住鼠标右键并拖动
- **平移视图** - 按住鼠标中键并拖动
- **缩放视图** - 滚动鼠标滚轮
- **聚焦对象** - 选中对象后按F键

## 状态栏

状态栏显示当前编辑器状态和有用信息：

![状态栏](../../assets/images/status-bar.png)

- **选中对象信息** - 显示当前选中对象的基本信息
- **坐标信息** - 显示鼠标指针位置的坐标
- **性能指标** - 显示FPS和渲染统计信息
- **协作状态** - 显示协作会话状态
- **编辑模式** - 显示当前编辑模式

## 自定义界面

IR引擎编辑器允许您根据个人偏好自定义界面布局：

- **调整面板大小** - 拖动面板边缘
- **重新排列面板** - 拖动面板标签到新位置
- **分离面板** - 将面板拖出主窗口
- **重置布局** - 通过"视图 > 重置布局"恢复默认布局

## 下一步

现在您已经了解了编辑器的界面布局，可以继续学习[基本操作](./basic-operations.md)，开始创建您的第一个场景。
