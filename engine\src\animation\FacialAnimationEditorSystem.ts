/**
 * 面部动画编辑器系统
 * 用于管理和更新面部动画编辑器组件
 */
import * as THREE from 'three';
import { Entity } from '../core/Entity';
import { System } from '../core/System';
import { World } from '../core/World';
import { EventEmitter } from '../utils/EventEmitter';
import { FacialAnimationEditorComponent, FacialAnimationEditorConfig, FacialAnimationClip, FacialAnimationKeyframe } from './FacialAnimationEditor';
import { FacialExpressionType, VisemeType } from './FacialAnimation';

/**
 * 面部动画编辑器系统
 */
export class FacialAnimationEditorSystem extends System {
  /** 系统类型 */
  static readonly type = 'FacialAnimationEditor';

  /** 面部动画编辑器组件 */
  private components: Map<Entity, FacialAnimationEditorComponent> = new Map();
  
  /** 配置 */
  private config: FacialAnimationEditorConfig;
  
  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: FacialAnimationEditorConfig = {
    debug: false,
    defaultFrameRate: 30,
    defaultDuration: 5.0,
    defaultBlendTime: 0.3
  };

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /**
   * 构造函数
   * @param world 世界
   * @param config 配置
   */
  constructor(world: World, config?: Partial<FacialAnimationEditorConfig>) {
    super(world);
    this.config = { ...FacialAnimationEditorSystem.DEFAULT_CONFIG, ...config };
  }

  /**
   * 创建面部动画编辑器组件
   * @param entity 实体
   * @returns 面部动画编辑器组件
   */
  public createEditor(entity: Entity): FacialAnimationEditorComponent {
    // 检查是否已存在组件
    if (this.components.has(entity)) {
      return this.components.get(entity)!;
    }

    // 创建新组件
    const component = new FacialAnimationEditorComponent(entity);
    this.components.set(entity, component);

    // 设置默认帧率
    component.setFrameRate(this.config.defaultFrameRate!);

    if (this.config.debug) {
      console.log(`创建面部动画编辑器组件: ${entity.id}`);
    }

    return component;
  }

  /**
   * 移除面部动画编辑器组件
   * @param entity 实体
   */
  public removeEditor(entity: Entity): void {
    if (this.components.has(entity)) {
      this.components.delete(entity);

      if (this.config.debug) {
        console.log(`移除面部动画编辑器组件: ${entity.id}`);
      }
    }
  }

  /**
   * 获取面部动画编辑器组件
   * @param entity 实体
   * @returns 面部动画编辑器组件，如果不存在则返回null
   */
  public getEditor(entity: Entity): FacialAnimationEditorComponent | null {
    return this.components.get(entity) || null;
  }

  /**
   * 创建默认表情动画
   * @param entity 实体
   * @param name 动画名称
   * @returns 动画片段
   */
  public createDefaultExpressionAnimation(entity: Entity, name: string): FacialAnimationClip | null {
    const editor = this.getEditor(entity);
    if (!editor) return null;

    // 创建动画片段
    const clip = editor.createClip(name, 2.0, true);

    // 添加关键帧
    editor.addKeyframe(0.0, {
      expression: FacialExpressionType.NEUTRAL,
      expressionWeight: 0.0
    });

    editor.addKeyframe(1.0, {
      expression: FacialExpressionType.HAPPY,
      expressionWeight: 1.0
    });

    editor.addKeyframe(2.0, {
      expression: FacialExpressionType.NEUTRAL,
      expressionWeight: 0.0
    });

    return clip;
  }

  /**
   * 创建默认口型动画
   * @param entity 实体
   * @param name 动画名称
   * @returns 动画片段
   */
  public createDefaultVisemeAnimation(entity: Entity, name: string): FacialAnimationClip | null {
    const editor = this.getEditor(entity);
    if (!editor) return null;

    // 创建动画片段
    const clip = editor.createClip(name, 2.0, true);

    // 添加关键帧
    editor.addKeyframe(0.0, {
      viseme: VisemeType.SILENT,
      visemeWeight: 0.0
    });

    editor.addKeyframe(0.5, {
      viseme: VisemeType.AA,
      visemeWeight: 1.0
    });

    editor.addKeyframe(1.0, {
      viseme: VisemeType.EE,
      visemeWeight: 1.0
    });

    editor.addKeyframe(1.5, {
      viseme: VisemeType.OH,
      visemeWeight: 1.0
    });

    editor.addKeyframe(2.0, {
      viseme: VisemeType.SILENT,
      visemeWeight: 0.0
    });

    return clip;
  }

  /**
   * 创建组合动画
   * @param entity 实体
   * @param name 动画名称
   * @returns 动画片段
   */
  public createCombinedAnimation(entity: Entity, name: string): FacialAnimationClip | null {
    const editor = this.getEditor(entity);
    if (!editor) return null;

    // 创建动画片段
    const clip = editor.createClip(name, 4.0, true);

    // 添加关键帧 - 同时包含表情和口型
    editor.addKeyframe(0.0, {
      expression: FacialExpressionType.NEUTRAL,
      expressionWeight: 0.0,
      viseme: VisemeType.SILENT,
      visemeWeight: 0.0
    });

    editor.addKeyframe(1.0, {
      expression: FacialExpressionType.HAPPY,
      expressionWeight: 1.0,
      viseme: VisemeType.AA,
      visemeWeight: 1.0
    });

    editor.addKeyframe(2.0, {
      expression: FacialExpressionType.SURPRISED,
      expressionWeight: 1.0,
      viseme: VisemeType.OH,
      visemeWeight: 1.0
    });

    editor.addKeyframe(3.0, {
      expression: FacialExpressionType.HAPPY,
      expressionWeight: 0.5,
      viseme: VisemeType.EE,
      visemeWeight: 0.5
    });

    editor.addKeyframe(4.0, {
      expression: FacialExpressionType.NEUTRAL,
      expressionWeight: 0.0,
      viseme: VisemeType.SILENT,
      visemeWeight: 0.0
    });

    return clip;
  }

  /**
   * 导出动画片段为JSON
   * @param entity 实体
   * @param clipName 动画片段名称
   * @returns JSON字符串
   */
  public exportClipToJSON(entity: Entity, clipName: string): string | null {
    const editor = this.getEditor(entity);
    if (!editor) return null;

    const clip = editor.getClip(clipName);
    if (!clip) return null;

    return JSON.stringify(clip, null, 2);
  }

  /**
   * 从JSON导入动画片段
   * @param entity 实体
   * @param json JSON字符串
   * @returns 导入的动画片段
   */
  public importClipFromJSON(entity: Entity, json: string): FacialAnimationClip | null {
    const editor = this.getEditor(entity);
    if (!editor) return null;

    try {
      const clipData = JSON.parse(json) as FacialAnimationClip;
      
      // 创建新片段
      const clip = editor.createClip(clipData.name, clipData.duration, clipData.loop);
      
      // 添加关键帧
      for (const keyframe of clipData.keyframes) {
        editor.addKeyframe(keyframe.time, keyframe);
      }
      
      return clip;
    } catch (error) {
      console.error('导入动画片段失败:', error);
      return null;
    }
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    // 更新所有面部动画编辑器组件
    for (const component of this.components.values()) {
      component.update(deltaTime);
    }
  }
}
