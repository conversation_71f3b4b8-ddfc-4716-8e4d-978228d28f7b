/**
 * 抓取系统
 * 用于处理实体之间的抓取交互
 */
import { System } from '../../core/System';
import { Entity } from '../../core/Entity';
import { Vector3, Quaternion, Matrix4 } from 'three';
import { BodyType } from '../../physics/types/BodyType';
import { TransformComponent } from '../../core/TransformComponent';
import { Hand } from '../components/PhysicsGrabComponent';

// 临时定义缺失的类型
export enum GrabType {
  DIRECT = 'direct',
  DISTANCE = 'distance',
  SPRING = 'spring'
}

// 临时定义组件接口
interface GrabbableComponent {
  grabType: GrabType;
  grabDistance: number;
}

interface GrabberComponent {
  // 抓取者组件属性
}

interface GrabbedComponent {
  grabber: Entity;
  hand: Hand;
  offset: Vector3;
}

interface PhysicsBodyComponent {
  applyForce(x: number, y: number, z: number): void;
}

// 临时定义系统接口
interface InputSystem {
  // 输入系统属性
}

interface PhysicsSystem {
  // 物理系统属性
}

/**
 * 抓取系统配置
 */
export interface GrabSystemConfig {
  /** 是否启用调试模式 */
  debug?: boolean;
  /** 是否启用物理抓取 */
  enablePhysicsGrab?: boolean;
  /** 是否启用网络同步 */
  enableNetworkSync?: boolean;
  /** 是否启用手势抓取 */
  enableGestureGrab?: boolean;
  /** 默认抓取类型 */
  defaultGrabType?: GrabType;
}

/**
 * 抓取系统
 */
export class GrabSystem extends System {
  /** 系统名称 */
  public static readonly NAME: string = 'GrabSystem';

  /** 世界引用 */
  private readonly world: World;

  /** 输入系统引用 */
  private inputSystem?: InputSystem;

  /** 物理系统引用 */
  private physicsSystem?: PhysicsSystem;

  /** 可抓取组件列表 */
  private grabbableComponents: Map<Entity, GrabbableComponent> = new Map();

  /** 抓取者组件列表 */
  private grabberComponents: Map<Entity, GrabberComponent> = new Map();

  /** 被抓取组件列表 */
  private grabbedComponents: Map<Entity, GrabbedComponent> = new Map();

  /** 配置 */
  private config: GrabSystemConfig;

  /** 临时向量 - 用于计算 */
  private tempVector: Vector3 = new Vector3();

  /** 临时四元数 - 用于计算 */
  private tempQuaternion: Quaternion = new Quaternion();

  /** 临时矩阵 - 用于计算 */
  private tempMatrix: Matrix4 = new Matrix4();

  /** 原始物理体类型映射 - 用于恢复物理体类型 */
  private originalBodyTypes: Map<Entity, BodyType> = new Map();

  /**
   * 构造函数
   * @param config 系统配置
   */
  constructor(config: GrabSystemConfig = {}) {
    super(100); // 设置优先级
    this.config = {
      debug: config.debug !== undefined ? config.debug : false,
      enablePhysicsGrab: config.enablePhysicsGrab !== undefined ? config.enablePhysicsGrab : true,
      enableNetworkSync: config.enableNetworkSync !== undefined ? config.enableNetworkSync : true,
      enableGestureGrab: config.enableGestureGrab !== undefined ? config.enableGestureGrab : true,
      defaultGrabType: config.defaultGrabType || GrabType.DIRECT
    };

    // 获取输入系统引用
    this.inputSystem = world.getSystem(InputSystem.NAME) as InputSystem;
    if (!this.inputSystem && this.config.debug) {
      Debug.warn('GrabSystem', 'InputSystem not found, some features may not work properly');
    }

    // 获取物理系统引用
    this.physicsSystem = world.getSystem(PhysicsSystem.NAME) as PhysicsSystem;
    if (!this.physicsSystem && this.config.enablePhysicsGrab && this.config.debug) {
      Debug.warn('GrabSystem', 'PhysicsSystem not found, physics grab will not work');
    }
  }

  /**
   * 初始化系统
   */
  initialize(): void {
    if (this.config.debug) {
      Debug.log('GrabSystem', 'Initializing GrabSystem');
    }

    // 监听实体添加和移除事件
    this.world.on('entityAdded', this.onEntityAdded.bind(this));
    this.world.on('entityRemoved', this.onEntityRemoved.bind(this));

    // 初始化现有实体
    const entities = this.world.getEntities();
    for (const entity of entities) {
      this.setupEntityComponents(entity);
    }
  }

  /**
   * 处理实体添加事件
   * @param entity 添加的实体
   */
  private onEntityAdded(entity: Entity): void {
    this.setupEntityComponents(entity);
  }

  /**
   * 处理实体移除事件
   * @param entity 移除的实体
   */
  private onEntityRemoved(entity: Entity): void {
    // 移除组件引用
    this.grabbableComponents.delete(entity);
    this.grabberComponents.delete(entity);
    this.grabbedComponents.delete(entity);
    this.originalBodyTypes.delete(entity);
  }

  /**
   * 设置实体组件
   * @param entity 实体
   */
  private setupEntityComponents(entity: Entity): void {
    // 检查并注册可抓取组件
    const grabbableComponent = entity.getComponent<GrabbableComponent>(GrabbableComponent.TYPE);
    if (grabbableComponent) {
      this.registerGrabbableComponent(entity, grabbableComponent);
    }

    // 检查并注册抓取者组件
    const grabberComponent = entity.getComponent<GrabberComponent>(GrabberComponent.TYPE);
    if (grabberComponent) {
      this.registerGrabberComponent(entity, grabberComponent);
    }

    // 检查并注册被抓取组件
    const grabbedComponent = entity.getComponent<GrabbedComponent>(GrabbedComponent.TYPE);
    if (grabbedComponent) {
      this.registerGrabbedComponent(entity, grabbedComponent);
    }
  }

  /**
   * 注册可抓取组件
   * @param entity 实体
   * @param component 可抓取组件
   */
  registerGrabbableComponent(entity: Entity, component: GrabbableComponent): void {
    this.grabbableComponents.set(entity, component);

    if (this.config.debug) {
      Debug.log('GrabSystem', `Registered grabbable component for entity ${entity.id}`);
    }
  }

  /**
   * 注册抓取者组件
   * @param entity 实体
   * @param component 抓取者组件
   */
  registerGrabberComponent(entity: Entity, component: GrabberComponent): void {
    this.grabberComponents.set(entity, component);

    if (this.config.debug) {
      Debug.log('GrabSystem', `Registered grabber component for entity ${entity.id}`);
    }
  }

  /**
   * 注册被抓取组件
   * @param entity 实体
   * @param component 被抓取组件
   */
  registerGrabbedComponent(entity: Entity, component: GrabbedComponent): void {
    this.grabbedComponents.set(entity, component);

    if (this.config.debug) {
      Debug.log('GrabSystem', `Registered grabbed component for entity ${entity.id}`);
    }
  }

  /**
   * 更新系统
   * @param deltaTime 时间增量（秒）
   */
  update(deltaTime: number): void {
    // 处理输入
    this.handleInput();

    // 更新被抓取对象的位置和旋转
    this.updateGrabbedEntities();
  }

  /**
   * 处理输入
   */
  private handleInput(): void {
    // 如果没有输入系统，则返回
    if (!this.inputSystem) return;

    // 这里可以添加输入处理逻辑
    // 例如，检测按键或手势来触发抓取和释放
  }

  /**
   * 更新被抓取实体
   */
  private updateGrabbedEntities(): void {
    for (const [entity, grabbedComponent] of this.grabbedComponents) {
      const grabber = grabbedComponent.grabber;
      const hand = grabbedComponent.hand;

      // 获取抓取者的变换组件
      const grabberTransform = grabber.getComponent<Transform>('Transform');
      if (!grabberTransform) continue;

      // 获取被抓取实体的变换组件
      const entityTransform = entity.getComponent<Transform>('Transform');
      if (!entityTransform) continue;

      // 获取可抓取组件
      const grabbableComponent = this.grabbableComponents.get(entity);
      if (!grabbableComponent) continue;

      // 根据抓取类型更新位置和旋转
      switch (grabbableComponent.grabType) {
        case GrabType.DIRECT:
          this.updateDirectGrab(entity, grabber, hand);
          break;
        case GrabType.DISTANCE:
          this.updateDistanceGrab(entity, grabber, hand);
          break;
        case GrabType.SPRING:
          this.updateSpringGrab(entity, grabber, hand);
          break;
      }
    }
  }

  /**
   * 更新直接抓取
   * @param entity 被抓取实体
   * @param grabber 抓取者
   * @param hand 抓取手
   */
  private updateDirectGrab(entity: Entity, grabber: Entity, hand: Hand): void {
    // 获取抓取者的变换组件
    const grabberTransform = grabber.getComponent<Transform>('Transform');
    if (!grabberTransform) return;

    // 获取被抓取实体的变换组件
    const entityTransform = entity.getComponent<Transform>('Transform');
    if (!entityTransform) return;

    // 获取被抓取组件
    const grabbedComponent = this.grabbedComponents.get(entity);
    if (!grabbedComponent) return;

    // 计算手的位置和旋转
    const handPosition = this.getHandPosition(grabber, hand);
    const handRotation = this.getHandRotation(grabber, hand);

    // 应用偏移
    const offset = grabbedComponent.offset;
    this.tempVector.set(offset.x, offset.y, offset.z);
    this.tempVector.applyQuaternion(handRotation);

    // 更新位置和旋转
    entityTransform.setPosition(
      handPosition.x + this.tempVector.x,
      handPosition.y + this.tempVector.y,
      handPosition.z + this.tempVector.z
    );
    entityTransform.setRotation(handRotation.x, handRotation.y, handRotation.z, handRotation.w);
  }

  /**
   * 更新距离抓取
   * @param entity 被抓取实体
   * @param grabber 抓取者
   * @param hand 抓取手
   */
  private updateDistanceGrab(entity: Entity, grabber: Entity, hand: Hand): void {
    // 获取可抓取组件
    const grabbableComponent = this.grabbableComponents.get(entity);
    if (!grabbableComponent) return;

    // 获取抓取者的变换组件
    const grabberTransform = grabber.getComponent<Transform>('Transform');
    if (!grabberTransform) return;

    // 获取被抓取实体的变换组件
    const entityTransform = entity.getComponent<Transform>('Transform');
    if (!entityTransform) return;

    // 计算手的位置和旋转
    const handPosition = this.getHandPosition(grabber, hand);
    const handRotation = this.getHandRotation(grabber, hand);

    // 计算方向向量
    this.tempVector.set(0, 0, -1);
    this.tempVector.applyQuaternion(handRotation);

    // 计算目标位置
    const distance = grabbableComponent.grabDistance;
    const targetPosition = new Vector3(
      handPosition.x + this.tempVector.x * distance,
      handPosition.y + this.tempVector.y * distance,
      handPosition.z + this.tempVector.z * distance
    );

    // 更新位置和旋转
    entityTransform.setPosition(targetPosition.x, targetPosition.y, targetPosition.z);
    entityTransform.setRotation(handRotation.x, handRotation.y, handRotation.z, handRotation.w);
  }

  /**
   * 更新弹簧抓取
   * @param entity 被抓取实体
   * @param grabber 抓取者
   * @param hand 抓取手
   */
  private updateSpringGrab(entity: Entity, grabber: Entity, hand: Hand): void {
    // 如果没有物理系统，则使用直接抓取
    if (!this.physicsSystem) {
      this.updateDirectGrab(entity, grabber, hand);
      return;
    }

    // 获取物理体组件
    const physicsBody = entity.getComponent<PhysicsBodyComponent>(PhysicsBodyComponent.TYPE);
    if (!physicsBody) {
      this.updateDirectGrab(entity, grabber, hand);
      return;
    }

    // 获取抓取者的变换组件
    const grabberTransform = grabber.getComponent<Transform>('Transform');
    if (!grabberTransform) return;

    // 计算手的位置
    const handPosition = this.getHandPosition(grabber, hand);

    // 获取当前位置
    const entityTransform = entity.getComponent<Transform>('Transform');
    if (!entityTransform) return;
    const currentPosition = new Vector3(
      entityTransform.position.x,
      entityTransform.position.y,
      entityTransform.position.z
    );

    // 计算方向和距离
    const direction = new Vector3().subVectors(handPosition, currentPosition);
    const distance = direction.length();
    direction.normalize();

    // 应用弹簧力
    const springStrength = 10.0; // 弹簧强度
    const force = direction.multiplyScalar(distance * springStrength);
    physicsBody.applyForce(force.x, force.y, force.z);
  }

  /**
   * 获取手的位置
   * @param grabber 抓取者
   * @param hand 手
   * @returns 手的位置
   */
  private getHandPosition(grabber: Entity, hand: Hand): Vector3 {
    // 获取抓取者的变换组件
    const grabberTransform = grabber.getComponent<Transform>('Transform');
    if (!grabberTransform) {
      return new Vector3();
    }

    // 获取抓取者的位置
    const position = new Vector3(
      grabberTransform.position.x,
      grabberTransform.position.y,
      grabberTransform.position.z
    );

    // 根据手的类型计算偏移
    const handOffset = new Vector3();
    if (hand === Hand.LEFT) {
      handOffset.set(-0.2, -0.1, 0.1);
    } else if (hand === Hand.RIGHT) {
      handOffset.set(0.2, -0.1, 0.1);
    }

    // 应用旋转
    const rotation = new Quaternion(
      grabberTransform.rotation.x,
      grabberTransform.rotation.y,
      grabberTransform.rotation.z,
      grabberTransform.rotation.w
    );
    handOffset.applyQuaternion(rotation);

    // 返回最终位置
    return position.add(handOffset);
  }

  /**
   * 获取手的旋转
   * @param grabber 抓取者
   * @param hand 手
   * @returns 手的旋转
   */
  private getHandRotation(grabber: Entity, hand: Hand): Quaternion {
    // 获取抓取者的变换组件
    const grabberTransform = grabber.getComponent<Transform>('Transform');
    if (!grabberTransform) {
      return new Quaternion();
    }

    // 获取抓取者的旋转
    const rotation = new Quaternion(
      grabberTransform.rotation.x,
      grabberTransform.rotation.y,
      grabberTransform.rotation.z,
      grabberTransform.rotation.w
    );

    // 根据手的类型计算额外旋转
    const handRotation = new Quaternion();
    if (hand === Hand.LEFT) {
      handRotation.setFromEuler({ x: 0, y: 0, z: -Math.PI / 8 } as any);
    } else if (hand === Hand.RIGHT) {
      handRotation.setFromEuler({ x: 0, y: 0, z: Math.PI / 8 } as any);
    }

    // 应用额外旋转
    rotation.multiply(handRotation);

    return rotation;
  }
}
