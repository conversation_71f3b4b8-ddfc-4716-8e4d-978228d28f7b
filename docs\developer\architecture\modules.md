# IR引擎编辑器模块架构

## 概述

IR引擎编辑器采用模块化设计，将功能划分为多个模块，每个模块负责特定的功能。这种设计使得编辑器具有高度的可扩展性和可维护性。

## 目录结构

```
editor/
├── public/                 # 静态资源
├── src/                    # 源代码
│   ├── assets/             # 资源文件
│   ├── components/         # 组件
│   │   ├── common/         # 通用组件
│   │   ├── layout/         # 布局组件
│   │   ├── panels/         # 面板组件
│   │   ├── tools/          # 工具组件
│   │   └── ...             # 其他组件
│   ├── hooks/              # 自定义钩子
│   ├── pages/              # 页面组件
│   ├── services/           # 服务
│   ├── store/              # Redux状态管理
│   │   ├── auth/           # 认证状态
│   │   ├── project/        # 项目状态
│   │   ├── scene/          # 场景状态
│   │   ├── entity/         # 实体状态
│   │   ├── asset/          # 资产状态
│   │   ├── ui/             # UI状态
│   │   ├── tool/           # 工具状态
│   │   ├── history/        # 历史记录状态
│   │   ├── collaboration/  # 协作状态
│   │   └── index.ts        # 状态根文件
│   ├── styles/             # 样式文件
│   ├── types/              # 类型定义
│   ├── utils/              # 工具函数
│   ├── App.tsx             # 应用根组件
│   ├── i18n.ts             # 国际化配置
│   └── main.tsx            # 入口文件
├── tests/                  # 测试文件
├── .env                    # 环境变量
├── package.json            # 项目配置
├── tsconfig.json           # TypeScript配置
└── vite.config.ts          # Vite配置
```

## 核心模块

### 1. 组件模块

组件模块包含编辑器的所有UI组件，按功能和用途分为多个子模块。

#### 1.1 通用组件

通用组件是基础UI组件，如按钮、输入框、选择框等。这些组件大多是对Ant Design组件的封装和扩展，添加了编辑器特定的功能和样式。

```typescript
// 按钮组件示例
import React from 'react';
import { Button as AntButton } from 'antd';
import { ButtonProps as AntButtonProps } from 'antd/lib/button';

export interface ButtonProps extends AntButtonProps {
  tooltip?: string;
}

export const Button: React.FC<ButtonProps> = ({ tooltip, ...props }) => {
  if (tooltip) {
    return (
      <Tooltip title={tooltip}>
        <AntButton {...props} />
      </Tooltip>
    );
  }
  return <AntButton {...props} />;
};
```

#### 1.2 布局组件

布局组件负责编辑器的整体布局，包括顶部菜单栏、侧边栏、内容区域等。

```typescript
// 应用布局组件示例
import React from 'react';
import { Layout } from 'antd';
import { Outlet } from 'react-router-dom';
import { Header } from './Header';
import { Sidebar } from './Sidebar';
import { Footer } from './Footer';

const { Content } = Layout;

export const AppLayout: React.FC = () => {
  return (
    <Layout className="app-layout">
      <Header />
      <Layout>
        <Sidebar />
        <Content className="app-content">
          <Outlet />
        </Content>
      </Layout>
      <Footer />
    </Layout>
  );
};
```

#### 1.3 面板组件

面板组件是编辑器的功能面板，如场景面板、层级面板、检查器面板、资产面板等。

```typescript
// 面板注册器示例
import React from 'react';
import { TabData } from 'rc-dock';
import { useTranslation } from 'react-i18next';
import { PanelType } from '../../store/ui/uiSlice';
import HierarchyPanel from './HierarchyPanel';
import InspectorPanel from './InspectorPanel';
import ScenePanel from './ScenePanel';
import AssetsPanel from './AssetsPanel';
import ConsolePanel from './ConsolePanel';

// 面板组件映射
const panelComponents = {
  [PanelType.HIERARCHY]: HierarchyPanel,
  [PanelType.INSPECTOR]: InspectorPanel,
  [PanelType.SCENE]: ScenePanel,
  [PanelType.ASSETS]: AssetsPanel,
  [PanelType.CONSOLE]: ConsolePanel,
};

// 获取面板组件
export const getPanelComponent = (type: PanelType): React.ComponentType => {
  return panelComponents[type] || (() => <div>未知面板类型</div>);
};

// 注册面板组件
export const registerPanelComponent = (type: PanelType, component: React.ComponentType): void => {
  panelComponents[type] = component;
};

// 层级面板标签
export const HierarchyPanelTab: TabData = {
  id: PanelType.HIERARCHY,
  title: '层级',
  content: <HierarchyPanel />,
};

// 检查器面板标签
export const InspectorPanelTab: TabData = {
  id: PanelType.INSPECTOR,
  title: '检查器',
  content: <InspectorPanel />,
};

// 场景面板标签
export const ScenePanelTab: TabData = {
  id: PanelType.SCENE,
  title: '场景',
  content: <ScenePanel />,
};

// 资产面板标签
export const AssetsPanelTab: TabData = {
  id: PanelType.ASSETS,
  title: '资产',
  content: <AssetsPanel />,
};

// 控制台面板标签
export const ConsolePanelTab: TabData = {
  id: PanelType.CONSOLE,
  title: '控制台',
  content: <ConsolePanel />,
};
```

#### 1.4 工具组件

工具组件是编辑器的功能工具，如变换工具、选择工具等。

```typescript
// 工具注册器示例
import React from 'react';
import { ToolType } from '../../store/tool/toolSlice';
import TransformTool from './TransformTool';
import SelectTool from './SelectTool';
import PaintTool from './PaintTool';
import EraseTool from './EraseTool';
import CloneTool from './CloneTool';

// 工具组件映射
const toolComponents = {
  [ToolType.TRANSFORM]: TransformTool,
  [ToolType.SELECT]: SelectTool,
  [ToolType.PAINT]: PaintTool,
  [ToolType.ERASE]: EraseTool,
  [ToolType.CLONE]: CloneTool,
};

// 获取工具组件
export const getToolComponent = (type: ToolType): React.ComponentType => {
  return toolComponents[type] || (() => <div>未知工具类型</div>);
};

// 注册工具组件
export const registerToolComponent = (type: ToolType, component: React.ComponentType): void => {
  toolComponents[type] = component;
};
```

### 2. 服务模块

服务模块提供各种服务，负责与后端API交互、管理资源等。

#### 2.1 引擎服务

引擎服务负责与引擎核心交互，提供了一组API来操作引擎。

```typescript
// 引擎服务示例
import { EventEmitter } from 'events';
import { Engine, EngineOptions, Scene, Entity, Component } from 'ir-engine-core';

export enum EngineEventType {
  INITIALIZED = 'engine:initialized',
  STARTED = 'engine:started',
  STOPPED = 'engine:stopped',
  SCENE_LOADED = 'engine:scene:loaded',
  SCENE_UNLOADED = 'engine:scene:unloaded',
  ENTITY_CREATED = 'engine:entity:created',
  ENTITY_DELETED = 'engine:entity:deleted',
  ENTITY_SELECTED = 'engine:entity:selected',
  ENTITY_DESELECTED = 'engine:entity:deselected',
  COMPONENT_ADDED = 'engine:component:added',
  COMPONENT_REMOVED = 'engine:component:removed',
  COMPONENT_CHANGED = 'engine:component:changed',
}

export class EngineService extends EventEmitter {
  private static instance: EngineService;
  private engine: Engine | null = null;
  private activeScene: Scene | null = null;
  private activeCamera: Camera | null = null;
  private selectedEntities: Entity[] = [];

  private constructor() {
    super();
  }

  public static getInstance(): EngineService {
    if (!EngineService.instance) {
      EngineService.instance = new EngineService();
    }
    return EngineService.instance;
  }

  public async initialize(canvas: HTMLCanvasElement, options: Partial<EngineOptions> = {}): Promise<void> {
    if (this.engine) {
      return;
    }

    try {
      // 创建引擎实例
      this.engine = new Engine({
        canvas,
        autoStart: false,
        ...options,
      });

      // 初始化引擎
      this.engine.initialize();

      // 发出初始化事件
      this.emit(EngineEventType.INITIALIZED, this.engine);

      console.log('引擎初始化成功');
    } catch (error) {
      console.error('引擎初始化失败:', error);
      throw error;
    }
  }

  // 其他方法...
}
```

#### 2.2 项目服务

项目服务负责项目的创建、打开、保存等。

```typescript
// 项目服务示例
import axios from 'axios';
import { Project, ProjectOptions, ExportOptions } from '../types/project';

export class ProjectService {
  private static instance: ProjectService;
  private apiUrl: string;

  private constructor() {
    this.apiUrl = import.meta.env.VITE_API_URL || '';
  }

  public static getInstance(): ProjectService {
    if (!ProjectService.instance) {
      ProjectService.instance = new ProjectService();
    }
    return ProjectService.instance;
  }

  public async createProject(options: ProjectOptions): Promise<Project> {
    try {
      const response = await axios.post(`${this.apiUrl}/projects`, options);
      return response.data;
    } catch (error) {
      console.error('创建项目失败:', error);
      throw error;
    }
  }

  // 其他方法...
}
```

### 3. 状态管理模块

状态管理模块使用Redux管理编辑器的状态，包括各种状态切片。

#### 3.1 状态根文件

```typescript
// 状态根文件示例
import { configureStore } from '@reduxjs/toolkit';
import authReducer from './auth/authSlice';
import projectReducer from './project/projectSlice';
import sceneReducer from './scene/sceneSlice';
import entityReducer from './entity/entitySlice';
import assetReducer from './asset/assetSlice';
import uiReducer from './ui/uiSlice';
import toolReducer from './tool/toolSlice';
import historyReducer from './history/historySlice';
import collaborationReducer from './collaboration/collaborationSlice';

export const store = configureStore({
  reducer: {
    auth: authReducer,
    project: projectReducer,
    scene: sceneReducer,
    entity: entityReducer,
    asset: assetReducer,
    ui: uiReducer,
    tool: toolReducer,
    history: historyReducer,
    collaboration: collaborationReducer,
  },
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
```

#### 3.2 状态切片

```typescript
// UI状态切片示例
import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export enum PanelType {
  HIERARCHY = 'hierarchy',
  INSPECTOR = 'inspector',
  SCENE = 'scene',
  ASSETS = 'assets',
  CONSOLE = 'console',
  LAYERS = 'layers',
  INSTANCES = 'instances',
  COLLABORATION = 'collaboration',
  USER_TESTING = 'user-testing',
  DEBUG = 'debug',
}

export enum ThemeType {
  LIGHT = 'light',
  DARK = 'dark',
}

interface UiState {
  sidebarCollapsed: boolean;
  theme: ThemeType;
  panelLayout: Record<string, any>;
  activePanels: PanelType[];
}

const initialState: UiState = {
  sidebarCollapsed: false,
  theme: ThemeType.DARK,
  panelLayout: {},
  activePanels: [PanelType.HIERARCHY, PanelType.INSPECTOR, PanelType.SCENE, PanelType.ASSETS],
};

export const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    toggleSidebar: (state) => {
      state.sidebarCollapsed = !state.sidebarCollapsed;
    },
    setTheme: (state, action: PayloadAction<ThemeType>) => {
      state.theme = action.payload;
    },
    setPanelLayout: (state, action: PayloadAction<Record<string, any>>) => {
      state.panelLayout = action.payload;
    },
    addPanel: (state, action: PayloadAction<PanelType>) => {
      if (!state.activePanels.includes(action.payload)) {
        state.activePanels.push(action.payload);
      }
    },
    removePanel: (state, action: PayloadAction<PanelType>) => {
      state.activePanels = state.activePanels.filter((panel) => panel !== action.payload);
    },
  },
});

export const { toggleSidebar, setTheme, setPanelLayout, addPanel, removePanel } = uiSlice.actions;

export default uiSlice.reducer;
```

### 4. 工具函数模块

工具函数模块提供各种工具函数，辅助编辑器的功能实现。

```typescript
// 数学工具示例
import * as THREE from 'three';

export const Vector3 = {
  // 创建向量
  create: (x = 0, y = 0, z = 0): THREE.Vector3 => {
    return new THREE.Vector3(x, y, z);
  },

  // 复制向量
  clone: (v: THREE.Vector3): THREE.Vector3 => {
    return v.clone();
  },

  // 向量加法
  add: (a: THREE.Vector3, b: THREE.Vector3): THREE.Vector3 => {
    return a.clone().add(b);
  },

  // 向量减法
  subtract: (a: THREE.Vector3, b: THREE.Vector3): THREE.Vector3 => {
    return a.clone().sub(b);
  },

  // 向量点积
  dot: (a: THREE.Vector3, b: THREE.Vector3): number => {
    return a.dot(b);
  },

  // 向量叉积
  cross: (a: THREE.Vector3, b: THREE.Vector3): THREE.Vector3 => {
    return a.clone().cross(b);
  },

  // 向量长度
  length: (v: THREE.Vector3): number => {
    return v.length();
  },

  // 向量归一化
  normalize: (v: THREE.Vector3): THREE.Vector3 => {
    return v.clone().normalize();
  },
};
```

## 模块间通信

编辑器模块间通信主要通过以下方式：

1. **Redux状态**：组件通过Redux状态共享数据，通过dispatch action触发状态变化。
2. **服务调用**：组件通过调用服务方法与其他模块交互。
3. **事件系统**：服务通过事件系统通知状态变化。
4. **自定义钩子**：组件通过自定义钩子访问服务和状态。

## 扩展点

编辑器提供了多个扩展点，允许开发者扩展编辑器的功能：

1. **面板注册**：通过`PanelRegistry`注册自定义面板。
2. **工具注册**：通过`ToolRegistry`注册自定义工具。
3. **组件编辑器注册**：通过`ComponentEditorRegistry`注册自定义组件编辑器。
4. **资产类型注册**：通过`AssetTypeRegistry`注册自定义资产类型。
5. **命令注册**：通过`CommandRegistry`注册自定义命令。

## 模块依赖关系

模块间的依赖关系如下：

1. **UI组件** 依赖于 **状态管理** 和 **服务**。
2. **服务** 依赖于 **工具函数** 和 **引擎接口**。
3. **状态管理** 依赖于 **服务**。
4. **工具函数** 不依赖其他模块。
5. **引擎接口** 依赖于 **引擎核心**。

这种依赖关系确保了模块间的松耦合，提高了代码的可维护性和可扩展性。
