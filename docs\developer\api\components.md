# 编辑器组件 API文档

## 概述

IR引擎编辑器组件是编辑器界面的构建块，包括各种UI组件和功能组件。这些组件基于React和Ant Design开发，提供了丰富的功能和良好的用户体验。

## 目录

- [通用组件](#通用组件)
- [编辑器特有组件](#编辑器特有组件)
- [面板组件](#面板组件)
- [工具组件](#工具组件)

## 通用组件

通用组件是编辑器中常用的基础UI组件，大部分基于Ant Design组件库进行封装和扩展。

### Button

按钮组件，用于触发操作。

```typescript
import { Button } from '../components/common/Button';

// 使用示例
<Button
  type="primary"
  icon={<SaveOutlined />}
  onClick={handleSave}
>
  保存
</Button>
```

**属性：**

| 属性名 | 类型 | 默认值 | 描述 |
|-------|------|-------|------|
| type | 'primary' \| 'default' \| 'dashed' \| 'link' \| 'text' | 'default' | 按钮类型 |
| size | 'large' \| 'middle' \| 'small' | 'middle' | 按钮大小 |
| icon | ReactNode | - | 按钮图标 |
| loading | boolean | false | 是否显示加载状态 |
| disabled | boolean | false | 是否禁用 |
| onClick | (event: MouseEvent) => void | - | 点击事件回调 |

### Input

输入框组件，用于文本输入。

```typescript
import { Input } from '../components/common/Input';

// 使用示例
<Input
  placeholder="请输入名称"
  value={name}
  onChange={handleNameChange}
/>
```

**属性：**

| 属性名 | 类型 | 默认值 | 描述 |
|-------|------|-------|------|
| value | string | - | 输入框值 |
| placeholder | string | - | 占位文本 |
| disabled | boolean | false | 是否禁用 |
| onChange | (value: string) => void | - | 值变化回调 |
| onBlur | () => void | - | 失去焦点回调 |
| onFocus | () => void | - | 获得焦点回调 |

### Select

选择框组件，用于从多个选项中选择。

```typescript
import { Select } from '../components/common/Select';

// 使用示例
<Select
  options={[
    { value: 'option1', label: '选项1' },
    { value: 'option2', label: '选项2' },
  ]}
  value={selectedOption}
  onChange={handleOptionChange}
/>
```

**属性：**

| 属性名 | 类型 | 默认值 | 描述 |
|-------|------|-------|------|
| options | { value: string, label: string }[] | [] | 选项列表 |
| value | string | - | 当前选中值 |
| placeholder | string | - | 占位文本 |
| disabled | boolean | false | 是否禁用 |
| onChange | (value: string) => void | - | 值变化回调 |

### ColorPicker

颜色选择器组件，用于选择颜色。

```typescript
import { ColorPicker } from '../components/common/ColorPicker';

// 使用示例
<ColorPicker
  color={color}
  onChange={handleColorChange}
/>
```

**属性：**

| 属性名 | 类型 | 默认值 | 描述 |
|-------|------|-------|------|
| color | string | '#ffffff' | 当前颜色值（十六进制） |
| onChange | (color: string) => void | - | 颜色变化回调 |
| disabled | boolean | false | 是否禁用 |

## 编辑器特有组件

编辑器特有组件是为IR引擎编辑器专门设计的组件，提供了编辑器特定的功能。

### Viewport

3D视口组件，用于显示和编辑3D场景。

```typescript
import { Viewport } from '../components/Viewport';

// 使用示例
<Viewport
  onReady={handleViewportReady}
  showGrid={true}
  showAxes={true}
/>
```

**属性：**

| 属性名 | 类型 | 默认值 | 描述 |
|-------|------|-------|------|
| onReady | (canvas: HTMLCanvasElement) => void | - | 视口准备就绪回调 |
| showGrid | boolean | true | 是否显示网格 |
| showAxes | boolean | true | 是否显示坐标轴 |
| showStats | boolean | false | 是否显示性能统计 |
| backgroundColor | string | '#1e1e1e' | 背景颜色 |

**方法：**

| 方法名 | 参数 | 返回值 | 描述 |
|-------|------|-------|------|
| getCanvas | - | HTMLCanvasElement | 获取画布元素 |
| resize | - | void | 调整视口大小 |
| takeScreenshot | - | string | 获取视口截图（base64） |

### SceneHierarchy

场景层级组件，用于显示和管理场景中的实体层级结构。

```typescript
import { SceneHierarchy } from '../components/SceneHierarchy';

// 使用示例
<SceneHierarchy
  onSelect={handleEntitySelect}
  onDrop={handleEntityDrop}
/>
```

**属性：**

| 属性名 | 类型 | 默认值 | 描述 |
|-------|------|-------|------|
| onSelect | (entityId: string) => void | - | 实体选择回调 |
| onDrop | (sourceId: string, targetId: string) => void | - | 实体拖放回调 |
| filter | (entity: Entity) => boolean | - | 实体过滤函数 |

### PropertyEditor

属性编辑器组件，用于编辑实体和组件的属性。

```typescript
import { PropertyEditor } from '../components/PropertyEditor';

// 使用示例
<PropertyEditor
  entity={selectedEntity}
  onChange={handlePropertyChange}
/>
```

**属性：**

| 属性名 | 类型 | 默认值 | 描述 |
|-------|------|-------|------|
| entity | Entity | null | 当前编辑的实体 |
| onChange | (entity: Entity, property: string, value: any) => void | - | 属性变化回调 |
| readOnly | boolean | false | 是否只读 |

### AssetBrowser

资产浏览器组件，用于浏览和管理项目资产。

```typescript
import { AssetBrowser } from '../components/AssetBrowser';

// 使用示例
<AssetBrowser
  onSelect={handleAssetSelect}
  onImport={handleAssetImport}
/>
```

**属性：**

| 属性名 | 类型 | 默认值 | 描述 |
|-------|------|-------|------|
| onSelect | (assetId: string) => void | - | 资产选择回调 |
| onImport | (files: File[]) => void | - | 资产导入回调 |
| filter | (asset: Asset) => boolean | - | 资产过滤函数 |
| view | 'grid' \| 'list' | 'grid' | 视图模式 |

## 面板组件

面板组件是编辑器界面的主要组成部分，包括各种功能面板。

### ScenePanel

场景面板，用于管理场景和场景设置。

```typescript
import { ScenePanel } from '../components/panels/ScenePanel';

// 使用示例
<ScenePanel />
```

**属性：**

| 属性名 | 类型 | 默认值 | 描述 |
|-------|------|-------|------|
| onSceneChange | (sceneId: string) => void | - | 场景变化回调 |
| onSceneCreate | (name: string) => void | - | 场景创建回调 |
| onSceneDelete | (sceneId: string) => void | - | 场景删除回调 |

### HierarchyPanel

层级面板，用于显示和管理场景中的实体层级结构。

```typescript
import { HierarchyPanel } from '../components/panels/HierarchyPanel';

// 使用示例
<HierarchyPanel />
```

**属性：**

| 属性名 | 类型 | 默认值 | 描述 |
|-------|------|-------|------|
| onEntitySelect | (entityId: string) => void | - | 实体选择回调 |
| onEntityCreate | (type: string) => void | - | 实体创建回调 |
| onEntityDelete | (entityId: string) => void | - | 实体删除回调 |

### InspectorPanel

检查器面板，用于编辑选中实体的属性。

```typescript
import { InspectorPanel } from '../components/panels/InspectorPanel';

// 使用示例
<InspectorPanel />
```

**属性：**

| 属性名 | 类型 | 默认值 | 描述 |
|-------|------|-------|------|
| entity | Entity | null | 当前编辑的实体 |
| onChange | (property: string, value: any) => void | - | 属性变化回调 |
| readOnly | boolean | false | 是否只读 |

### AssetsPanel

资产面板，用于浏览和管理项目资产。

```typescript
import { AssetsPanel } from '../components/panels/AssetsPanel';

// 使用示例
<AssetsPanel />
```

**属性：**

| 属性名 | 类型 | 默认值 | 描述 |
|-------|------|-------|------|
| onAssetSelect | (assetId: string) => void | - | 资产选择回调 |
| onAssetImport | (files: File[]) => void | - | 资产导入回调 |
| onAssetDelete | (assetId: string) => void | - | 资产删除回调 |

## 工具组件

工具组件是编辑器提供的各种功能工具。

### TransformTool

变换工具，用于变换（移动、旋转、缩放）3D对象。

```typescript
import { TransformTool } from '../components/tools/TransformTool';

// 使用示例
<TransformTool
  mode="translate"
  space="world"
  onTransform={handleTransform}
/>
```

**属性：**

| 属性名 | 类型 | 默认值 | 描述 |
|-------|------|-------|------|
| mode | 'translate' \| 'rotate' \| 'scale' | 'translate' | 变换模式 |
| space | 'world' \| 'local' | 'world' | 坐标空间 |
| snap | boolean | false | 是否启用对齐 |
| snapValue | number | 1 | 对齐值 |
| onTransform | (entity: Entity, transform: Transform) => void | - | 变换回调 |

### SelectTool

选择工具，用于选择3D对象。

```typescript
import { SelectTool } from '../components/tools/SelectTool';

// 使用示例
<SelectTool
  mode="single"
  onSelect={handleSelect}
/>
```

**属性：**

| 属性名 | 类型 | 默认值 | 描述 |
|-------|------|-------|------|
| mode | 'single' \| 'multiple' | 'single' | 选择模式 |
| filter | (entity: Entity) => boolean | - | 实体过滤函数 |
| onSelect | (entities: Entity[]) => void | - | 选择回调 |
