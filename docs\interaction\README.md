# 交互系统文档

## 概述

交互系统（Interaction System）是IR引擎的核心系统之一，用于处理3D场景中的对象交互。它提供了一套完整的交互机制，包括交互检测、交互事件处理、交互提示和交互高亮等功能。

## 主要特性

- **多种交互类型**：支持点击交互、接近交互和悬停交互
- **交互检测**：基于距离、视锥体和射线投射的交互检测
- **交互事件**：完整的交互事件系统，支持事件监听和分发
- **交互提示**：可自定义的交互提示系统，支持文本和图标
- **交互高亮**：多种高亮效果，包括轮廓高亮、发光高亮和颜色高亮
- **优先级排序**：基于距离和其他因素的交互对象优先级排序

## 系统组件

交互系统由以下主要组件组成：

1. **InteractionSystem**：交互系统的核心，负责管理和协调所有交互组件
2. **InteractableComponent**：标记可交互对象的组件，定义交互行为
3. **InteractionEventComponent**：处理交互事件的组件，支持事件监听和分发
4. **InteractionPromptComponent**：显示交互提示的组件，支持文本和图标
5. **InteractionHighlightComponent**：高亮可交互对象的组件，支持多种高亮效果

## 使用方法

### 1. 初始化交互系统

首先，需要创建并初始化交互系统，并将其添加到世界中：

```typescript
// 创建交互系统
const interactionSystem = new InteractionSystem(world, {
  debug: true,                  // 是否启用调试模式
  maxInteractionDistance: 5,    // 最大交互距离
  enableFrustumCheck: true,     // 是否启用视锥体检测
  enableHighlight: true,        // 是否启用高亮效果
  enablePrompt: true            // 是否启用交互提示
});

// 添加交互系统到世界
world.addSystem(interactionSystem);
```

### 2. 创建可交互对象

然后，创建可交互对象并添加相关组件：

```typescript
// 创建实体
const entity = new Entity(world);

// 添加变换组件
entity.addComponent(new Transform({
  position: new Vector3(0, 0, 0),
  rotation: new Vector3(0, 0, 0),
  scale: new Vector3(1, 1, 1)
}));

// 添加可交互组件
const interactable = new InteractableComponent(entity, {
  interactionType: InteractionType.CLICK,  // 交互类型
  label: '可交互对象',                      // 交互标签
  prompt: '按E键交互',                      // 交互提示
  interactionDistance: 5,                  // 交互距离
  onInteract: (e) => {                     // 交互回调
    console.log('与对象交互');
    // 在这里可以添加交互逻辑
  }
});
entity.addComponent(interactable);

// 添加交互事件组件
const interactionEvent = new InteractionEventComponent(entity);
entity.addComponent(interactionEvent);

// 添加交互提示组件
const interactionPrompt = new InteractionPromptComponent(entity, {
  text: '按E键交互',                        // 提示文本
  positionType: PromptPositionType.FOLLOW, // 提示位置类型
  offset: new Vector3(0, 1.5, 0),          // 提示位置偏移
  visible: false                           // 初始是否可见
});
entity.addComponent(interactionPrompt);

// 添加交互高亮组件
const interactionHighlight = new InteractionHighlightComponent(entity, {
  highlightType: HighlightType.OUTLINE,    // 高亮类型
  highlightColor: '#ffff00',               // 高亮颜色
  pulse: true,                             // 是否脉冲效果
  highlighted: false                       // 初始是否高亮
});
entity.addComponent(interactionHighlight);

// 将实体添加到场景
scene.addEntity(entity);

// 注册组件到交互系统
interactionSystem.registerInteractableComponent(entity, interactable);
interactionSystem.registerInteractionEventComponent(entity, interactionEvent);
```

### 3. 监听交互事件

可以监听交互事件来响应用户交互：

```typescript
// 监听交互事件
interactionEvent.addEventListener(InteractionEventType.INTERACTION_START, (event) => {
  console.log('开始交互', event);
});

interactionEvent.addEventListener(InteractionEventType.INTERACTION_END, (event) => {
  console.log('结束交互', event);
});

interactionEvent.addEventListener(InteractionEventType.ENTER_RANGE, (event) => {
  console.log('进入交互范围', event);
});

interactionEvent.addEventListener(InteractionEventType.EXIT_RANGE, (event) => {
  console.log('离开交互范围', event);
});

interactionEvent.addEventListener(InteractionEventType.HIGHLIGHT_START, (event) => {
  console.log('开始高亮', event);
});

interactionEvent.addEventListener(InteractionEventType.HIGHLIGHT_END, (event) => {
  console.log('结束高亮', event);
});
```

### 4. 手动控制交互

也可以手动控制交互组件的行为：

```typescript
// 手动触发交互
interactable.interact();

// 控制高亮
interactionHighlight.highlighted = true;  // 开始高亮
interactionHighlight.highlighted = false; // 结束高亮

// 控制提示
interactionPrompt.visible = true;  // 显示提示
interactionPrompt.visible = false; // 隐藏提示

// 更新提示文本
interactionPrompt.text = '新的提示文本';
```

## 交互类型

交互系统支持以下交互类型：

- **点击交互（CLICK）**：需要用户主动点击或按键才能触发交互
- **接近交互（PROXIMITY）**：当用户接近对象时自动触发交互
- **悬停交互（HOVER）**：当用户将鼠标悬停在对象上时触发交互

## 高亮类型

交互系统支持以下高亮类型：

- **轮廓高亮（OUTLINE）**：在对象周围显示轮廓线
- **发光高亮（GLOW）**：使对象发光
- **颜色高亮（COLOR）**：改变对象的颜色
- **自定义高亮（CUSTOM）**：自定义高亮效果

## 提示位置类型

交互提示支持以下位置类型：

- **世界空间（WORLD）**：在世界空间中的固定位置显示提示
- **屏幕空间（SCREEN）**：在屏幕空间中的固定位置显示提示
- **跟随对象（FOLLOW）**：提示跟随对象移动

## 最佳实践

1. **合理设置交互距离**：根据对象的大小和重要性设置合适的交互距离
2. **提供清晰的交互提示**：使用简洁明了的文本和图标提示用户如何交互
3. **使用适当的高亮效果**：根据场景风格选择合适的高亮效果
4. **优化交互检测**：在复杂场景中，可以降低交互检测的频率以提高性能
5. **处理交互冲突**：当多个对象可交互时，确保用户能够清楚地知道当前正在与哪个对象交互

## 示例

请参考 `examples/interaction/InteractionExample.ts` 文件，了解交互系统的完整使用示例。
