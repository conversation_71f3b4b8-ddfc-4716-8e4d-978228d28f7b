# GPU加速面部动画

GPU加速面部动画是IR引擎中用于提高面部动画性能的技术。它将面部动画计算从CPU转移到GPU，显著减少CPU负担，提高渲染性能，特别是对于复杂模型和大量角色的场景。

## 主要特性

- **GPU加速计算**：使用GPU计算面部动画，减少CPU负担
- **混合形状纹理**：使用纹理存储混合形状权重，提高访问效率
- **自定义着色器**：使用自定义着色器实现高效的面部动画计算
- **计算着色器支持**：支持使用计算着色器进一步提高性能（如果可用）
- **自动降级**：在不支持GPU加速的环境中自动降级到CPU计算

## 工作原理

GPU加速面部动画的工作原理如下：

1. **混合形状映射**：创建混合形状名称到索引的映射
2. **混合形状纹理**：创建存储混合形状权重的纹理
3. **GPU材质**：创建使用自定义着色器的GPU材质
4. **权重更新**：在纹理中更新混合形状权重
5. **GPU计算**：在GPU中计算面部动画

### 混合形状纹理

混合形状纹理是一个RGBA格式的浮点纹理，用于存储混合形状权重。每个纹素的R通道存储一个混合形状的权重，可以存储大量混合形状权重。

### 自定义着色器

自定义着色器用于在GPU中计算面部动画。它从混合形状纹理中读取权重，并应用到顶点位置上。

```glsl
// 顶点着色器
uniform sampler2D blendShapeTexture;
uniform float blendShapeTextureSize;

attribute vec4 morphTarget0;
attribute vec4 morphTarget1;
// ... 更多混合形状属性

vec4 getBlendShapeWeight(int index) {
  float y = floor(float(index) / blendShapeTextureSize);
  float x = float(index) - y * blendShapeTextureSize;
  vec2 uv = vec2(x, y) / blendShapeTextureSize;
  return texture2D(blendShapeTexture, uv);
}

vec3 applyBlendShape(vec3 position) {
  vec3 result = position;
  
  // 应用混合形状
  for (int i = 0; i < 8; i++) {
    vec4 weight = getBlendShapeWeight(i);
    
    if (i == 0) result += morphTarget0.xyz * weight.x;
    else if (i == 1) result += morphTarget1.xyz * weight.x;
    // ... 更多混合形状
  }
  
  return result;
}

void main() {
  vec3 transformed = applyBlendShape(position);
  
  // 应用骨骼动画
  #include <skinning_vertex>
  
  // 投影
  gl_Position = projectionMatrix * modelViewMatrix * vec4(transformed, 1.0);
}
```

## 基本用法

### 创建GPU面部动画系统

```typescript
import { GPUFacialAnimationSystem } from '../../animation';

// 创建GPU面部动画系统
const gpuSystem = new GPUFacialAnimationSystem(world, {
  debug: true,
  useComputeShader: false,
  maxBlendShapes: 32,
  textureSize: 16
});

// 添加系统到世界
world.addSystem(gpuSystem);
```

### 创建GPU面部动画组件

```typescript
// 创建GPU面部动画组件
const gpuComponent = gpuSystem.createGPUFacialAnimation(characterEntity, skinnedMesh);
```

### 设置表情和口型

```typescript
// 设置表情
gpuComponent.setExpression(FacialExpressionType.HAPPY, 1.0);

// 设置口型
gpuComponent.setViseme(VisemeType.AA, 1.0);

// 直接设置混合形状权重
gpuComponent.setBlendShapeWeight('smile', 1.0);
```

### 恢复原始材质

```typescript
// 恢复原始材质
gpuComponent.restoreOriginalMaterial(skinnedMesh);
```

### 销毁组件

```typescript
// 销毁组件
gpuSystem.removeGPUFacialAnimation(characterEntity, skinnedMesh);
```

## 高级功能

### 使用计算着色器

如果环境支持计算着色器，可以使用计算着色器进一步提高性能。

```typescript
// 创建GPU面部动画系统
const gpuSystem = new GPUFacialAnimationSystem(world, {
  debug: true,
  useComputeShader: true  // 启用计算着色器
});
```

### 自定义混合形状映射

可以自定义混合形状名称到表情和口型的映射。

```typescript
// 设置自定义表情映射
gpuComponent.setExpressionMapping(FacialExpressionType.HAPPY, 'smile');
gpuComponent.setExpressionMapping(FacialExpressionType.SAD, 'frown');

// 设置自定义口型映射
gpuComponent.setVisemeMapping(VisemeType.AA, 'mouthOpen');
gpuComponent.setVisemeMapping(VisemeType.PP, 'mouthClose');
```

## 性能优化

### 纹理大小

纹理大小影响混合形状的最大数量。较大的纹理可以存储更多的混合形状，但会消耗更多的GPU内存。

```typescript
// 设置较大的纹理大小
const gpuSystem = new GPUFacialAnimationSystem(world, {
  textureSize: 32  // 支持最多32*32=1024个混合形状
});
```

### 混合形状数量

限制混合形状的数量可以减少GPU计算负担。

```typescript
// 限制混合形状数量
const gpuSystem = new GPUFacialAnimationSystem(world, {
  maxBlendShapes: 16  // 只使用16个混合形状
});
```

### 更新频率

减少混合形状权重的更新频率可以减少CPU到GPU的数据传输。

```typescript
// 在需要时才更新混合形状权重
if (needsUpdate) {
  gpuComponent.setExpression(expression, weight);
}
```

## 与其他系统集成

### 与面部动画系统集成

GPU面部动画系统可以与面部动画系统集成，实现高性能的面部动画。

```typescript
import { FacialAnimationSystem } from '../../animation';

// 创建面部动画系统
const facialAnimationSystem = new FacialAnimationSystem(world, {
  debug: true
});

// 创建GPU面部动画系统
const gpuSystem = new GPUFacialAnimationSystem(world, {
  debug: true
});

// 添加系统到世界
world.addSystem(facialAnimationSystem);
world.addSystem(gpuSystem);

// 创建面部动画组件
const facialAnimation = facialAnimationSystem.createFacialAnimation(characterEntity);

// 创建GPU面部动画组件
const gpuComponent = gpuSystem.createGPUFacialAnimation(characterEntity, skinnedMesh);

// 将面部动画组件与模型绑定
facialAnimationSystem.linkToModel(characterEntity, skinnedMesh);
```

### 与面部动画编辑器集成

GPU面部动画系统可以与面部动画编辑器集成，实现高性能的面部动画编辑。

```typescript
import { FacialAnimationEditorSystem } from '../../animation';

// 创建面部动画编辑器系统
const editorSystem = new FacialAnimationEditorSystem(world, {
  debug: true
});

// 创建GPU面部动画系统
const gpuSystem = new GPUFacialAnimationSystem(world, {
  debug: true
});

// 添加系统到世界
world.addSystem(editorSystem);
world.addSystem(gpuSystem);

// 创建面部动画编辑器组件
const editor = editorSystem.createEditor(characterEntity);

// 创建GPU面部动画组件
const gpuComponent = gpuSystem.createGPUFacialAnimation(characterEntity, skinnedMesh);
```

## 兼容性

GPU加速面部动画需要WebGL2支持。在不支持WebGL2的环境中，系统会自动降级到CPU计算。

### 检查GPU支持

```typescript
// 检查GPU支持
const gpuSystem = new GPUFacialAnimationSystem(world);
const supportsGPU = gpuSystem.supportsGPU();

if (supportsGPU) {
  console.log('支持GPU加速面部动画');
} else {
  console.log('不支持GPU加速面部动画，将使用CPU计算');
}
```

## 示例

完整的GPU加速面部动画示例可以在 `examples/animation/GPUFacialAnimationExample.ts` 中找到。

```typescript
// 创建示例
const example = new GPUFacialAnimationExample();

// 启动示例
example.start();
```

## 最佳实践

1. **检查GPU支持**：在使用GPU加速面部动画前，检查环境是否支持WebGL2。
2. **限制混合形状数量**：只使用必要的混合形状，减少GPU计算负担。
3. **优化更新频率**：减少混合形状权重的更新频率，减少CPU到GPU的数据传输。
4. **使用计算着色器**：如果环境支持，使用计算着色器进一步提高性能。
5. **释放资源**：不再需要GPU加速面部动画时，及时释放资源。
