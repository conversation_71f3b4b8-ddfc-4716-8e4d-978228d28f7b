/**
 * 动画片段适配器
 * 用于在自定义 AnimationClip 和 Three.js AnimationClip 之间进行转换
 */

import * as THREE from 'three';
import { AnimationClip } from '../AnimationClip';

export class AnimationClipAdapter {
  /**
   * 将自定义 AnimationClip 转换为 Three.js AnimationClip
   */
  public static toThreeClip(customClip: AnimationClip): THREE.AnimationClip {
    // 如果已经是 Three.js 的 AnimationClip，直接返回
    if (customClip instanceof THREE.AnimationClip) {
      return customClip as THREE.AnimationClip;
    }

    // 获取 Three.js 格式的动画片段
    const threeClip = customClip.toThreeAnimationClip();
    return threeClip;
  }

  /**
   * 将 Three.js AnimationClip 转换为自定义 AnimationClip
   */
  public static fromThreeClip(threeClip: THREE.AnimationClip): AnimationClip {
    // 创建自定义 AnimationClip
    const customClip = new AnimationClip({
      name: threeClip.name,
      duration: threeClip.duration,
      tracks: threeClip.tracks,
      uuid: threeClip.uuid
    });

    return customClip;
  }

  /**
   * 安全地将任意类型转换为 Three.js AnimationClip
   */
  public static ensureThreeClip(clip: any): THREE.AnimationClip {
    if (!clip) {
      throw new Error('AnimationClip is null or undefined');
    }

    // 如果已经是 Three.js AnimationClip
    if (clip instanceof THREE.AnimationClip) {
      return clip;
    }

    // 如果是自定义 AnimationClip
    if (clip.toThreeAnimationClip && typeof clip.toThreeAnimationClip === 'function') {
      return clip.toThreeAnimationClip();
    }

    // 如果有必要的属性，尝试创建 Three.js AnimationClip
    if (clip.name && clip.duration && clip.tracks) {
      return new THREE.AnimationClip(clip.name, clip.duration, clip.tracks, clip.blendMode);
    }

    throw new Error('Cannot convert to THREE.AnimationClip: invalid format');
  }

  /**
   * 安全地将任意类型转换为自定义 AnimationClip
   */
  public static ensureCustomClip(clip: any): AnimationClip {
    if (!clip) {
      throw new Error('AnimationClip is null or undefined');
    }

    // 如果已经是自定义 AnimationClip
    if (clip instanceof AnimationClip) {
      return clip;
    }

    // 如果是 Three.js AnimationClip
    if (clip instanceof THREE.AnimationClip) {
      return this.fromThreeClip(clip);
    }

    // 尝试从对象创建
    if (clip.name && clip.duration) {
      return new AnimationClip({
        name: clip.name,
        duration: clip.duration,
        tracks: clip.tracks || [],
        uuid: clip.uuid
      });
    }

    throw new Error('Cannot convert to custom AnimationClip: invalid format');
  }

  /**
   * 批量转换为 Three.js AnimationClip
   */
  public static toThreeClips(clips: any[]): THREE.AnimationClip[] {
    return clips.map(clip => this.ensureThreeClip(clip));
  }

  /**
   * 批量转换为自定义 AnimationClip
   */
  public static toCustomClips(clips: any[]): AnimationClip[] {
    return clips.map(clip => this.ensureCustomClip(clip));
  }

  /**
   * 检查是否为有效的动画片段
   */
  public static isValidClip(clip: any): boolean {
    if (!clip) return false;
    
    // 检查基本属性
    if (!clip.name || typeof clip.name !== 'string') return false;
    if (typeof clip.duration !== 'number' || clip.duration < 0) return false;
    
    // 检查轨道
    if (!Array.isArray(clip.tracks)) return false;
    
    return true;
  }

  /**
   * 复制动画片段
   */
  public static cloneClip(clip: any): any {
    if (clip instanceof THREE.AnimationClip) {
      return clip.clone();
    }
    
    if (clip instanceof AnimationClip) {
      return clip.clone();
    }
    
    // 通用复制
    return JSON.parse(JSON.stringify(clip));
  }
}
