/**
 * Avatar骨骼组件
 * 用于管理角色骨骼映射和姿势
 */
import * as THREE from 'three';
import { Component } from '../../core/Component';
import { Entity } from '../../core/Entity';

/**
 * 人形骨骼名称
 */
export enum HumanBoneName {
  // 躯干
  Hips = 'hips',
  Spine = 'spine',
  Chest = 'chest',
  UpperChest = 'upperChest',
  Neck = 'neck',
  Head = 'head',
  
  // 左臂
  LeftShoulder = 'leftShoulder',
  LeftUpperArm = 'leftUpperArm',
  LeftLowerArm = 'leftLowerArm',
  LeftHand = 'leftHand',
  
  // 右臂
  RightShoulder = 'rightShoulder',
  RightUpperArm = 'rightUpperArm',
  RightLowerArm = 'rightLowerArm',
  RightHand = 'rightHand',
  
  // 左腿
  LeftUpperLeg = 'leftUpperLeg',
  LeftLowerLeg = 'leftLowerLeg',
  LeftFoot = 'leftFoot',
  LeftToes = 'leftToes',
  
  // 右腿
  RightUpperLeg = 'rightUpperLeg',
  RightLowerLeg = 'rightLowerLeg',
  RightFoot = 'rightFoot',
  RightToes = 'rightToes',
  
  // 左手指
  LeftThumbProximal = 'leftThumbProximal',
  LeftThumbIntermediate = 'leftThumbIntermediate',
  LeftThumbDistal = 'leftThumbDistal',
  LeftIndexProximal = 'leftIndexProximal',
  LeftIndexIntermediate = 'leftIndexIntermediate',
  LeftIndexDistal = 'leftIndexDistal',
  LeftMiddleProximal = 'leftMiddleProximal',
  LeftMiddleIntermediate = 'leftMiddleIntermediate',
  LeftMiddleDistal = 'leftMiddleDistal',
  LeftRingProximal = 'leftRingProximal',
  LeftRingIntermediate = 'leftRingIntermediate',
  LeftRingDistal = 'leftRingDistal',
  LeftLittleProximal = 'leftLittleProximal',
  LeftLittleIntermediate = 'leftLittleIntermediate',
  LeftLittleDistal = 'leftLittleDistal',
  
  // 右手指
  RightThumbProximal = 'rightThumbProximal',
  RightThumbIntermediate = 'rightThumbIntermediate',
  RightThumbDistal = 'rightThumbDistal',
  RightIndexProximal = 'rightIndexProximal',
  RightIndexIntermediate = 'rightIndexIntermediate',
  RightIndexDistal = 'rightIndexDistal',
  RightMiddleProximal = 'rightMiddleProximal',
  RightMiddleIntermediate = 'rightMiddleIntermediate',
  RightMiddleDistal = 'rightMiddleDistal',
  RightRingProximal = 'rightRingProximal',
  RightRingIntermediate = 'rightRingIntermediate',
  RightRingDistal = 'rightRingDistal',
  RightLittleProximal = 'rightLittleProximal',
  RightLittleIntermediate = 'rightLittleIntermediate',
  RightLittleDistal = 'rightLittleDistal',
  
  // 眼睛
  LeftEye = 'leftEye',
  RightEye = 'rightEye'
}

/**
 * 骨骼信息
 */
export interface BoneInfo {
  /** 骨骼实体 */
  entity: Entity;
  /** 骨骼对象 */
  bone: THREE.Bone;
  /** 初始本地旋转 */
  initialLocalRotation: THREE.Quaternion;
  /** 初始世界旋转 */
  initialWorldRotation: THREE.Quaternion;
  /** 初始世界旋转逆 */
  initialWorldRotationInverse: THREE.Quaternion;
  /** 父骨骼世界旋转 */
  parentWorldRotation: THREE.Quaternion;
  /** 父骨骼世界旋转逆 */
  parentWorldRotationInverse: THREE.Quaternion;
}

/**
 * Avatar骨骼组件
 */
export class AvatarRigComponent extends Component {
  /** 组件类型 */
  public static readonly type: string = 'AvatarRig';
  
  /** 骨骼名称到实体的映射 */
  private bonesToEntities: Map<HumanBoneName, Entity> = new Map();
  
  /** 实体到骨骼名称的映射 */
  private entitiesToBones: Map<Entity, HumanBoneName> = new Map();
  
  /** 骨骼信息 */
  private boneInfos: Map<HumanBoneName, BoneInfo> = new Map();
  
  /** 骨骼数组 */
  private bones: THREE.Bone[] = [];
  
  /** 是否已初始化 */
  private initialized: boolean = false;

  /**
   * 构造函数
   */
  constructor() {
    super(AvatarRigComponent.type);
  }

  /**
   * 设置骨骼
   * @param boneName 骨骼名称
   * @param entity 实体
   * @param bone 骨骼对象
   */
  public setBone(boneName: HumanBoneName, entity: Entity, bone?: THREE.Bone): void {
    // 移除旧映射
    const oldEntity = this.bonesToEntities.get(boneName);
    if (oldEntity) {
      this.entitiesToBones.delete(oldEntity);
    }
    
    // 设置新映射
    this.bonesToEntities.set(boneName, entity);
    this.entitiesToBones.set(entity, boneName);
    
    // 如果提供了骨骼对象，则更新骨骼信息
    if (bone) {
      this.updateBoneInfo(boneName, entity, bone);
    }
    
    // 标记为未初始化，需要重新初始化
    this.initialized = false;
  }

  /**
   * 获取骨骼
   * @param boneName 骨骼名称
   * @returns 骨骼实体
   */
  public getBone(boneName: HumanBoneName): Entity | null {
    return this.bonesToEntities.get(boneName) || null;
  }

  /**
   * 获取骨骼名称
   * @param entity 实体
   * @returns 骨骼名称
   */
  public getBoneName(entity: Entity): HumanBoneName | null {
    return this.entitiesToBones.get(entity) || null;
  }

  /**
   * 获取所有骨骼
   * @returns 骨骼数组
   */
  public getBones(): THREE.Bone[] {
    // 如果未初始化，则初始化
    if (!this.initialized) {
      this.initializeBones();
    }
    
    return this.bones;
  }

  /**
   * 获取骨骼信息
   * @param boneName 骨骼名称
   * @returns 骨骼信息
   */
  public getBoneInfo(boneName: HumanBoneName): BoneInfo | null {
    return this.boneInfos.get(boneName) || null;
  }

  /**
   * 更新骨骼信息
   * @param boneName 骨骼名称
   * @param entity 实体
   * @param bone 骨骼对象
   */
  private updateBoneInfo(boneName: HumanBoneName, entity: Entity, bone: THREE.Bone): void {
    // 计算世界变换
    bone.updateWorldMatrix(true, false);
    
    // 获取初始本地旋转
    const initialLocalRotation = bone.quaternion.clone();
    
    // 获取初始世界旋转
    const initialWorldRotation = new THREE.Quaternion();
    bone.getWorldQuaternion(initialWorldRotation);
    
    // 计算初始世界旋转逆
    const initialWorldRotationInverse = initialWorldRotation.clone().invert();
    
    // 获取父骨骼世界旋转
    const parentWorldRotation = new THREE.Quaternion();
    if (bone.parent && bone.parent instanceof THREE.Bone) {
      bone.parent.getWorldQuaternion(parentWorldRotation);
    }
    
    // 计算父骨骼世界旋转逆
    const parentWorldRotationInverse = parentWorldRotation.clone().invert();
    
    // 创建骨骼信息
    const boneInfo: BoneInfo = {
      entity,
      bone,
      initialLocalRotation,
      initialWorldRotation,
      initialWorldRotationInverse,
      parentWorldRotation,
      parentWorldRotationInverse
    };
    
    // 更新骨骼信息
    this.boneInfos.set(boneName, boneInfo);
  }

  /**
   * 初始化骨骼
   */
  private initializeBones(): void {
    // 清空骨骼数组
    this.bones = [];
    
    // 获取所有骨骼对象
    for (const [boneName, entity] of this.bonesToEntities) {
      // 获取骨骼对象
      const bone = this.getBoneObject(entity);
      if (bone) {
        // 更新骨骼信息
        this.updateBoneInfo(boneName, entity, bone);
        // 添加到骨骼数组
        this.bones.push(bone);
      }
    }
    
    // 标记为已初始化
    this.initialized = true;
  }

  /**
   * 获取骨骼对象
   * @param entity 实体
   * @returns 骨骼对象
   */
  private getBoneObject(entity: Entity): THREE.Bone | null {
    // 这里需要根据实际情况获取骨骼对象
    // 例如，可以从实体的组件中获取骨骼对象
    
    // 获取变换组件
    const transform = entity.getComponent('Transform');
    if (!transform) return null;
    
    // 获取Object3D
    const object3D = (transform as any).getObject3D();
    if (!object3D) return null;
    
    // 如果是骨骼，则直接返回
    if (object3D instanceof THREE.Bone) {
      return object3D;
    }
    
    // 否则，查找子对象中的骨骼
    let bone: THREE.Bone = null;
    object3D.traverse((child) => {
      if (!bone && child instanceof THREE.Bone) {
        bone = child;
      }
    });
    
    return bone;
  }

  /**
   * 设置姿势
   * @param boneName 骨骼名称
   * @param rotation 旋转
   */
  public setPose(boneName: HumanBoneName, rotation: THREE.Quaternion): void {
    // 获取骨骼信息
    const boneInfo = this.boneInfos.get(boneName);
    if (!boneInfo) return;
    
    // 设置骨骼旋转
    boneInfo.bone.quaternion.copy(rotation);
    
    // 标记需要更新
    boneInfo.bone.updateMatrixWorld(true);
  }

  /**
   * 重置姿势
   */
  public resetPose(): void {
    // 重置所有骨骼到初始姿势
    for (const boneInfo of this.boneInfos.values()) {
      boneInfo.bone.quaternion.copy(boneInfo.initialLocalRotation);
      boneInfo.bone.updateMatrixWorld(true);
    }
  }

  /**
   * 克隆组件
   * @returns 克隆的组件
   */
  public clone(): AvatarRigComponent {
    const clone = new AvatarRigComponent();
    
    // 复制骨骼映射
    for (const [boneName, entity] of this.bonesToEntities) {
      clone.bonesToEntities.set(boneName, entity);
    }
    
    for (const [entity, boneName] of this.entitiesToBones) {
      clone.entitiesToBones.set(entity, boneName);
    }
    
    // 标记为未初始化
    clone.initialized = false;
    
    return clone;
  }
}
