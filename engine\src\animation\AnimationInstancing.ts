/**
 * 动画实例化系统
 * 用于优化大量相同动画的性能
 */
import * as THREE from 'three';
import { Entity } from '../core/Entity';
import { Component } from '../core/Component';
import { AnimationClip } from './AnimationClip';
import { System } from '../core/System';
import { World } from '../core/World';
import { EventEmitter } from '../utils/EventEmitter';

/**
 * 动画实例配置
 */
export interface AnimationInstanceConfig {
  /** 动画片段 */
  clip: AnimationClip | THREE.AnimationClip;
  /** 时间偏移 */
  timeOffset?: number;
  /** 时间缩放 */
  timeScale?: number;
  /** 权重 */
  weight?: number;
  /** 是否循环 */
  loop?: boolean;
}

/**
 * 动画实例组件
 */
export class AnimationInstanceComponent extends Component {
  /** 组件类型 */
  public static readonly type: string = 'AnimationInstance';

  /** 实例组ID */
  private _groupId: string;
  /** 配置 */
  private _config: AnimationInstanceConfig;
  /** 当前时间 */
  private _time: number = 0;
  /** 是否启用 */
  private _enabled: boolean = true;
  /** 是否暂停 */
  private _paused: boolean = false;
  /** 实例索引 */
  private _instanceIndex: number = -1;
  /** 变换矩阵 */
  private _matrix: THREE.Matrix4 = new THREE.Matrix4();
  /** 骨骼矩阵 */
  private _boneMatrices: Float32Array | null = null;
  /** 骨骼数量 */
  private _boneCount: number = 0;

  /**
   * 创建动画实例组件
   * @param groupId 实例组ID
   * @param config 配置
   */
  constructor(groupId: string, config: AnimationInstanceConfig) {
    super(AnimationInstanceComponent.type);
    this._groupId = groupId;
    this._config = config;
  }

  /**
   * 获取实例组ID
   * @returns 实例组ID
   */
  public getGroupId(): string {
    return this._groupId;
  }

  /**
   * 获取配置
   * @returns 配置
   */
  public getConfig(): AnimationInstanceConfig {
    return this._config;
  }

  /**
   * 设置配置
   * @param config 配置
   */
  public setConfig(config: AnimationInstanceConfig): void {
    this._config = config;
  }

  /**
   * 获取当前时间
   * @returns 当前时间
   */
  public getTime(): number {
    return this._time;
  }

  /**
   * 设置当前时间
   * @param time 当前时间
   */
  public setTime(time: number): void {
    this._time = time;
  }

  /**
   * 是否启用
   * @returns 是否启用
   */
  public isEnabled(): boolean {
    return this._enabled;
  }

  /**
   * 设置启用状态
   * @param enabled 启用状态
   */
  public setEnabled(enabled: boolean): void {
    this._enabled = enabled;
  }

  /**
   * 是否暂停
   * @returns 是否暂停
   */
  public isPaused(): boolean {
    return this._paused;
  }

  /**
   * 设置暂停状态
   * @param paused 暂停状态
   */
  public setPaused(paused: boolean): void {
    this._paused = paused;
  }

  /**
   * 获取实例索引
   * @returns 实例索引
   */
  public getInstanceIndex(): number {
    return this._instanceIndex;
  }

  /**
   * 设置实例索引
   * @param index 实例索引
   */
  public setInstanceIndex(index: number): void {
    this._instanceIndex = index;
  }

  /**
   * 获取变换矩阵
   * @returns 变换矩阵
   */
  public getMatrix(): THREE.Matrix4 {
    return this._matrix;
  }

  /**
   * 设置变换矩阵
   * @param matrix 变换矩阵
   */
  public setMatrix(matrix: THREE.Matrix4): void {
    this._matrix.copy(matrix);
  }

  /**
   * 获取骨骼矩阵
   * @returns 骨骼矩阵
   */
  public getBoneMatrices(): Float32Array | null {
    return this._boneMatrices;
  }

  /**
   * 设置骨骼矩阵
   * @param matrices 骨骼矩阵
   * @param count 骨骼数量
   */
  public setBoneMatrices(matrices: Float32Array, count: number): void {
    this._boneMatrices = matrices;
    this._boneCount = count;
  }

  /**
   * 获取骨骼数量
   * @returns 骨骼数量
   */
  public getBoneCount(): number {
    return this._boneCount;
  }

  /**
   * 更新组件
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this._enabled || this._paused) return;

    // 更新时间
    const timeScale = this._config.timeScale || 1.0;
    this._time += deltaTime * timeScale;

    // 处理循环
    if (this._config.loop) {
      const clip = this._config.clip;
      const duration = clip instanceof AnimationClip ? clip.duration : clip.duration;
      if (duration > 0) {
        this._time %= duration;
      }
    }
  }
}

/**
 * 动画实例组
 */
export interface AnimationInstanceGroup {
  /** 组ID */
  id: string;
  /** 动画片段 */
  clip: AnimationClip | THREE.AnimationClip;
  /** 实例列表 */
  instances: AnimationInstanceComponent[];
  /** 实例化网格 */
  instancedMesh?: THREE.InstancedMesh;
  /** 实例化骨骼 */
  instancedSkeleton?: THREE.InstancedBufferAttribute;
  /** 骨骼数量 */
  boneCount: number;
  /** 是否需要更新 */
  needsUpdate: boolean;
}

/**
 * 动画实例化系统配置
 */
export interface AnimationInstancingSystemConfig {
  /** 是否启用GPU蒙皮 */
  useGPUSkinning?: boolean;
  /** 是否使用实例化网格 */
  useInstancedMesh?: boolean;
  /** 最大实例数量 */
  maxInstances?: number;
  /** 是否启用调试 */
  debug?: boolean;
}

/**
 * 动画实例化系统
 * 用于管理和优化大量相同动画的性能
 */
export class AnimationInstancingSystem extends System {
  /** 配置 */
  private config: AnimationInstancingSystemConfig;
  /** 实例组映射 */
  private instanceGroups: Map<string, AnimationInstanceGroup> = new Map();
  /** 实例组件列表 */
  private instanceComponents: Map<Entity, AnimationInstanceComponent> = new Map();
  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();
  /** 着色器材质映射 */
  private shaderMaterials: Map<string, THREE.ShaderMaterial> = new Map();
  /** 是否支持GPU蒙皮 */
  private supportsGPUSkinning: boolean = false;

  /**
   * 创建动画实例化系统
   * @param world 世界
   * @param config 配置
   */
  constructor(_world: World, config: AnimationInstancingSystemConfig = {}) {
    super();
    this.config = {
      useGPUSkinning: config.useGPUSkinning !== undefined ? config.useGPUSkinning : true,
      useInstancedMesh: config.useInstancedMesh !== undefined ? config.useInstancedMesh : true,
      maxInstances: config.maxInstances || 100,
      debug: config.debug || false
    };

    // 检查是否支持GPU蒙皮
    this.checkGPUSkinningSupport();
  }

  /**
   * 检查是否支持GPU蒙皮
   */
  private checkGPUSkinningSupport(): void {
    // 检查WebGL2和相关扩展
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl2');

    if (gl) {
      // WebGL2支持实例化数组
      this.supportsGPUSkinning = true;

      if (this.config.debug) {
        console.log('GPU蒙皮支持: WebGL2');
      }
    } else {
      // 检查WebGL1扩展
      const gl1 = canvas.getContext('webgl');
      if (gl1) {
        const ext = gl1.getExtension('ANGLE_instanced_arrays');
        this.supportsGPUSkinning = !!ext;

        if (this.config.debug) {
          console.log('GPU蒙皮支持: WebGL1 + ANGLE_instanced_arrays');
        }
      } else {
        this.supportsGPUSkinning = false;

        if (this.config.debug) {
          console.warn('GPU蒙皮不支持: WebGL不可用');
        }
      }
    }

    // 如果不支持GPU蒙皮，禁用相关功能
    if (!this.supportsGPUSkinning) {
      this.config.useGPUSkinning = false;

      if (this.config.debug) {
        console.warn('已禁用GPU蒙皮，将使用CPU蒙皮');
      }
    }
  }

  /**
   * 创建实例组
   * @param id 组ID
   * @param clip 动画片段
   * @returns 实例组
   */
  public createInstanceGroup(id: string, clip: AnimationClip | THREE.AnimationClip): AnimationInstanceGroup {
    // 检查是否已存在同名组
    if (this.instanceGroups.has(id)) {
      console.warn(`实例组 "${id}" 已存在`);
      return this.instanceGroups.get(id)!;
    }

    // 创建实例组
    const group: AnimationInstanceGroup = {
      id,
      clip,
      instances: [],
      boneCount: 0,
      needsUpdate: true
    };

    // 添加到映射
    this.instanceGroups.set(id, group);

    return group;
  }

  /**
   * 创建实例组件
   * @param entity 实体
   * @param groupId 组ID
   * @param config 配置
   * @returns 实例组件
   */
  public createInstance(entity: Entity, groupId: string, config: AnimationInstanceConfig): AnimationInstanceComponent {
    // 检查实例组是否存在
    if (!this.instanceGroups.has(groupId)) {
      // 如果不存在，创建新的实例组
      this.createInstanceGroup(groupId, config.clip);
    }

    // 创建实例组件
    const component = new AnimationInstanceComponent(groupId, config);

    // 添加到实体
    entity.addComponent(component);

    // 添加到映射
    this.instanceComponents.set(entity, component);

    // 添加到实例组
    const group = this.instanceGroups.get(groupId)!;
    component.setInstanceIndex(group.instances.length);
    group.instances.push(component);
    group.needsUpdate = true;

    return component;
  }

  /**
   * 移除实例组件
   * @param entity 实体
   * @returns 是否成功移除
   */
  public removeInstance(entity: Entity): boolean {
    // 检查实体是否有实例组件
    const component = this.instanceComponents.get(entity);
    if (!component) {
      return false;
    }

    // 从实例组中移除
    const groupId = component.getGroupId();
    const group = this.instanceGroups.get(groupId);
    if (group) {
      const index = component.getInstanceIndex();
      if (index >= 0 && index < group.instances.length) {
        // 移除实例
        group.instances.splice(index, 1);

        // 更新后续实例的索引
        for (let i = index; i < group.instances.length; i++) {
          group.instances[i].setInstanceIndex(i);
        }

        group.needsUpdate = true;
      }
    }

    // 从映射中移除
    this.instanceComponents.delete(entity);

    // 从实体中移除
    entity.removeComponent(AnimationInstanceComponent.type);

    return true;
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    // 更新所有实例组件
    for (const [, component] of this.instanceComponents) {
      if (component.isEnabled()) {
        component.update(deltaTime);
      }
    }

    // 更新所有实例组
    for (const [, group] of this.instanceGroups) {
      if (group.needsUpdate) {
        this.updateInstanceGroup(group);
        group.needsUpdate = false;
      }
    }
  }

  /**
   * 更新实例组
   * @param group 实例组
   */
  private updateInstanceGroup(group: AnimationInstanceGroup): void {
    // 如果没有实例，不需要更新
    if (group.instances.length === 0) {
      return;
    }

    // 如果使用实例化网格
    if (this.config.useInstancedMesh && group.instancedMesh) {
      // 更新实例化网格
      for (let i = 0; i < group.instances.length; i++) {
        const instance = group.instances[i];
        group.instancedMesh.setMatrixAt(i, instance.getMatrix());
      }

      group.instancedMesh.instanceMatrix.needsUpdate = true;

      // 如果使用GPU蒙皮
      if (this.config.useGPUSkinning && group.instancedSkeleton) {
        // 更新骨骼矩阵
        for (let i = 0; i < group.instances.length; i++) {
          const instance = group.instances[i];
          const boneMatrices = instance.getBoneMatrices();

          if (boneMatrices) {
            // 更新实例化骨骼属性
            for (let j = 0; j < group.boneCount; j++) {
              const offset = i * group.boneCount * 16 + j * 16;
              for (let k = 0; k < 16; k++) {
                (group.instancedSkeleton.array as Float32Array)[offset + k] = boneMatrices[j * 16 + k];
              }
            }
          }
        }

        group.instancedSkeleton.needsUpdate = true;
      }
    }
  }
}
