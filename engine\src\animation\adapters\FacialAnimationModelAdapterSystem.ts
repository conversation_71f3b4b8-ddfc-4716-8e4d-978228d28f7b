/**
 * 面部动画模型适配器系统
 * 用于管理面部动画模型适配器组件
 */
import * as THREE from 'three';
import { Entity } from '../../core/Entity';
import { System } from '../../core/System';
import { World } from '../../core/World';
import { EventEmitter } from '../../utils/EventEmitter';
import { FacialExpressionType, VisemeType } from '../FacialAnimation';
import { FacialAnimationModelAdapterComponent, FacialAnimationModelAdapterConfig, FacialAnimationModelType } from './FacialAnimationModelAdapter';

/**
 * 面部动画模型适配器系统配置
 */
export interface FacialAnimationModelAdapterSystemConfig {
  /** 是否启用调试 */
  debug?: boolean;
  /** 默认模型类型 */
  defaultModelType?: FacialAnimationModelType;
  /** 是否自动检测混合形状 */
  autoDetectBlendShapes?: boolean;
}

/**
 * 面部动画模型适配器系统
 */
export class FacialAnimationModelAdapterSystem extends System {
  /** 系统类型 */
  static readonly type = 'FacialAnimationModelAdapter';

  /** 面部动画模型适配器组件 */
  private components: Map<Entity, FacialAnimationModelAdapterComponent> = new Map();

  /** 配置 */
  private config: FacialAnimationModelAdapterSystemConfig;

  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: FacialAnimationModelAdapterSystemConfig = {
    debug: false,
    defaultModelType: FacialAnimationModelType.GENERIC,
    autoDetectBlendShapes: true
  };

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /**
   * 构造函数
   * @param world 世界
   * @param config 配置
   */
  constructor(_world: World, config?: Partial<FacialAnimationModelAdapterSystemConfig>) {
    super();
    this.config = { ...FacialAnimationModelAdapterSystem.DEFAULT_CONFIG, ...config };
  }

  /**
   * 创建面部动画模型适配器
   * @param entity 实体
   * @param config 配置
   * @returns 面部动画模型适配器组件
   */
  public createModelAdapter(entity: Entity, config?: Partial<FacialAnimationModelAdapterConfig>): FacialAnimationModelAdapterComponent {
    // 检查是否已存在组件
    if (this.components.has(entity)) {
      return this.components.get(entity)!;
    }

    // 合并配置
    const mergedConfig: FacialAnimationModelAdapterConfig = {
      modelType: config?.modelType || this.config.defaultModelType,
      autoDetectBlendShapes: config?.autoDetectBlendShapes !== undefined ? config?.autoDetectBlendShapes : this.config.autoDetectBlendShapes,
      debug: config?.debug !== undefined ? config?.debug : this.config.debug,
      expressionMappings: config?.expressionMappings,
      visemeMappings: config?.visemeMappings
    };

    // 创建新组件
    const component = new FacialAnimationModelAdapterComponent(entity, mergedConfig);
    this.components.set(entity, component);

    if (this.config.debug) {
      console.log(`创建面部动画模型适配器: ${entity.id}`);
    }

    return component;
  }

  /**
   * 移除面部动画模型适配器
   * @param entity 实体
   */
  public removeModelAdapter(entity: Entity): void {
    if (this.components.has(entity)) {
      this.components.delete(entity);

      if (this.config.debug) {
        console.log(`移除面部动画模型适配器: ${entity.id}`);
      }
    }
  }

  /**
   * 获取面部动画模型适配器
   * @param entity 实体
   * @returns 面部动画模型适配器组件，如果不存在则返回null
   */
  public getModelAdapter(entity: Entity): FacialAnimationModelAdapterComponent | null {
    return this.components.get(entity) || null;
  }

  /**
   * 设置骨骼网格
   * @param entity 实体
   * @param mesh 骨骼网格
   * @returns 是否成功设置
   */
  public setSkinnedMesh(entity: Entity, mesh: THREE.SkinnedMesh): boolean {
    const adapter = this.getModelAdapter(entity);
    if (!adapter) return false;

    adapter.setSkinnedMesh(mesh);
    return true;
  }

  /**
   * 应用表情
   * @param entity 实体
   * @param expression 表情类型
   * @param weight 权重
   * @returns 是否成功应用
   */
  public applyExpression(entity: Entity, expression: FacialExpressionType, weight: number): boolean {
    const adapter = this.getModelAdapter(entity);
    if (!adapter) return false;

    return adapter.applyExpression(expression, weight);
  }

  /**
   * 应用口型
   * @param entity 实体
   * @param viseme 口型类型
   * @param weight 权重
   * @returns 是否成功应用
   */
  public applyViseme(entity: Entity, viseme: VisemeType, weight: number): boolean {
    const adapter = this.getModelAdapter(entity);
    if (!adapter) return false;

    return adapter.applyViseme(viseme, weight);
  }

  /**
   * 重置所有表情和口型
   * @param entity 实体
   * @returns 是否成功重置
   */
  public resetAll(entity: Entity): boolean {
    const adapter = this.getModelAdapter(entity);
    if (!adapter) return false;

    adapter.resetAll();
    return true;
  }

  /**
   * 创建VRM模型适配器
   * @param entity 实体
   * @param mesh 骨骼网格
   * @returns 面部动画模型适配器组件
   */
  public createVRMModelAdapter(entity: Entity, mesh: THREE.SkinnedMesh): FacialAnimationModelAdapterComponent {
    const adapter = this.createModelAdapter(entity, {
      modelType: FacialAnimationModelType.VRM,
      autoDetectBlendShapes: true
    });

    adapter.setSkinnedMesh(mesh);
    return adapter;
  }

  /**
   * 创建FBX模型适配器
   * @param entity 实体
   * @param mesh 骨骼网格
   * @returns 面部动画模型适配器组件
   */
  public createFBXModelAdapter(entity: Entity, mesh: THREE.SkinnedMesh): FacialAnimationModelAdapterComponent {
    const adapter = this.createModelAdapter(entity, {
      modelType: FacialAnimationModelType.FBX,
      autoDetectBlendShapes: true
    });

    adapter.setSkinnedMesh(mesh);
    return adapter;
  }

  /**
   * 创建GLTF模型适配器
   * @param entity 实体
   * @param mesh 骨骼网格
   * @returns 面部动画模型适配器组件
   */
  public createGLTFModelAdapter(entity: Entity, mesh: THREE.SkinnedMesh): FacialAnimationModelAdapterComponent {
    const adapter = this.createModelAdapter(entity, {
      modelType: FacialAnimationModelType.GLTF,
      autoDetectBlendShapes: true
    });

    adapter.setSkinnedMesh(mesh);
    return adapter;
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(_deltaTime: number): void {
    // 目前没有需要每帧更新的逻辑
    // 适配器组件的更新由FacialAnimationSystem触发
  }
}
