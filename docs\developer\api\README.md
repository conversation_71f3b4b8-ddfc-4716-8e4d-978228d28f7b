# IR引擎编辑器 API文档

## 概述

IR引擎编辑器API是一组TypeScript接口和类，提供对IR引擎编辑器各种功能的访问。编辑器基于React、Redux和Ant Design开发，提供了直观的可视化界面，让用户可以轻松创建和编辑3D场景、模型、材质、动画等内容。

## 目录

- [核心服务](#核心服务)
- [组件](#组件)
- [面板](#面板)
- [工具](#工具)
- [状态管理](#状态管理)
- [实用工具](#实用工具)

## 核心服务

编辑器核心服务是编辑器的基础功能模块，提供了编辑器的核心功能。

### EngineService

`EngineService`是编辑器与引擎交互的核心服务，负责初始化引擎、加载场景、管理实体等。

```typescript
class EngineService {
  /**
   * 初始化引擎
   * @param canvas 画布元素
   * @param options 引擎选项
   */
  public async initialize(canvas: HTMLCanvasElement, options?: Partial<EngineOptions>): Promise<void>;

  /**
   * 启动引擎
   */
  public start(): void;

  /**
   * 停止引擎
   */
  public stop(): void;

  /**
   * 加载场景
   * @param sceneId 场景ID
   */
  public async loadScene(sceneId: string): Promise<Scene>;

  /**
   * 保存场景
   * @param scene 场景对象
   */
  public async saveScene(scene: Scene): Promise<void>;

  /**
   * 创建实体
   * @param options 实体选项
   */
  public createEntity(options?: EntityOptions): Entity;

  /**
   * 删除实体
   * @param entity 实体对象或ID
   */
  public deleteEntity(entity: Entity | string): void;

  /**
   * 选择实体
   * @param entity 实体对象或ID
   */
  public selectEntity(entity: Entity | string): void;

  /**
   * 获取选中的实体
   */
  public getSelectedEntities(): Entity[];

  /**
   * 添加组件到实体
   * @param entity 实体对象或ID
   * @param componentType 组件类型
   * @param options 组件选项
   */
  public addComponent(entity: Entity | string, componentType: string, options?: any): Component;

  /**
   * 移除实体的组件
   * @param entity 实体对象或ID
   * @param componentType 组件类型
   */
  public removeComponent(entity: Entity | string, componentType: string): void;

  /**
   * 获取实体的组件
   * @param entity 实体对象或ID
   * @param componentType 组件类型
   */
  public getComponent(entity: Entity | string, componentType: string): Component | null;

  /**
   * 调用引擎方法
   * @param method 方法名称
   * @param args 参数
   */
  public async callEngineMethod(method: string, ...args: any[]): Promise<any>;
}
```

### ProjectService

`ProjectService`负责管理项目，包括创建、打开、保存项目等功能。

```typescript
class ProjectService {
  /**
   * 创建新项目
   * @param options 项目选项
   */
  public async createProject(options: ProjectOptions): Promise<Project>;

  /**
   * 打开项目
   * @param projectId 项目ID
   */
  public async openProject(projectId: string): Promise<Project>;

  /**
   * 保存项目
   * @param project 项目对象
   */
  public async saveProject(project: Project): Promise<void>;

  /**
   * 关闭项目
   * @param projectId 项目ID
   */
  public async closeProject(projectId: string): Promise<void>;

  /**
   * 获取项目列表
   */
  public async getProjects(): Promise<Project[]>;

  /**
   * 导出项目
   * @param projectId 项目ID
   * @param options 导出选项
   */
  public async exportProject(projectId: string, options: ExportOptions): Promise<void>;

  /**
   * 导入项目
   * @param file 项目文件
   */
  public async importProject(file: File): Promise<Project>;
}
```

### AssetService

`AssetService`负责管理资产，包括上传、下载、删除资产等功能。

```typescript
class AssetService {
  /**
   * 上传资产
   * @param file 文件对象
   * @param options 上传选项
   */
  public async uploadAsset(file: File, options?: UploadOptions): Promise<Asset>;

  /**
   * 下载资产
   * @param assetId 资产ID
   */
  public async downloadAsset(assetId: string): Promise<Blob>;

  /**
   * 删除资产
   * @param assetId 资产ID
   */
  public async deleteAsset(assetId: string): Promise<void>;

  /**
   * 获取资产列表
   * @param options 查询选项
   */
  public async getAssets(options?: QueryOptions): Promise<Asset[]>;

  /**
   * 获取资产详情
   * @param assetId 资产ID
   */
  public async getAssetDetails(assetId: string): Promise<AssetDetails>;

  /**
   * 创建资产文件夹
   * @param options 文件夹选项
   */
  public async createFolder(options: FolderOptions): Promise<Folder>;

  /**
   * 移动资产
   * @param assetId 资产ID
   * @param targetFolderId 目标文件夹ID
   */
  public async moveAsset(assetId: string, targetFolderId: string): Promise<void>;
}
```

更多核心服务的详细文档请参考各服务的API文档。

## 组件

编辑器组件是编辑器界面的构建块，包括各种UI组件和功能组件。

### 通用组件

- `Button`: 按钮组件
- `Input`: 输入框组件
- `Select`: 选择框组件
- `Checkbox`: 复选框组件
- `Radio`: 单选框组件
- `Switch`: 开关组件
- `Slider`: 滑块组件
- `ColorPicker`: 颜色选择器组件
- `FileUpload`: 文件上传组件
- `Modal`: 模态框组件
- `Tooltip`: 提示框组件
- `Popover`: 弹出框组件
- `Tabs`: 标签页组件
- `Tree`: 树形控件组件
- `Table`: 表格组件
- `Form`: 表单组件

### 编辑器特有组件

- `Viewport`: 3D视口组件
- `SceneHierarchy`: 场景层级组件
- `PropertyEditor`: 属性编辑器组件
- `AssetBrowser`: 资产浏览器组件
- `Timeline`: 时间轴组件
- `Console`: 控制台组件
- `StatusBar`: 状态栏组件
- `Toolbar`: 工具栏组件
- `MenuBar`: 菜单栏组件
- `DockPanel`: 可停靠面板组件

更多组件的详细文档请参考各组件的API文档。

## 面板

编辑器面板是编辑器界面的主要组成部分，包括各种功能面板。

### 核心面板

- `ScenePanel`: 场景面板
- `HierarchyPanel`: 层级面板
- `InspectorPanel`: 检查器面板
- `AssetsPanel`: 资产面板
- `ConsolePanel`: 控制台面板
- `LayersPanel`: 图层面板
- `InstancesPanel`: 实例面板
- `CollaborationPanel`: 协作面板
- `UserTestingPanel`: 用户测试面板
- `DebugPanel`: 调试面板

更多面板的详细文档请参考各面板的API文档。

## 工具

编辑器工具是编辑器提供的各种功能工具。

### 变换工具

- `TransformTool`: 变换工具
- `MoveTool`: 移动工具
- `RotateTool`: 旋转工具
- `ScaleTool`: 缩放工具

### 编辑工具

- `SelectTool`: 选择工具
- `PaintTool`: 绘制工具
- `EraseTool`: 擦除工具
- `CloneTool`: 克隆工具
- `SnapTool`: 对齐工具

更多工具的详细文档请参考各工具的API文档。

## 状态管理

编辑器使用Redux进行状态管理，包括各种状态切片。

### 核心状态

- `authSlice`: 认证状态
- `projectSlice`: 项目状态
- `sceneSlice`: 场景状态
- `entitySlice`: 实体状态
- `assetSlice`: 资产状态
- `uiSlice`: UI状态
- `toolSlice`: 工具状态
- `historySlice`: 历史记录状态
- `collaborationSlice`: 协作状态

更多状态管理的详细文档请参考各状态切片的API文档。

## 实用工具

编辑器提供了各种实用工具函数。

### 数学工具

- `Vector3`: 三维向量
- `Quaternion`: 四元数
- `Matrix4`: 4x4矩阵
- `Euler`: 欧拉角
- `Box3`: 三维包围盒
- `Sphere`: 球体
- `Ray`: 射线
- `Color`: 颜色

### 文件工具

- `FileUtils`: 文件工具
- `ImageUtils`: 图像工具
- `ModelUtils`: 模型工具
- `TextureUtils`: 纹理工具
- `AudioUtils`: 音频工具
- `VideoUtils`: 视频工具

更多实用工具的详细文档请参考各工具的API文档。
