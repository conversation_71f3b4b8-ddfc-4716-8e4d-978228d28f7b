# 视觉脚本

IR引擎编辑器的视觉脚本系统提供了一种无需编写代码的方式来创建交互式内容和游戏逻辑。本文档将详细介绍如何使用视觉脚本系统创建各种行为和逻辑，以及如何将视觉脚本与其他系统集成。

## 视觉脚本概述

### 什么是视觉脚本？

视觉脚本是一种基于节点和连接的可视化编程方式，允许用户通过连接不同的节点来创建逻辑流程，而无需编写传统的代码。这使得非程序员也能创建复杂的交互和行为。

### 主要功能

IR引擎的视觉脚本系统支持以下主要功能：

- **节点编辑器**：直观的节点连接界面
- **丰富的节点库**：包含数百种预定义节点
- **变量系统**：创建和管理全局和局部变量
- **事件系统**：响应各种游戏和用户事件
- **调试工具**：实时调试和监控脚本执行
- **模块化**：创建可重用的子图和函数
- **扩展性**：创建自定义节点和功能
- **性能优化**：高效执行复杂的视觉脚本

### 视觉脚本组件

视觉脚本系统主要包括以下组件：

- **视觉脚本编辑器**：创建和编辑视觉脚本的主界面
- **视觉脚本组件**：附加到实体上，包含视觉脚本数据和执行逻辑
- **视觉脚本资产**：保存为资产的视觉脚本，可以在多个对象间共享
- **视觉脚本变量**：存储和管理脚本中使用的变量
- **视觉脚本事件**：触发脚本执行的事件系统

## 视觉脚本编辑器

### 打开视觉脚本编辑器

1. 选择带有视觉脚本组件的对象
2. 在属性面板中，找到视觉脚本组件
3. 点击"编辑脚本"按钮
4. 或点击顶部菜单栏的"窗口 > 视觉脚本编辑器"

![视觉脚本编辑器](../../assets/images/visual-script-editor.png)

### 编辑器界面

视觉脚本编辑器包含以下主要部分：

- **工具栏**：包含常用工具和操作按钮
- **节点库**：显示可用的节点类别和节点
- **画布**：创建和连接节点的主要区域
- **属性面板**：显示和编辑选中节点的属性
- **变量面板**：管理脚本中使用的变量
- **调试面板**：调试和监控脚本执行
- **导航器**：在大型脚本中导航

### 基本操作

- **添加节点**：从节点库拖拽节点到画布，或右键点击画布选择节点
- **连接节点**：点击一个节点的输出端口，拖动到另一个节点的输入端口
- **断开连接**：右键点击连接线选择"断开连接"，或按住Alt键点击连接线
- **删除节点**：选中节点后按Delete键，或右键点击节点选择"删除"
- **复制节点**：选中节点后按Ctrl+C（Windows）/Command+C（Mac），然后按Ctrl+V/Command+V
- **组织节点**：选中多个节点后右键点击，选择"创建组"或"对齐节点"

## 创建视觉脚本

### 添加视觉脚本组件

1. 在场景中选择对象
2. 在属性面板中，点击"添加组件"按钮
3. 选择"脚本 > 视觉脚本组件"
4. 点击"创建新脚本"或"加载脚本"

![视觉脚本组件](../../assets/images/visual-script-component.png)

### 创建基本脚本

以下是创建简单视觉脚本的步骤：

1. 打开视觉脚本编辑器
2. 从节点库中添加"事件 > 开始"节点（脚本开始执行的入口点）
3. 添加功能节点（如"变换 > 设置位置"）
4. 连接"开始"节点的执行输出到功能节点的执行输入
5. 设置功能节点的参数（如位置值）
6. 点击"保存"按钮

![基本视觉脚本](../../assets/images/basic-visual-script.png)

### 事件驱动脚本

大多数视觉脚本是事件驱动的，响应特定事件执行操作：

1. 从节点库中添加事件节点（如"事件 > 鼠标点击"）
2. 添加要执行的操作节点（如"动画 > 播放动画"）
3. 连接事件节点的执行输出到操作节点的执行输入
4. 设置操作节点的参数
5. 保存脚本

## 节点类型

### 事件节点

事件节点是脚本执行的触发点，包括：

- **生命周期事件**：开始、更新、销毁等
- **输入事件**：鼠标点击、键盘按键、触摸等
- **碰撞事件**：碰撞开始、碰撞结束等
- **交互事件**：交互开始、交互结束等
- **自定义事件**：用户定义的事件

### 逻辑节点

逻辑节点用于控制脚本的执行流程：

- **分支**：If、Switch等条件分支
- **循环**：For、While等循环结构
- **序列**：按顺序执行多个操作
- **延迟**：延迟执行操作
- **计时器**：定时执行操作

### 数学节点

数学节点用于执行各种数学运算：

- **基本运算**：加、减、乘、除
- **向量运算**：向量加减、点积、叉积等
- **插值**：线性插值、平滑插值等
- **随机**：随机数、随机向量等
- **转换**：类型转换、坐标转换等

### 变量节点

变量节点用于读取和设置变量：

- **获取变量**：读取变量值
- **设置变量**：设置变量值
- **变量操作**：增加、减少变量值等

### 实体节点

实体节点用于操作场景中的实体：

- **查找实体**：通过名称、标签等查找实体
- **创建实体**：动态创建新实体
- **销毁实体**：销毁实体
- **获取组件**：获取实体上的组件
- **设置组件属性**：修改组件属性

### 变换节点

变换节点用于修改实体的变换属性：

- **获取/设置位置**：读取或修改实体位置
- **获取/设置旋转**：读取或修改实体旋转
- **获取/设置缩放**：读取或修改实体缩放
- **朝向**：使实体朝向特定方向
- **跟随**：使实体跟随另一个实体

### 动画节点

动画节点用于控制动画：

- **播放动画**：播放指定动画
- **停止动画**：停止当前动画
- **设置动画参数**：修改动画参数
- **获取动画状态**：获取当前动画状态
- **动画事件**：响应动画事件

### 物理节点

物理节点用于物理模拟：

- **应用力**：对刚体应用力
- **应用扭矩**：对刚体应用扭矩
- **射线检测**：执行物理射线检测
- **设置物理属性**：修改物理属性
- **物理约束**：创建和管理物理约束

### 音频节点

音频节点用于控制音频：

- **播放音频**：播放音频剪辑
- **停止音频**：停止音频播放
- **设置音量**：调整音频音量
- **设置音调**：调整音频音调
- **音频事件**：响应音频事件

### UI节点

UI节点用于控制用户界面：

- **显示/隐藏UI**：控制UI元素的可见性
- **设置文本**：修改文本内容
- **设置图像**：修改图像内容
- **UI动画**：控制UI动画
- **UI事件**：响应UI事件

### 网络节点

网络节点用于多用户场景：

- **发送消息**：向其他用户发送消息
- **接收消息**：接收来自其他用户的消息
- **同步变量**：同步变量值
- **RPC调用**：远程过程调用
- **网络事件**：响应网络事件

### AI节点

AI节点用于人工智能功能：

- **寻路**：AI角色寻路
- **行为树**：控制AI行为
- **感知**：AI感知周围环境
- **决策**：AI决策逻辑
- **学习**：AI学习和适应

## 变量系统

### 创建变量

1. 在视觉脚本编辑器中，点击"变量"面板
2. 点击"+"按钮创建新变量
3. 设置变量属性：
   - **名称**：变量的唯一标识符
   - **类型**：变量的数据类型（布尔、整数、浮点数、字符串、向量等）
   - **作用域**：变量的作用范围（局部、全局）
   - **默认值**：变量的初始值
   - **描述**：变量的说明文本
4. 点击"创建"按钮

![创建变量](../../assets/images/create-variable.png)

### 变量类型

IR引擎支持以下变量类型：

- **基本类型**：布尔、整数、浮点数、字符串
- **向量类型**：二维向量、三维向量、四维向量
- **颜色类型**：RGB颜色、RGBA颜色
- **引用类型**：实体引用、组件引用、资产引用
- **数组类型**：各种类型的数组
- **枚举类型**：预定义的枚举值
- **自定义类型**：用户定义的复合类型

### 使用变量

1. 从节点库中添加"变量 > 获取变量"或"变量 > 设置变量"节点
2. 在节点属性中，选择要操作的变量
3. 连接节点的输入或输出端口到其他节点

或者，直接在参数字段中引用变量：

1. 点击参数字段旁边的变量图标
2. 从变量列表中选择要使用的变量
3. 参数将显示为变量引用

## 流程控制

### 条件分支

创建条件分支（If语句）：

1. 从节点库中添加"逻辑 > 分支"节点
2. 连接条件输入（布尔值）
3. 连接"真"和"假"执行输出到不同的操作节点

![条件分支](../../assets/images/condition-branch.png)

### 循环

创建循环（For循环）：

1. 从节点库中添加"逻辑 > For循环"节点
2. 设置起始值、结束值和步长
3. 连接循环体执行输出到要重复执行的操作节点
4. 连接操作节点的执行输出回到循环节点的循环继续输入
5. 连接循环完成执行输出到循环后的操作节点

![For循环](../../assets/images/for-loop.png)

### 序列

创建序列（按顺序执行多个操作）：

1. 从节点库中添加"逻辑 > 序列"节点
2. 设置序列中的步骤数量
3. 连接每个步骤的执行输出到相应的操作节点
4. 连接最后一个操作节点的执行输出到序列节点的完成输入

![序列](../../assets/images/sequence.png)

## 调试和测试

### 调试模式

1. 在视觉脚本编辑器中，点击工具栏的"调试"按钮
2. 进入游戏模式或预览模式
3. 脚本执行时，活动节点和数据流将在编辑器中高亮显示
4. 使用调试控制按钮（暂停、步进、继续）控制执行

![调试模式](../../assets/images/debug-mode.png)

### 断点

设置断点：

1. 右键点击节点
2. 选择"添加断点"
3. 脚本执行到该节点时将暂停
4. 在断点处，可以检查变量值和执行状态
5. 使用调试控制按钮继续执行

### 监视变量

监视变量值：

1. 在调试面板中，点击"+"按钮
2. 选择要监视的变量
3. 变量值将在调试面板中实时更新
4. 可以设置条件断点，当变量满足特定条件时暂停执行

### 日志输出

添加日志输出：

1. 从节点库中添加"调试 > 打印日志"节点
2. 连接要记录的数据到日志节点的输入
3. 设置日志级别（信息、警告、错误）
4. 日志将显示在控制台面板中

## 模块化和重用

### 创建子图

子图是可重用的视觉脚本片段：

1. 选择要封装为子图的节点
2. 右键点击，选择"创建子图"
3. 输入子图名称
4. 定义子图的输入和输出参数
5. 点击"创建"按钮

![创建子图](../../assets/images/create-subgraph.png)

### 使用子图

1. 从节点库中添加"子图 > [子图名称]"节点
2. 连接执行流和数据输入
3. 子图将作为单个节点执行其内部逻辑

### 创建函数

函数是可重用的纯逻辑片段，没有执行流：

1. 在视觉脚本编辑器中，点击"函数"面板
2. 点击"创建函数"按钮
3. 设置函数名称、输入参数和返回类型
4. 在函数编辑器中创建函数逻辑
5. 点击"保存"按钮

### 使用函数

1. 从节点库中添加"函数 > [函数名称]"节点
2. 连接数据输入
3. 使用函数的返回值连接到其他节点

## 高级功能

### 状态机

创建状态机：

1. 从节点库中添加"高级 > 状态机"节点
2. 添加状态节点（如"状态机 > 状态"）
3. 定义每个状态的进入、更新和退出逻辑
4. 定义状态之间的转换条件
5. 连接状态机的执行流

![状态机](../../assets/images/state-machine.png)

### 协程

创建协程（可暂停和恢复的执行流）：

1. 从节点库中添加"高级 > 协程"节点
2. 定义协程逻辑
3. 使用"等待"节点暂停执行
4. 协程将在等待条件满足后自动恢复

### 事件系统

创建自定义事件：

1. 在视觉脚本编辑器中，点击"事件"面板
2. 点击"创建事件"按钮
3. 设置事件名称和参数
4. 点击"创建"按钮

触发自定义事件：

1. 从节点库中添加"事件 > 触发事件"节点
2. 选择要触发的事件
3. 设置事件参数
4. 连接执行流

监听自定义事件：

1. 从节点库中添加"事件 > 事件监听器"节点
2. 选择要监听的事件
3. 连接事件触发时的执行输出到响应逻辑

## 性能优化

### 优化技巧

- **减少更新节点**：避免在每帧更新中执行不必要的操作
- **使用事件驱动**：使用事件触发而不是持续检查
- **缓存结果**：缓存计算结果而不是重复计算
- **使用对象池**：重用对象而不是频繁创建和销毁
- **批处理操作**：合并多个类似操作
- **使用原生节点**：使用优化的原生节点而不是复杂的自定义逻辑

### 性能分析

1. 在视觉脚本编辑器中，点击"性能"面板
2. 启用性能分析
3. 运行脚本
4. 查看每个节点的执行时间和调用次数
5. 识别性能瓶颈并优化

## 与代码集成

### 调用JavaScript代码

1. 从节点库中添加"脚本 > 执行JavaScript"节点
2. 在代码编辑器中编写JavaScript代码
3. 定义输入和输出参数
4. 连接执行流和数据

### 创建自定义节点

1. 使用JavaScript API创建自定义节点类
2. 定义节点的输入、输出和执行逻辑
3. 注册节点到视觉脚本系统
4. 自定义节点将出现在节点库中

## 最佳实践

### 组织技巧

- **使用注释**：添加注释节点说明脚本功能
- **使用组**：将相关节点组织到组中
- **命名规范**：使用清晰一致的命名约定
- **模块化**：将复杂逻辑拆分为子图和函数
- **文档化**：记录脚本的用途、参数和依赖

### 常见陷阱

- **循环引用**：避免变量或节点之间的循环引用
- **过度复杂**：避免创建过于复杂的单个脚本
- **性能问题**：注意高频执行节点的性能影响
- **未处理的错误**：添加错误处理和边界检查
- **硬编码值**：使用变量而不是硬编码值

## 下一步

现在您已经了解了视觉脚本系统的基本功能，可以继续学习其他相关功能：

- [交互系统](./interaction-system.md)
- [动画系统](./animation-system.md)
- [物理系统](./physics-system.md)
- [视觉脚本高级技巧](../advanced/advanced-visual-scripting.md)
