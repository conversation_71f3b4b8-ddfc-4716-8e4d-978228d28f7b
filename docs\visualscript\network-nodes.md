# 视觉脚本网络节点使用指南

本文档介绍了IR引擎视觉脚本系统中的网络节点，包括网络连接、消息发送/接收等功能，以及它们的使用方法和示例。

## 目录

- [网络节点概述](#网络节点概述)
- [网络连接节点](#网络连接节点)
- [消息发送/接收节点](#消息发送接收节点)
- [用户管理节点](#用户管理节点)
- [WebRTC节点](#webrtc节点)
- [示例](#示例)
  - [基本网络通信示例](#基本网络通信示例)
  - [多用户交互示例](#多用户交互示例)

## 网络节点概述

网络节点提供了与网络系统交互的功能，包括连接到服务器、发送和接收消息、管理用户等。这些节点使用户能够在视觉脚本中轻松创建网络功能，无需编写复杂的网络代码。

## 网络连接节点

### 连接到服务器节点

连接到服务器节点用于建立与网络服务器的连接。

**节点类型**: `network/connectToServer`

**输入插槽**:
- `flow` (Flow): 输入流程
- `serverUrl` (string): 服务器URL，默认为"ws://localhost:8080"
- `roomId` (string): 房间ID，可选

**输出插槽**:
- `success` (Flow): 连接成功
- `fail` (Flow): 连接失败
- `connected` (boolean): 是否已连接

**使用示例**:
```json
{
  "id": "connect",
  "type": "network/connectToServer",
  "parameters": {
    "serverUrl": {
      "value": "ws://example.com:8080"
    },
    "roomId": {
      "value": "room1"
    }
  }
}
```

### 断开连接节点

断开连接节点用于断开与网络服务器的连接。

**节点类型**: `network/disconnect`

**输入插槽**:
- `flow` (Flow): 输入流程

**输出插槽**:
- `flow` (Flow): 输出流程

**使用示例**:
```json
{
  "id": "disconnect",
  "type": "network/disconnect"
}
```

## 消息发送/接收节点

### 发送网络消息节点

发送网络消息节点用于向其他用户发送网络消息。

**节点类型**: `network/sendMessage`

**输入插槽**:
- `flow` (Flow): 输入流程
- `message` (object): 消息内容
- `targetUserId` (string): 目标用户ID，为空则发送给所有用户
- `reliable` (boolean): 是否可靠传输，默认为true

**输出插槽**:
- `flow` (Flow): 输出流程
- `success` (boolean): 是否发送成功

**使用示例**:
```json
{
  "id": "sendMessage",
  "type": "network/sendMessage",
  "parameters": {
    "message": {
      "value": {
        "type": "chat",
        "content": "你好，世界！"
      }
    },
    "targetUserId": {
      "value": ""
    },
    "reliable": {
      "value": true
    }
  }
}
```

### 接收网络消息节点

接收网络消息节点是一个事件节点，当接收到网络消息时触发。

**节点类型**: `network/events/onMessage`

**输出插槽**:
- `flow` (Flow): 输出流程
- `message` (object): 消息内容
- `senderId` (string): 发送者ID

**使用示例**:
```json
{
  "id": "onMessage",
  "type": "network/events/onMessage"
}
```

## 用户管理节点

### 获取当前用户节点

获取当前用户节点用于获取当前用户的信息。

**节点类型**: `network/user/getCurrent`

**输入插槽**:
- `flow` (Flow): 输入流程

**输出插槽**:
- `flow` (Flow): 输出流程
- `userId` (string): 用户ID
- `username` (string): 用户名
- `role` (string): 用户角色

**使用示例**:
```json
{
  "id": "getCurrentUser",
  "type": "network/user/getCurrent"
}
```

### 获取在线用户节点

获取在线用户节点用于获取当前在线的所有用户。

**节点类型**: `network/user/getOnline`

**输入插槽**:
- `flow` (Flow): 输入流程

**输出插槽**:
- `flow` (Flow): 输出流程
- `users` (array): 用户列表

**使用示例**:
```json
{
  "id": "getOnlineUsers",
  "type": "network/user/getOnline"
}
```

## WebRTC节点

### 创建WebRTC连接节点

创建WebRTC连接节点用于创建与其他用户的WebRTC连接。

**节点类型**: `network/webrtc/createConnection`

**输入插槽**:
- `flow` (Flow): 输入流程
- `targetUserId` (string): 目标用户ID

**输出插槽**:
- `success` (Flow): 连接成功
- `fail` (Flow): 连接失败
- `connection` (object): WebRTC连接对象

**使用示例**:
```json
{
  "id": "createWebRTCConnection",
  "type": "network/webrtc/createConnection",
  "parameters": {
    "targetUserId": {
      "value": "user123"
    }
  }
}
```

### 发送WebRTC数据节点

发送WebRTC数据节点用于通过WebRTC连接发送数据。

**节点类型**: `network/webrtc/sendData`

**输入插槽**:
- `flow` (Flow): 输入流程
- `connection` (object): WebRTC连接对象
- `data` (object): 要发送的数据

**输出插槽**:
- `flow` (Flow): 输出流程
- `success` (boolean): 是否发送成功

**使用示例**:
```json
{
  "id": "sendWebRTCData",
  "type": "network/webrtc/sendData",
  "parameters": {
    "data": {
      "value": {
        "type": "position",
        "x": 0,
        "y": 0,
        "z": 0
      }
    }
  }
}
```

## 示例

### 基本网络通信示例

以下是一个基本的网络通信示例，演示如何连接到服务器并发送/接收消息：

```json
{
  "nodes": [
    {
      "id": "start",
      "type": "core/events/onStart",
      "metadata": {
        "positionX": 100,
        "positionY": 100
      },
      "flows": {
        "flow": {
          "nodeId": "connect",
          "socket": "flow"
        }
      }
    },
    {
      "id": "connect",
      "type": "network/connectToServer",
      "metadata": {
        "positionX": 300,
        "positionY": 100
      },
      "parameters": {
        "serverUrl": {
          "value": "ws://localhost:8080"
        }
      },
      "flows": {
        "success": {
          "nodeId": "logSuccess",
          "socket": "flow"
        },
        "fail": {
          "nodeId": "logFail",
          "socket": "flow"
        }
      }
    },
    {
      "id": "logSuccess",
      "type": "core/debug/log",
      "metadata": {
        "positionX": 500,
        "positionY": 50
      },
      "parameters": {
        "message": {
          "value": "连接服务器成功"
        }
      }
    },
    {
      "id": "logFail",
      "type": "core/debug/log",
      "metadata": {
        "positionX": 500,
        "positionY": 150
      },
      "parameters": {
        "message": {
          "value": "连接服务器失败"
        }
      }
    },
    {
      "id": "onMessage",
      "type": "network/events/onMessage",
      "metadata": {
        "positionX": 100,
        "positionY": 300
      },
      "flows": {
        "flow": {
          "nodeId": "logMessage",
          "socket": "flow"
        }
      }
    },
    {
      "id": "logMessage",
      "type": "core/debug/log",
      "metadata": {
        "positionX": 300,
        "positionY": 300
      },
      "parameters": {
        "message": {
          "value": "收到消息: ${message}"
        }
      }
    }
  ]
}
```

### 多用户交互示例

请参考 `examples/visualscript/NetworkExample.ts` 中的完整示例，该示例演示了如何使用视觉脚本系统的网络节点实现多用户交互。
