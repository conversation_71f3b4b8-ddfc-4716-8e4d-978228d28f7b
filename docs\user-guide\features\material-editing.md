# 材质编辑

材质编辑是IR引擎编辑器的核心功能之一，允许您创建、编辑和管理3D对象的材质。本文档将详细介绍如何使用材质编辑器创建各种类型的材质，以及如何调整材质参数以获得理想的视觉效果。

## 材质基础

### 什么是材质？

材质定义了3D对象的外观，包括颜色、反射率、透明度等视觉属性。IR引擎支持多种材质类型，从简单的基础材质到复杂的物理渲染材质，满足不同的渲染需求。

### 材质类型

IR引擎支持以下主要材质类型：

- **标准材质（Standard）**：基于物理的渲染材质，适用于大多数真实世界的物体
- **基础材质（Basic）**：简单的非物理材质，不受光照影响
- **Lambert材质**：漫反射材质，适用于哑光表面
- **Phong材质**：具有高光反射的材质，适用于光滑表面
- **卡通材质（Toon）**：卡通渲染风格的材质
- **深度材质（Depth）**：用于显示深度信息的材质
- **法线材质（Normal）**：用于显示法线信息的材质
- **线框材质（Wireframe）**：显示对象的线框结构
- **着色器材质（Shader）**：自定义着色器材质，提供最大的灵活性

## 材质面板

### 打开材质面板

1. 点击顶部菜单栏的"视图 > 材质面板"
2. 或使用快捷键Ctrl+4（Windows）/Command+4（Mac）

![材质面板](../../assets/images/material-panel.png)

### 材质面板界面

材质面板包含以下主要部分：

- **材质列表**：显示项目中的所有材质
- **预览区域**：显示当前选中材质的预览
- **工具栏**：提供创建、导入、导出等操作
- **搜索框**：用于搜索材质

## 创建和管理材质

### 创建新材质

1. 在材质面板中，点击"+"按钮
2. 在弹出的菜单中，选择材质类型
3. 输入材质名称
4. 点击"创建"按钮

![创建新材质](../../assets/images/create-new-material.png)

### 导入材质

1. 在材质面板中，点击"导入"按钮
2. 在文件浏览器中，选择要导入的材质文件（.mat）
3. 点击"打开"按钮

### 导出材质

1. 在材质面板中，选择要导出的材质
2. 点击右键并选择"导出"
3. 在文件浏览器中，选择保存位置和文件名
4. 点击"保存"按钮

### 复制材质

1. 在材质面板中，选择要复制的材质
2. 点击右键并选择"复制"
3. 输入新材质名称
4. 点击"确定"按钮

### 删除材质

1. 在材质面板中，选择要删除的材质
2. 点击右键并选择"删除"
3. 在确认对话框中，点击"确定"按钮

## 编辑材质

### 打开材质编辑器

1. 在材质面板中，双击材质
2. 或选择材质后点击右键，选择"编辑"

![材质编辑器](../../assets/images/material-editor.png)

### 材质编辑器界面

材质编辑器包含以下主要部分：

- **预览区域**：显示材质在不同预览模型上的效果
- **属性面板**：显示和编辑材质属性
- **纹理面板**：管理材质使用的纹理贴图
- **着色器编辑器**：对于着色器材质，提供着色器代码编辑功能

### 预览控制

- **预览模型**：可以选择球体、立方体、平面等不同形状
- **旋转预览**：拖动预览区域可以旋转预览模型
- **缩放预览**：使用鼠标滚轮可以缩放预览模型
- **环境设置**：可以更改预览环境光照和背景

## 编辑材质属性

### 标准材质属性

标准材质（PBR材质）包含以下主要属性：

- **基础颜色（Base Color）**：材质的主要颜色
- **金属度（Metalness）**：材质的金属特性，0表示非金属，1表示金属
- **粗糙度（Roughness）**：材质表面的粗糙程度，0表示光滑，1表示粗糙
- **法线贴图（Normal Map）**：用于增加表面细节的法线贴图
- **环境光遮蔽（Ambient Occlusion）**：模拟环境光遮蔽效果
- **自发光（Emissive）**：材质的自发光颜色和强度
- **透明度（Opacity）**：材质的透明度，0表示完全透明，1表示完全不透明
- **折射率（Refraction）**：材质的光线折射率

### 调整颜色属性

1. 点击颜色属性旁边的颜色方块
2. 在弹出的颜色选择器中，选择所需颜色
3. 或直接输入RGB或HEX值

![颜色选择器](../../assets/images/color-picker.png)

### 调整数值属性

1. 拖动滑块调整数值
2. 或直接在输入框中输入精确数值
3. 点击输入框旁边的重置按钮可恢复默认值

### 使用纹理贴图

1. 点击属性旁边的纹理槽
2. 在弹出的资产选择器中，选择要使用的纹理
3. 或点击"导入"按钮导入新纹理
4. 设置纹理的缩放、偏移和旋转参数

![纹理设置](../../assets/images/texture-settings.png)

## 纹理管理

### 添加纹理

1. 在材质编辑器的纹理面板中，点击"添加纹理"按钮
2. 选择纹理类型（颜色贴图、法线贴图、粗糙度贴图等）
3. 在资产选择器中，选择要使用的纹理
4. 点击"确定"按钮

### 编辑纹理参数

1. 在纹理面板中，选择要编辑的纹理
2. 调整以下参数：
   - **缩放（Scale）**：调整纹理的缩放比例
   - **偏移（Offset）**：调整纹理的位置偏移
   - **旋转（Rotation）**：调整纹理的旋转角度
   - **重复（Repeat）**：设置纹理的重复方式
   - **过滤（Filter）**：设置纹理的过滤方式

### 移除纹理

1. 在纹理面板中，选择要移除的纹理
2. 点击"移除"按钮
3. 在确认对话框中，点击"确定"按钮

## 高级材质功能

### 创建着色器材质

1. 在材质面板中，点击"+"按钮
2. 选择"着色器材质"
3. 输入材质名称
4. 点击"创建"按钮
5. 在着色器编辑器中编写顶点着色器和片段着色器代码
6. 点击"编译"按钮测试着色器
7. 点击"保存"按钮保存材质

![着色器编辑器](../../assets/images/shader-editor.png)

### 使用材质实例

材质实例允许您创建基于现有材质的变体，而不影响原始材质：

1. 在材质面板中，选择基础材质
2. 点击右键并选择"创建实例"
3. 输入实例名称
4. 点击"创建"按钮
5. 编辑实例材质的属性

### 创建材质函数

材质函数是可重用的材质片段，可以在多个材质中使用：

1. 在材质面板中，点击"+"按钮
2. 选择"材质函数"
3. 输入函数名称
4. 点击"创建"按钮
5. 在材质函数编辑器中创建函数
6. 点击"保存"按钮保存函数

## 材质预设

### 使用内置预设

1. 在材质编辑器中，点击"预设"按钮
2. 从预设列表中选择所需预设
3. 点击"应用"按钮

![材质预设](../../assets/images/material-presets.png)

### 创建自定义预设

1. 在材质编辑器中，调整材质参数
2. 点击"预设"按钮
3. 点击"保存为预设"
4. 输入预设名称
5. 点击"保存"按钮

### 管理预设

1. 在材质编辑器中，点击"预设"按钮
2. 点击"管理预设"
3. 在预设管理器中，可以重命名、删除或导出预设

## 材质应用

### 将材质应用到对象

1. 在场景中选择对象
2. 在属性面板的"材质"部分，点击材质槽
3. 在弹出的材质选择器中，选择要应用的材质
4. 点击"应用"按钮

### 创建多材质对象

对于需要多个材质的复杂对象：

1. 在场景中选择对象
2. 在属性面板的"材质"部分，点击"添加材质槽"按钮
3. 为每个材质槽分配不同的材质
4. 使用材质ID编辑器指定哪些部分使用哪个材质槽

## 最佳实践

### 性能优化

- 尽量减少使用的纹理数量
- 对于移动平台，使用较小的纹理分辨率
- 使用纹理压缩格式
- 对于不需要高精度的属性，使用纹理图集合并多个属性
- 避免使用过于复杂的着色器

### 组织材质

- 使用清晰的命名约定
- 将相关材质分组到文件夹中
- 为常用材质创建预设
- 使用材质实例而不是复制材质
- 记录特殊材质的用途和设置

## 故障排除

### 材质显示不正确

如果材质在场景中显示不正确：

1. 检查材质类型是否适合您的渲染需求
2. 确保纹理文件格式正确且未损坏
3. 检查纹理坐标和UV映射
4. 确保法线贴图使用正确的格式和设置
5. 检查光照设置是否与材质类型兼容

### 材质性能问题

如果材质导致性能问题：

1. 使用性能分析工具找出瓶颈
2. 减少纹理分辨率或数量
3. 简化着色器代码
4. 使用LOD（细节层次）材质
5. 考虑使用烘焙技术而不是复杂的实时材质

## 下一步

现在您已经了解了材质编辑的基本功能，可以继续学习其他相关功能：

- [光照系统](./lighting-system.md)
- [后期处理效果](./post-processing.md)
- [渲染设置](./rendering-settings.md)
- [材质高级技巧](../advanced/advanced-materials.md)
