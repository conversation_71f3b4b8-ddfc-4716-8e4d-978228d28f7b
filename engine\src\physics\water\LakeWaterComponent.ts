/**
 * 湖泊水体组件
 * 用于模拟湖泊水体，包括波动、反射等特性
 */
import * as THREE from 'three';
import { WaterBodyComponent, WaterBodyType, WaterBodyShape, WaterBodyConfig } from './WaterBodyComponent';
import { Entity } from '../../core/Entity';
import { Debug } from '../../utils/Debug';

/**
 * 湖泊形状类型
 */
export enum LakeShapeType {
  /** 圆形 */
  CIRCLE = 'circle',
  /** 椭圆形 */
  ELLIPSE = 'ellipse',
  /** 不规则形状 */
  IRREGULAR = 'irregular',
  /** 自定义多边形 */
  CUSTOM_POLYGON = 'custom_polygon'
}

/**
 * 湖泊水体配置
 */
export interface LakeWaterConfig extends WaterBodyConfig {
  /** 湖泊形状类型 */
  shapeType?: LakeShapeType;
  /** 湖泊宽度 */
  width?: number;
  /** 湖泊长度 */
  length?: number;
  /** 湖泊深度 */
  depth?: number;
  /** 湖泊边缘点（用于不规则形状或自定义多边形） */
  edgePoints?: THREE.Vector2[];
  /** 湖泊分辨率（几何体细分数） */
  resolution?: number;
  /** 湖床高度变化 */
  bedHeightVariation?: number;
  /** 是否生成湖岸 */
  generateShore?: boolean;
  /** 湖岸高度 */
  shoreHeight?: number;
  /** 湖岸宽度 */
  shoreWidth?: number;
}

/**
 * 湖泊水体组件
 */
export class LakeWaterComponent extends WaterBodyComponent {
  /** 湖泊形状类型 */
  private shapeType: LakeShapeType;
  /** 湖泊宽度 */
  private width: number;
  /** 湖泊长度 */
  private length: number;
  /** 湖泊深度 */
  private depth: number;
  /** 湖泊边缘点 */
  private edgePoints: THREE.Vector2[];
  /** 湖泊分辨率 */
  private resolution: number;
  /** 湖床高度变化 */
  private bedHeightVariation: number;
  /** 是否生成湖岸 */
  private generateShore: boolean;
  /** 湖岸高度 */
  private shoreHeight: number;
  /** 湖岸宽度 */
  private shoreWidth: number;
  /** 湖泊几何体 */
  private lakeGeometry: THREE.BufferGeometry | null;
  /** 湖岸几何体 */
  private shoreGeometry: THREE.BufferGeometry | null;
  /** 湖床几何体 */
  private bedGeometry: THREE.BufferGeometry | null;

  /**
   * 构造函数
   * @param entity 实体
   * @param config 配置
   */
  constructor(entity: Entity, config: LakeWaterConfig = {}) {
    // 设置默认配置
    const defaultConfig: LakeWaterConfig = {
      ...config,
      type: WaterBodyType.LAKE,
      shape: WaterBodyShape.CUSTOM,
      enableFlow: false
    };

    // 调用父类构造函数
    super(entity, defaultConfig);

    // 设置湖泊特定属性
    this.shapeType = config.shapeType || LakeShapeType.CIRCLE;
    this.width = config.width || 50;
    this.length = config.length || 50;
    this.depth = config.depth || 10;
    this.edgePoints = config.edgePoints || [];
    this.resolution = config.resolution || 32;
    this.bedHeightVariation = config.bedHeightVariation || 1.0;
    this.generateShore = config.generateShore !== undefined ? config.generateShore : true;
    this.shoreHeight = config.shoreHeight || 1.0;
    this.shoreWidth = config.shoreWidth || 5.0;
    this.lakeGeometry = null;
    this.shoreGeometry = null;
    this.bedGeometry = null;

    // 如果是不规则形状或自定义多边形，但没有提供边缘点，则创建默认边缘点
    if ((this.shapeType === LakeShapeType.IRREGULAR || this.shapeType === LakeShapeType.CUSTOM_POLYGON) && this.edgePoints.length === 0) {
      this.createDefaultEdgePoints();
    }
  }

  /**
   * 创建默认边缘点
   */
  private createDefaultEdgePoints(): void {
    const numPoints = 8;
    const radius = this.width / 2;

    for (let i = 0; i < numPoints; i++) {
      const angle = (i / numPoints) * Math.PI * 2;
      // 添加一些随机性，使湖泊形状不那么规则
      const r = radius * (0.8 + Math.random() * 0.4);
      const x = Math.cos(angle) * r;
      const y = Math.sin(angle) * r;
      this.edgePoints.push(new THREE.Vector2(x, y));
    }
  }

  /**
   * 初始化组件
   */
  public override initialize(): void {
    if (this.initialized) {
      return;
    }

    // 创建湖泊几何体
    this.createLakeGeometry();

    // 如果需要生成湖岸，创建湖岸几何体
    if (this.generateShore) {
      this.createShoreGeometry();
    }

    // 创建湖床几何体
    this.createBedGeometry();

    // 创建水体材质
    this.createWaterMaterial();

    // 初始化水面高度图
    this.initializeHeightMap();

    // 初始化水面法线图
    this.initializeNormalMap();

    // 初始化水流速度图
    this.initializeVelocityMap();

    // 如果启用粒子，初始化粒子系统
    if (this.isParticlesEnabled()) {
      this.initializeParticleSystem();
    }

    this.initialized = true;
    Debug.log('LakeWaterComponent', '湖泊水体组件初始化完成');
  }

  /**
   * 创建湖泊几何体
   */
  private createLakeGeometry(): void {
    // 根据湖泊形状类型创建不同的几何体
    switch (this.shapeType) {
      case LakeShapeType.CIRCLE:
        this.createCircleLakeGeometry();
        break;
      case LakeShapeType.ELLIPSE:
        this.createEllipseLakeGeometry();
        break;
      case LakeShapeType.IRREGULAR:
      case LakeShapeType.CUSTOM_POLYGON:
        this.createIrregularLakeGeometry();
        break;
      default:
        this.createCircleLakeGeometry();
        break;
    }
  }

  /**
   * 创建圆形湖泊几何体
   */
  private createCircleLakeGeometry(): void {
    const radius = this.width / 2;
    const geometry = new THREE.CircleGeometry(radius, this.resolution);

    // 将几何体中心移到原点
    geometry.translate(0, 0, 0);

    this.lakeGeometry = geometry;
  }

  /**
   * 创建椭圆形湖泊几何体
   */
  private createEllipseLakeGeometry(): void {
    const radiusX = this.width / 2;
    const radiusZ = this.length / 2;
    const geometry = new THREE.BufferGeometry();
    const vertices: number[] = [];
    const uvs: number[] = [];
    const indices: number[] = [];

    // 添加中心点
    vertices.push(0, 0, 0);
    uvs.push(0.5, 0.5);

    // 添加边缘点
    for (let i = 0; i <= this.resolution; i++) {
      const angle = (i / this.resolution) * Math.PI * 2;
      const x = Math.cos(angle) * radiusX;
      const z = Math.sin(angle) * radiusZ;

      vertices.push(x, 0, z);

      // 计算UV坐标
      const u = 0.5 + Math.cos(angle) * 0.5;
      const v = 0.5 + Math.sin(angle) * 0.5;
      uvs.push(u, v);

      // 添加三角形索引
      if (i < this.resolution) {
        indices.push(0, i + 1, i + 2);
      }
    }

    // 设置几何体属性
    geometry.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3));
    geometry.setAttribute('uv', new THREE.Float32BufferAttribute(uvs, 2));
    geometry.setIndex(indices);

    // 计算法线
    geometry.computeVertexNormals();

    this.lakeGeometry = geometry;
  }

  /**
   * 创建不规则湖泊几何体
   */
  private createIrregularLakeGeometry(): void {
    // 使用THREE.Shape创建不规则形状
    const shape = new THREE.Shape();

    // 如果有边缘点，使用它们创建形状
    if (this.edgePoints.length > 0) {
      // 移动到第一个点
      shape.moveTo(this.edgePoints[0].x, this.edgePoints[0].y);

      // 连接其余的点
      for (let i = 1; i < this.edgePoints.length; i++) {
        shape.lineTo(this.edgePoints[i].x, this.edgePoints[i].y);
      }

      // 闭合形状
      shape.closePath();
    } else {
      // 如果没有边缘点，创建一个简单的不规则形状
      const radius = this.width / 2;

      // 移动到第一个点
      shape.moveTo(radius, 0);

      // 创建不规则形状
      for (let i = 1; i <= this.resolution; i++) {
        const angle = (i / this.resolution) * Math.PI * 2;
        // 添加一些随机性，使湖泊形状不那么规则
        const r = radius * (0.8 + Math.random() * 0.4);
        const x = Math.cos(angle) * r;
        const y = Math.sin(angle) * r;
        shape.lineTo(x, y);
      }
    }

    // 创建几何体
    const geometry = new THREE.ShapeGeometry(shape, this.resolution);

    // 将几何体旋转为水平面
    geometry.rotateX(-Math.PI / 2);

    this.lakeGeometry = geometry;
  }

  /**
   * 创建湖岸几何体
   */
  private createShoreGeometry(): void {
    if (!this.lakeGeometry) {
      return;
    }

    // 创建湖岸几何体
    const shoreGeometry = new THREE.BufferGeometry();

    // 获取湖泊几何体的顶点
    const lakePositions = this.lakeGeometry.getAttribute('position').array;
    const lakeVertexCount = lakePositions.length / 3;

    const vertices: number[] = [];
    const uvs: number[] = [];
    const indices: number[] = [];

    // 遍历湖泊边缘顶点
    for (let i = 0; i < lakeVertexCount; i++) {
      const x = lakePositions[i * 3];
      const y = lakePositions[i * 3 + 1];
      const z = lakePositions[i * 3 + 2];

      // 计算顶点到中心的方向
      const direction = new THREE.Vector3(x, 0, z).normalize();

      // 计算湖岸外侧点
      const outerX = x + direction.x * this.shoreWidth;
      const outerZ = z + direction.z * this.shoreWidth;

      // 添加湖泊边缘点
      vertices.push(x, y, z);

      // 添加湖岸外侧点
      vertices.push(outerX, y + this.shoreHeight, outerZ);

      // 添加UV坐标
      uvs.push(0, i / lakeVertexCount);
      uvs.push(1, i / lakeVertexCount);

      // 添加索引（创建三角形）
      if (i < lakeVertexCount - 1) {
        const baseIndex = i * 2;
        indices.push(
          baseIndex, baseIndex + 1, baseIndex + 2,
          baseIndex + 1, baseIndex + 3, baseIndex + 2
        );
      }
    }

    // 闭合湖岸（连接最后一个点和第一个点）
    if (lakeVertexCount > 2) {
      const lastIndex = (lakeVertexCount - 1) * 2;
      indices.push(
        lastIndex, lastIndex + 1, 0,
        lastIndex + 1, 1, 0
      );
    }

    // 设置几何体属性
    shoreGeometry.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3));
    shoreGeometry.setAttribute('uv', new THREE.Float32BufferAttribute(uvs, 2));
    shoreGeometry.setIndex(indices);

    // 计算法线
    shoreGeometry.computeVertexNormals();

    this.shoreGeometry = shoreGeometry;
  }

  /**
   * 创建湖床几何体
   */
  private createBedGeometry(): void {
    if (!this.lakeGeometry) {
      return;
    }

    // 创建湖床几何体
    const bedGeometry = new THREE.BufferGeometry();

    // 获取湖泊几何体的顶点
    const lakePositions = this.lakeGeometry.getAttribute('position').array;
    const lakeVertexCount = lakePositions.length / 3;

    const vertices: number[] = [];
    const uvs: number[] = [];
    const indices: number[] = [];

    // 计算湖泊中心点
    let centerX = 0;
    let centerZ = 0;

    for (let i = 0; i < lakeVertexCount; i++) {
      centerX += lakePositions[i * 3];
      centerZ += lakePositions[i * 3 + 2];
    }

    centerX /= lakeVertexCount;
    centerZ /= lakeVertexCount;

    // 添加湖泊中心点（湖床最深处）
    vertices.push(centerX, -this.depth, centerZ);
    uvs.push(0.5, 0.5);

    // 遍历湖泊边缘顶点
    for (let i = 0; i < lakeVertexCount; i++) {
      const x = lakePositions[i * 3];
      const y = lakePositions[i * 3 + 1];
      const z = lakePositions[i * 3 + 2];

      // 计算到中心的距离
      const distanceToCenter = Math.sqrt(
        Math.pow(x - centerX, 2) +
        Math.pow(z - centerZ, 2)
      );

      // 计算湖床深度（边缘较浅，中心较深）
      const normalizedDistance = distanceToCenter / (this.width / 2);
      const bedDepth = this.depth * (1 - Math.pow(normalizedDistance, 2));

      // 添加湖床点
      vertices.push(x, -bedDepth, z);

      // 添加UV坐标
      const angle = Math.atan2(z - centerZ, x - centerX);
      const u = 0.5 + Math.cos(angle) * 0.5 * normalizedDistance;
      const v = 0.5 + Math.sin(angle) * 0.5 * normalizedDistance;
      uvs.push(u, v);

      // 添加索引（创建三角形）
      if (i < lakeVertexCount - 1) {
        indices.push(0, i + 1, i + 2);
      }
    }

    // 闭合湖床（连接最后一个点和第一个点）
    if (lakeVertexCount > 2) {
      indices.push(0, lakeVertexCount, 1);
    }

    // 设置几何体属性
    bedGeometry.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3));
    bedGeometry.setAttribute('uv', new THREE.Float32BufferAttribute(uvs, 2));
    bedGeometry.setIndex(indices);

    // 计算法线
    bedGeometry.computeVertexNormals();

    this.bedGeometry = bedGeometry;
  }

  /**
   * 更新组件
   * @param deltaTime 帧间隔时间（秒）
   */
  public override update(deltaTime: number): void {
    super.update(deltaTime);

    // 更新湖泊波动效果
    this.updateLakeWaves(deltaTime);
  }

  /**
   * 更新湖泊波动效果
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateLakeWaves(deltaTime: number): void {
    // 获取水体网格
    const mesh = this.getMesh();
    if (!mesh) {
      return;
    }

    // 获取水体材质
    const material = mesh.material as THREE.MeshStandardMaterial;
    if (!material) {
      return;
    }

    // 如果有法线贴图，更新法线贴图的偏移，模拟波动效果
    if (material.normalMap) {
      // 使用不同的偏移方向，模拟多方向波动
      material.normalMap.offset.x += deltaTime * 0.03;
      material.normalMap.offset.y += deltaTime * 0.02;
    }
  }

  /**
   * 设置湖泊形状类型
   * @param shapeType 形状类型
   */
  public setShapeType(shapeType: LakeShapeType): void {
    this.shapeType = shapeType;

    // 如果是不规则形状或自定义多边形，但没有提供边缘点，则创建默认边缘点
    if ((this.shapeType === LakeShapeType.IRREGULAR || this.shapeType === LakeShapeType.CUSTOM_POLYGON) && this.edgePoints.length === 0) {
      this.createDefaultEdgePoints();
    }

    // 重新创建湖泊几何体
    this.createLakeGeometry();

    if (this.generateShore) {
      this.createShoreGeometry();
    }

    this.createBedGeometry();

    // 标记需要更新
    this.setNeedsUpdate(true);
  }

  /**
   * 设置湖泊尺寸
   * @param width 宽度
   * @param length 长度
   */
  public setSize(width: number, length?: number): void {
    this.width = width;
    if (length !== undefined) {
      this.length = length;
    }

    // 重新创建湖泊几何体
    this.createLakeGeometry();

    if (this.generateShore) {
      this.createShoreGeometry();
    }

    this.createBedGeometry();

    // 标记需要更新
    this.setNeedsUpdate(true);
  }

  /**
   * 设置湖泊深度
   * @param depth 深度
   */
  public setDepth(depth: number): void {
    this.depth = depth;

    // 重新创建湖床几何体
    this.createBedGeometry();

    // 标记需要更新
    this.setNeedsUpdate(true);
  }

  /**
   * 设置湖泊边缘点
   * @param points 边缘点
   */
  public setEdgePoints(points: THREE.Vector2[]): void {
    this.edgePoints = points;

    // 重新创建湖泊几何体
    this.createLakeGeometry();

    if (this.generateShore) {
      this.createShoreGeometry();
    }

    this.createBedGeometry();

    // 标记需要更新
    this.setNeedsUpdate(true);
  }

  /**
   * 设置湖岸参数
   * @param generateShore 是否生成湖岸
   * @param shoreHeight 湖岸高度
   * @param shoreWidth 湖岸宽度
   */
  public setShoreParameters(generateShore: boolean, shoreHeight?: number, shoreWidth?: number): void {
    this.generateShore = generateShore;

    if (shoreHeight !== undefined) {
      this.shoreHeight = shoreHeight;
    }

    if (shoreWidth !== undefined) {
      this.shoreWidth = shoreWidth;
    }

    // 如果需要生成湖岸，创建湖岸几何体
    if (this.generateShore) {
      this.createShoreGeometry();
    }

    // 标记需要更新
    this.setNeedsUpdate(true);
  }

  /**
   * 设置湖床高度变化
   * @param variation 高度变化
   */
  public setBedHeightVariation(variation: number): void {
    this.bedHeightVariation = variation;

    // 重新创建湖床几何体
    this.createBedGeometry();

    // 标记需要更新
    this.setNeedsUpdate(true);
  }
}