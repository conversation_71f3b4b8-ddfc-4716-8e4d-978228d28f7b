# 协作编辑

IR引擎编辑器的协作编辑功能允许多个用户同时编辑同一个场景或项目，实现实时协作开发。本文档将详细介绍如何使用协作编辑功能。

## 协作编辑概述

### 主要功能

- **多人同时编辑**：多个用户可以同时编辑同一个场景
- **实时操作同步**：所有用户的操作实时同步到其他用户
- **用户状态显示**：显示其他用户的在线状态和编辑位置
- **冲突检测和解决**：自动检测和解决编辑冲突
- **操作历史记录**：记录所有用户的编辑操作
- **权限管理**：控制不同用户的编辑权限
- **实时聊天**：用户之间的实时文字交流

### 工作原理

IR引擎的协作编辑基于WebSocket技术，通过以下步骤实现：

1. 创建或加入协作会话
2. 建立与协作服务器的WebSocket连接
3. 实时广播和接收操作消息
4. 自动合并和解决冲突
5. 同步场景状态和用户信息

## 开始协作编辑

### 创建协作会话

1. 打开IR引擎编辑器
2. 打开要协作编辑的项目
3. 点击顶部菜单栏的"文件 > 协作 > 创建协作会话"
4. 在弹出的对话框中，设置以下选项：
   - 会话名称
   - 权限设置（公开/私有）
   - 用户权限默认值
   - 冲突解决策略
5. 点击"创建"按钮
6. 系统会生成一个会话ID，您可以将此ID分享给其他用户

![创建协作会话](../../assets/images/create-collaboration-session.png)

### 加入协作会话

1. 打开IR引擎编辑器
2. 点击顶部菜单栏的"文件 > 协作 > 加入协作会话"
3. 在弹出的对话框中，输入会话ID
4. 点击"加入"按钮
5. 系统会连接到协作会话并同步当前场景

![加入协作会话](../../assets/images/join-collaboration-session.png)

### 邀请用户

1. 在协作会话中，点击协作面板中的"邀请"按钮
2. 选择邀请方式：
   - 复制邀请链接
   - 通过邮件邀请
   - 生成二维码
3. 设置被邀请用户的初始权限
4. 发送邀请

## 协作界面

### 协作面板

协作面板是管理协作会话的主要界面，包含以下标签页：

![协作面板](../../assets/images/collaboration-panel.png)

#### 用户标签页

显示当前在线用户列表和状态：

- 用户名和头像
- 在线状态（在线/离线/忙碌）
- 当前编辑位置
- 权限级别
- 用户操作菜单

#### 聊天标签页

提供实时文字聊天功能：

- 发送文本消息
- 发送表情
- 发送截图
- 提及用户（@用户名）
- 查看聊天历史

#### 历史标签页

记录所有用户的编辑操作：

- 操作类型和时间
- 操作用户
- 受影响的对象
- 操作详情
- 回滚选项

#### 权限标签页

管理用户权限（仅对所有者和管理员可见）：

- 用户权限设置
- 角色管理
- 权限模板
- 批量权限设置

### 用户状态指示

在协作模式下，您可以看到其他用户的状态和位置：

- **光标位置**：显示其他用户的鼠标位置
- **选中对象**：高亮显示其他用户当前选中的对象
- **编辑区域**：显示其他用户正在编辑的区域
- **用户颜色**：每个用户分配一个唯一的颜色，用于标识

![用户状态指示](../../assets/images/user-indicators.png)

## 实时协作编辑

### 基本编辑操作

在协作模式下，基本编辑操作与单人模式相同：

1. 选择对象：点击场景中的对象
2. 移动对象：使用移动工具拖动对象
3. 旋转对象：使用旋转工具旋转对象
4. 缩放对象：使用缩放工具调整对象大小
5. 修改属性：在属性面板中修改对象属性

所有这些操作都会实时同步给其他用户。

### 锁定对象

为了避免冲突，您可以锁定正在编辑的对象：

1. 选择对象
2. 右键点击并选择"锁定"或按下Ctrl+L
3. 锁定后，其他用户将无法编辑该对象，直到您解锁或离开会话
4. 要解锁对象，右键点击并选择"解锁"或按下Ctrl+Shift+L

![锁定对象](../../assets/images/lock-object.png)

### 编辑区域指示

当您开始编辑某个区域时，系统会自动向其他用户显示您的编辑区域：

- 场景对象编辑：高亮显示正在编辑的对象
- 材质编辑：在材质面板中标记正在编辑的材质
- 动画编辑：在时间轴上标记正在编辑的动画片段

这有助于其他用户避免同时编辑同一区域，减少冲突。

### 查看其他用户的编辑

您可以查看其他用户正在进行的编辑：

1. 在用户列表中，将鼠标悬停在用户名上
2. 系统会显示该用户当前的编辑位置和选中的对象
3. 点击"跟随"按钮，您的视图将自动跟随该用户的视角

## 聊天和交流

### 发送消息

1. 切换到聊天标签页
2. 在输入框中输入消息
3. 按回车键或点击发送按钮

### 提及用户

您可以在消息中提及特定用户：

1. 在消息中输入@符号
2. 从弹出的用户列表中选择要提及的用户
3. 被提及的用户会收到通知

### 发送截图

1. 在聊天输入框旁边点击截图按钮
2. 系统会捕获当前视图的截图
3. 添加可选的标注
4. 点击发送按钮

### 添加注释

您可以在场景中添加注释，以便与其他用户交流：

1. 选择对象或场景位置
2. 右键点击并选择"添加注释"
3. 输入注释内容
4. 点击"保存"按钮

注释会显示在场景中，其他用户可以查看和回复。

![添加注释](../../assets/images/add-comment.png)

## 冲突检测和解决

### 冲突类型

在协作编辑中，可能会出现以下类型的冲突：

- **属性冲突**：多个用户同时修改同一对象的同一属性
- **结构冲突**：多个用户同时修改场景层次结构
- **删除冲突**：一个用户正在编辑某个对象，而另一个用户删除了该对象
- **资源冲突**：多个用户同时修改同一资源文件

### 自动冲突解决

IR引擎编辑器会尝试自动解决某些类型的冲突：

- **非重叠编辑**：如果多个用户编辑同一对象的不同属性，系统会自动合并这些更改
- **顺序编辑**：如果编辑操作可以按顺序应用，系统会自动排序这些操作
- **简单合并**：对于简单的文本或数值更改，系统会尝试自动合并

### 手动冲突解决

当系统无法自动解决冲突时，会显示冲突解决对话框：

1. 查看冲突详情，包括冲突类型、涉及的用户和对象
2. 选择解决方案：
   - 保留您的更改
   - 接受他人的更改
   - 手动合并更改
3. 点击"应用"按钮

![冲突解决](../../assets/images/conflict-resolution.png)

### 冲突预防

为了减少冲突，您可以采取以下措施：

- 使用锁定功能锁定正在编辑的对象
- 在聊天中通知其他用户您的编辑计划
- 将大型场景分割为多个子场景，不同用户编辑不同子场景
- 使用协作区域功能，划分不同用户的编辑区域

## 权限管理

### 权限级别

IR引擎编辑器提供以下权限级别：

- **所有者**：完全控制权限，可以管理用户和权限
- **管理员**：可以编辑场景、管理用户，但不能管理权限
- **编辑者**：可以编辑场景，但不能管理用户和权限
- **查看者**：只能查看场景，不能进行任何编辑操作

### 设置用户权限

1. 在协作面板中，切换到"权限"标签页
2. 找到目标用户
3. 点击权限下拉菜单
4. 选择新的权限级别
5. 点击"应用"按钮

![设置用户权限](../../assets/images/set-user-permissions.png)

### 自定义权限

除了预定义的权限级别外，您还可以创建自定义权限：

1. 在权限标签页中，点击"自定义权限"按钮
2. 创建新的权限模板
3. 设置各种操作的权限（读取、写入、删除等）
4. 保存权限模板
5. 将自定义权限分配给用户

### 实体级权限

您可以为特定实体设置权限，实现更细粒度的控制：

1. 选择目标实体
2. 右键点击并选择"权限"
3. 在权限对话框中，设置用户或角色的权限
4. 点击"保存"按钮

## 协作会话管理

### 会话设置

1. 在协作面板中，点击设置图标
2. 在会话设置对话框中，可以修改以下选项：
   - 会话名称
   - 权限设置
   - 冲突解决策略
   - 自动保存间隔
   - 历史记录保留时间

### 保存协作历史

1. 在协作面板的历史标签页中，点击"导出历史"按钮
2. 选择导出格式（JSON、CSV等）
3. 选择导出范围（时间段、用户、操作类型等）
4. 点击"导出"按钮

### 结束协作会话

1. 如果您是会话所有者，点击协作面板中的"结束会话"按钮
2. 确认结束会话
3. 系统会通知所有用户会话已结束
4. 所有用户将断开连接，但可以选择保存当前场景的本地副本

### 离开协作会话

1. 点击协作面板中的"离开会话"按钮
2. 或点击顶部菜单栏的"文件 > 协作 > 断开协作"
3. 确认离开会话
4. 您将断开与协作服务器的连接，但可以选择保存当前场景的本地副本

## 最佳实践

### 协作工作流

1. **规划**：在开始协作前，规划任务分工和工作区域
2. **沟通**：使用聊天功能保持沟通，通知重要更改
3. **分区**：将场景分为不同区域，分配给不同用户
4. **锁定**：编辑重要对象前先锁定
5. **同步**：定期同步进度和解决冲突
6. **保存**：定期保存项目和导出协作历史

### 大型团队协作

对于大型团队（10人以上），建议采取以下策略：

1. 指定协作协调员，负责权限管理和冲突解决
2. 将场景分解为多个子场景，不同团队负责不同子场景
3. 建立清晰的资产命名和组织规范
4. 使用标签和注释系统标记工作状态
5. 定期举行同步会议，协调工作进度

### 性能优化

协作编辑可能会增加网络和系统负担，以下是一些优化建议：

1. 使用有线网络连接，避免不稳定的Wi-Fi
2. 关闭不必要的实时同步选项（如光标位置）
3. 减少场景复杂度，使用LOD和实例化
4. 优化资产大小，减少同步数据量
5. 在高延迟环境下，增加本地缓存和预测功能

## 故障排除

### 连接问题

如果无法连接到协作会话：

1. 检查网络连接
2. 确认会话ID是否正确
3. 检查防火墙设置
4. 尝试重新启动编辑器
5. 联系会话所有者确认会话是否仍然活跃

### 同步问题

如果场景同步出现问题：

1. 点击协作面板中的"重新同步"按钮
2. 检查网络连接质量
3. 暂时离开会话，然后重新加入
4. 如果问题持续，尝试导出当前场景，然后重新加入会话

### 冲突解决失败

如果冲突无法解决：

1. 尝试手动解决冲突
2. 与涉及冲突的用户沟通
3. 考虑回滚到冲突前的版本
4. 在极端情况下，可以导出场景的本地副本，然后重新加入会话

## 下一步

现在您已经了解了协作编辑的基本功能，可以继续学习其他相关功能：

- [版本控制](../best-practices/version-control.md)
- [协作工作流最佳实践](../best-practices/collaboration.md)
- [大型项目管理](../best-practices/large-projects.md)
