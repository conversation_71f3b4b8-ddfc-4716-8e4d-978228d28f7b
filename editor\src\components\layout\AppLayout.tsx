/**
 * 应用布局组件
 */
import React, { useState, useEffect } from 'react';
import { Layout, Menu, Button, Dropdown, Avatar, Badge, Tooltip } from 'antd';
import {
  MenuUnfoldOutlined,
  MenuFoldOutlined,
  UserOutlined,
  SettingOutlined,
  LogoutOutlined,
  BellOutlined,
  TranslationOutlined,
  GithubOutlined,
  QuestionCircleOutlined,
  CommentOutlined,
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useAppDispatch, useAppSelector } from '../../store';
import { logout } from '../../store/auth/authSlice';
import { toggleSidebar } from '../../store/ui/uiSlice';

const { Header, Sider, Content } = Layout;

interface AppLayoutProps {
  children: React.ReactNode;
}

export const AppLayout: React.FC<AppLayoutProps> = ({ children }) => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();

  const { user } = useAppSelector((state) => state.auth);
  const { sidebarCollapsed } = useAppSelector((state) => state.ui);

  const handleLogout = () => {
    dispatch(logout());
    navigate('/login');
  };

  const handleToggleSidebar = () => {
    dispatch(toggleSidebar());
  };

  const handleChangeLanguage = (lang: string) => {
    i18n.changeLanguage(lang);
  };

  const userMenu = (
    <Menu>
      <Menu.Item key="profile" icon={<UserOutlined />} onClick={() => navigate('/profile')}>
        {t('common.profile')}
      </Menu.Item>
      <Menu.Item key="settings" icon={<SettingOutlined />} onClick={() => navigate('/settings')}>
        {t('common.settings')}
      </Menu.Item>
      <Menu.Divider />
      <Menu.Item key="logout" icon={<LogoutOutlined />} onClick={handleLogout}>
        {t('common.logout')}
      </Menu.Item>
    </Menu>
  );

  const languageMenu = (
    <Menu>
      <Menu.Item key="zh-CN" onClick={() => handleChangeLanguage('zh-CN')}>
        中文
      </Menu.Item>
      <Menu.Item key="en-US" onClick={() => handleChangeLanguage('en-US')}>
        English
      </Menu.Item>
    </Menu>
  );

  const notificationMenu = (
    <Menu>
      <Menu.Item key="empty">{t('common.noNotifications')}</Menu.Item>
    </Menu>
  );

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider trigger={null} collapsible collapsed={sidebarCollapsed} width={240}>
        <div className="logo" style={{ height: 64, padding: 16, textAlign: 'center' }}>
          <h1 style={{ color: '#fff', margin: 0, fontSize: sidebarCollapsed ? 16 : 20 }}>
            {sidebarCollapsed ? 'IR' : 'IR引擎编辑器'}
          </h1>
        </div>
        <Menu theme="dark" mode="inline" defaultSelectedKeys={['1']}>
          <Menu.Item key="1" onClick={() => navigate('/projects')}>
            {t('common.projects')}
          </Menu.Item>
          <Menu.Item key="2" onClick={() => navigate('/editor')}>
            {t('common.editor')}
          </Menu.Item>
          <Menu.Item key="3" onClick={() => navigate('/terrain-editor')}>
            {t('editor.terrain.terrainEditor')}
          </Menu.Item>
          <Menu.Item key="4" onClick={() => navigate('/settings')}>
            {t('common.settings')}
          </Menu.Item>
          <Menu.Item key="5" onClick={() => navigate('/help')}>
            {t('common.help')}
          </Menu.Item>
          <Menu.Item key="5" icon={<CommentOutlined />} onClick={() => navigate('/feedback-demo')}>
            反馈系统演示
          </Menu.Item>
        </Menu>
      </Sider>
      <Layout>
        <Header style={{ padding: 0, background: '#fff', display: 'flex', justifyContent: 'space-between' }}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Button
              type="text"
              icon={sidebarCollapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={handleToggleSidebar}
              style={{ fontSize: 16, width: 64, height: 64 }}
            />
          </div>
          <div style={{ display: 'flex', alignItems: 'center', marginRight: 16 }}>
            <Tooltip title={t('common.help')}>
              <Button type="text" icon={<QuestionCircleOutlined />} style={{ marginRight: 8 }} />
            </Tooltip>
            <Tooltip title={t('common.github')}>
              <Button
                type="text"
                icon={<GithubOutlined />}
                style={{ marginRight: 8 }}
                onClick={() => window.open('https://github.com/your-username/ir-engine', '_blank')}
              />
            </Tooltip>
            <Dropdown overlay={languageMenu} placement="bottomRight">
              <Button type="text" icon={<TranslationOutlined />} style={{ marginRight: 8 }} />
            </Dropdown>
            <Dropdown overlay={notificationMenu} placement="bottomRight">
              <Badge count={0} overflowCount={99}>
                <Button type="text" icon={<BellOutlined />} style={{ marginRight: 16 }} />
              </Badge>
            </Dropdown>
            <Dropdown overlay={userMenu} placement="bottomRight">
              <div style={{ cursor: 'pointer', display: 'flex', alignItems: 'center' }}>
                <Avatar icon={<UserOutlined />} src={user?.avatar} />
                <span style={{ marginLeft: 8 }}>{user?.username || t('common.guest')}</span>
              </div>
            </Dropdown>
          </div>
        </Header>
        <Content style={{ margin: '24px 16px', padding: 24, background: '#fff', minHeight: 280 }}>
          {children}
        </Content>
      </Layout>
    </Layout>
  );
};
