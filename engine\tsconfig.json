{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "module": "ESNext", "lib": ["ES2020", "DOM", "DOM.Iterable"], "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "strict": true, "noUnusedLocals": false, "noUnusedParameters": false, "noFallthroughCasesInSwitch": true, "downlevelIteration": true, "declaration": true, "declarationDir": "./dist/types", "outDir": "./dist", "baseUrl": ".", "paths": {"@/*": ["src/*"]}}, "include": ["src"], "exclude": ["node_modules", "dist", "tests"]}