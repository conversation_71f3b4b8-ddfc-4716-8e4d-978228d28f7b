/**
 * 动画重定向器
 * 用于将一个骨骼结构的动画应用到另一个骨骼结构上
 */
import * as THREE from 'three';
import { AnimationClip } from './AnimationClip';
import { Entity } from '../core/Entity';
import { EventEmitter } from '../utils/EventEmitter';

/**
 * 骨骼映射
 */
export interface BoneMapping {
  /** 源骨骼名称 */
  source: string;
  /** 目标骨骼名称 */
  target: string;
  /** 旋转偏移 */
  rotationOffset?: THREE.Quaternion;
  /** 位置缩放 */
  positionScale?: number;
  /** 是否镜像 */
  mirror?: boolean;
}

/**
 * 重定向配置
 */
export interface RetargetConfig {
  /** 骨骼映射 */
  boneMapping: BoneMapping[];
  /** 是否保留位置轨道 */
  preservePositionTracks?: boolean;
  /** 是否保留缩放轨道 */
  preserveScaleTracks?: boolean;
  /** 是否规范化旋转 */
  normalizeRotations?: boolean;
  /** 是否调整根骨骼高度 */
  adjustRootHeight?: boolean;
  /** 是否调整骨骼长度 */
  adjustBoneLength?: boolean;
  /** 是否使用四元数球面线性插值 */
  useQuaternionSlerp?: boolean;
  /** 是否自动创建骨骼映射 */
  autoCreateMapping?: boolean;
  /** 是否忽略未映射的骨骼 */
  ignoreUnmappedBones?: boolean;
}

/**
 * 重定向事件类型
 */
export enum RetargetEventType {
  /** 重定向开始 */
  RETARGET_START = 'retargetStart',
  /** 重定向完成 */
  RETARGET_COMPLETE = 'retargetComplete',
  /** 重定向错误 */
  RETARGET_ERROR = 'retargetError'
}

/**
 * 动画重定向器
 */
export class AnimationRetargeter {
  /** 源骨骼 */
  private sourceSkeleton: THREE.Skeleton | THREE.Bone[];
  /** 目标骨骼 */
  private targetSkeleton: THREE.Skeleton | THREE.Bone[];
  /** 重定向配置 */
  private config: RetargetConfig;
  /** 骨骼映射缓存 */
  private boneMappingCache: Map<string, string> = new Map();
  /** 骨骼索引映射缓存 */
  private boneIndexMappingCache: Map<number, number> = new Map();
  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();
  /** 是否已初始化 */
  private initialized: boolean = false;

  /**
   * 构造函数
   * @param sourceSkeleton 源骨骼
   * @param targetSkeleton 目标骨骼
   * @param config 重定向配置
   */
  constructor(
    sourceSkeleton: THREE.Skeleton | THREE.Bone[],
    targetSkeleton: THREE.Skeleton | THREE.Bone[],
    config: RetargetConfig
  ) {
    this.sourceSkeleton = sourceSkeleton;
    this.targetSkeleton = targetSkeleton;
    this.config = {
      boneMapping: [...config.boneMapping],
      preservePositionTracks: config.preservePositionTracks !== undefined ? config.preservePositionTracks : true,
      preserveScaleTracks: config.preserveScaleTracks !== undefined ? config.preserveScaleTracks : false,
      normalizeRotations: config.normalizeRotations !== undefined ? config.normalizeRotations : true,
      adjustRootHeight: config.adjustRootHeight !== undefined ? config.adjustRootHeight : true,
      adjustBoneLength: config.adjustBoneLength !== undefined ? config.adjustBoneLength : true,
      useQuaternionSlerp: config.useQuaternionSlerp !== undefined ? config.useQuaternionSlerp : true,
      autoCreateMapping: config.autoCreateMapping !== undefined ? config.autoCreateMapping : false,
      ignoreUnmappedBones: config.ignoreUnmappedBones !== undefined ? config.ignoreUnmappedBones : true
    };

    // 初始化
    this.initialize();
  }

  /**
   * 初始化
   */
  private initialize(): void {
    // 如果已初始化，则不再初始化
    if (this.initialized) return;

    // 创建骨骼映射缓存
    this.createBoneMappingCache();

    // 如果启用自动创建映射，则尝试自动创建
    if (this.config.autoCreateMapping) {
      this.autoCreateBoneMapping();
    }

    // 标记为已初始化
    this.initialized = true;
  }

  /**
   * 创建骨骼映射缓存
   */
  private createBoneMappingCache(): void {
    // 清空缓存
    this.boneMappingCache.clear();
    this.boneIndexMappingCache.clear();

    // 获取骨骼数组
    const sourceBones = this.getSourceBones();
    const targetBones = this.getTargetBones();

    // 创建骨骼名称映射
    for (const mapping of this.config.boneMapping) {
      this.boneMappingCache.set(mapping.source, mapping.target);
    }

    // 创建骨骼索引映射
    for (let i = 0; i < sourceBones.length; i++) {
      const sourceBone = sourceBones[i];
      const targetBoneName = this.boneMappingCache.get(sourceBone.name);

      if (targetBoneName) {
        // 查找目标骨骼索引
        const targetIndex = targetBones.findIndex(bone => bone.name === targetBoneName);
        if (targetIndex !== -1) {
          this.boneIndexMappingCache.set(i, targetIndex);
        }
      }
    }
  }

  /**
   * 自动创建骨骼映射
   */
  private autoCreateBoneMapping(): void {
    // 获取骨骼数组
    const sourceBones = this.getSourceBones();
    const targetBones = this.getTargetBones();

    // 尝试匹配相同名称的骨骼
    for (const sourceBone of sourceBones) {
      // 如果已经有映射，则跳过
      if (this.boneMappingCache.has(sourceBone.name)) continue;

      // 查找相同名称的目标骨骼
      const targetBone = targetBones.find(bone => bone.name === sourceBone.name);
      if (targetBone) {
        // 添加映射
        this.config.boneMapping.push({
          source: sourceBone.name,
          target: targetBone.name
        });
        this.boneMappingCache.set(sourceBone.name, targetBone.name);
      }
    }

    // 更新骨骼索引映射
    this.createBoneMappingCache();
  }

  /**
   * 获取源骨骼数组
   * @returns 骨骼数组
   */
  private getSourceBones(): THREE.Bone[] {
    if (Array.isArray(this.sourceSkeleton)) {
      return this.sourceSkeleton;
    } else {
      return this.sourceSkeleton.bones;
    }
  }

  /**
   * 获取目标骨骼数组
   * @returns 骨骼数组
   */
  private getTargetBones(): THREE.Bone[] {
    if (Array.isArray(this.targetSkeleton)) {
      return this.targetSkeleton;
    } else {
      return this.targetSkeleton.bones;
    }
  }

  /**
   * 重定向动画片段
   * @param clip 动画片段
   * @returns 重定向后的动画片段
   */
  public retarget(clip: THREE.AnimationClip): THREE.AnimationClip {
    // 发出重定向开始事件
    this.eventEmitter.emit(RetargetEventType.RETARGET_START, {
      clip: clip.name
    });

    try {
      // 创建新轨道
      const newTracks: THREE.KeyframeTrack[] = [];

      // 处理每个轨道
      for (const track of clip.tracks) {
        // 解析轨道名称
        const trackSplits = track.name.split('.');
        const boneName = trackSplits[0];
        const property = trackSplits[1];

        // 获取目标骨骼名称
        const targetBoneName = this.boneMappingCache.get(boneName);
        if (!targetBoneName && this.config.ignoreUnmappedBones) {
          continue;
        }

        // 创建新轨道
        let newTrack: THREE.KeyframeTrack = null;

        if (property === 'quaternion') {
          // 处理旋转轨道
          newTrack = this.retargetRotationTrack(track as THREE.QuaternionKeyframeTrack, boneName, targetBoneName);
        } else if (property === 'position' && this.config.preservePositionTracks) {
          // 处理位置轨道
          newTrack = this.retargetPositionTrack(track as THREE.VectorKeyframeTrack, boneName, targetBoneName);
        } else if (property === 'scale' && this.config.preserveScaleTracks) {
          // 处理缩放轨道
          newTrack = this.retargetScaleTrack(track as THREE.VectorKeyframeTrack, boneName, targetBoneName);
        }

        // 添加新轨道
        if (newTrack) {
          newTracks.push(newTrack);
        }
      }

      // 创建新的动画片段
      const newClip = new THREE.AnimationClip(
        clip.name,
        clip.duration,
        newTracks,
        clip.blendMode
      );

      // 发出重定向完成事件
      this.eventEmitter.emit(RetargetEventType.RETARGET_COMPLETE, {
        clip: clip.name,
        newClip: newClip.name
      });

      return newClip;
    } catch (error) {
      // 发出重定向错误事件
      this.eventEmitter.emit(RetargetEventType.RETARGET_ERROR, {
        clip: clip.name,
        error
      });

      throw error;
    }
  }

  /**
   * 重定向旋转轨道
   * @param track 旋转轨道
   * @param sourceBoneName 源骨骼名称
   * @param targetBoneName 目标骨骼名称
   * @returns 重定向后的旋转轨道
   */
  private retargetRotationTrack(
    track: THREE.QuaternionKeyframeTrack,
    sourceBoneName: string,
    targetBoneName: string
  ): THREE.QuaternionKeyframeTrack {
    // 复制时间和值
    const times = track.times.slice();
    const values = track.values.slice();

    // 获取骨骼映射
    const mapping = this.config.boneMapping.find(m => m.source === sourceBoneName);
    if (!mapping) return null;

    // 如果有旋转偏移，则应用旋转偏移
    if (mapping.rotationOffset) {
      const rotationOffset = mapping.rotationOffset.clone();

      // 处理每一帧
      for (let i = 0; i < track.times.length; i++) {
        const quaternion = new THREE.Quaternion();
        quaternion.fromArray(track.values, i * 4);

        // 应用旋转偏移
        if (mapping.mirror) {
          // 镜像旋转
          quaternion.y *= -1;
          quaternion.z *= -1;
        }

        // 应用旋转偏移
        quaternion.premultiply(rotationOffset);

        // 规范化旋转
        if (this.config.normalizeRotations) {
          quaternion.normalize();
        }

        // 存储结果
        quaternion.toArray(values, i * 4);
      }
    }

    // 创建新轨道
    return new THREE.QuaternionKeyframeTrack(
      `${targetBoneName}.quaternion`,
      times,
      values
    );
  }

  /**
   * 重定向位置轨道
   * @param track 位置轨道
   * @param sourceBoneName 源骨骼名称
   * @param targetBoneName 目标骨骼名称
   * @returns 重定向后的位置轨道
   */
  private retargetPositionTrack(
    track: THREE.VectorKeyframeTrack,
    sourceBoneName: string,
    targetBoneName: string
  ): THREE.VectorKeyframeTrack {
    // 复制时间和值
    const times = track.times.slice();
    const values = track.values.slice();

    // 获取骨骼映射
    const mapping = this.config.boneMapping.find(m => m.source === sourceBoneName);
    if (!mapping) return null;

    // 获取骨骼
    const sourceBones = this.getSourceBones();
    const targetBones = this.getTargetBones();
    const sourceBone = sourceBones.find(bone => bone.name === sourceBoneName);
    const targetBone = targetBones.find(bone => bone.name === targetBoneName);

    if (!sourceBone || !targetBone) return null;

    // 计算缩放因子
    let scale = 1.0;
    if (this.config.adjustBoneLength && mapping.positionScale === undefined) {
      // 计算骨骼长度比例
      const sourceLength = sourceBone.position.length();
      const targetLength = targetBone.position.length();
      if (sourceLength > 0 && targetLength > 0) {
        scale = targetLength / sourceLength;
      }
    } else if (mapping.positionScale !== undefined) {
      scale = mapping.positionScale;
    }

    // 处理每一帧
    for (let i = 0; i < track.times.length; i++) {
      const position = new THREE.Vector3();
      position.fromArray(track.values, i * 3);

      // 应用缩放
      position.multiplyScalar(scale);

      // 如果是镜像，则翻转X轴
      if (mapping.mirror) {
        position.x *= -1;
      }

      // 如果是根骨骼且需要调整高度
      if (sourceBoneName === 'Hips' && this.config.adjustRootHeight) {
        // 调整Y轴高度
        const sourceHeight = sourceBone.position.y;
        const targetHeight = targetBone.position.y;
        position.y = position.y - sourceHeight + targetHeight;
      }

      // 存储结果
      position.toArray(values, i * 3);
    }

    // 创建新轨道
    return new THREE.VectorKeyframeTrack(
      `${targetBoneName}.position`,
      times,
      values
    );
  }

  /**
   * 重定向缩放轨道
   * @param track 缩放轨道
   * @param sourceBoneName 源骨骼名称
   * @param targetBoneName 目标骨骼名称
   * @returns 重定向后的缩放轨道
   */
  private retargetScaleTrack(
    track: THREE.VectorKeyframeTrack,
    sourceBoneName: string,
    targetBoneName: string
  ): THREE.VectorKeyframeTrack {
    // 复制时间和值
    const times = track.times.slice();
    const values = track.values.slice();

    // 创建新轨道
    return new THREE.VectorKeyframeTrack(
      `${targetBoneName}.scale`,
      times,
      values
    );
  }

  /**
   * 添加事件监听器
   * @param type 事件类型
   * @param callback 回调函数
   */
  public addEventListener(type: RetargetEventType, callback: (data: any) => void): void {
    this.eventEmitter.on(type, callback);
  }

  /**
   * 移除事件监听器
   * @param type 事件类型
   * @param callback 回调函数
   */
  public removeEventListener(type: RetargetEventType, callback: (data: any) => void): void {
    this.eventEmitter.off(type, callback);
  }
}
