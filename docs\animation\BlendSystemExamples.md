# 动画混合系统示例

本文档提供了IR引擎动画混合系统的详细示例，展示了各种混合技术和应用场景。

## 目录

1. [基础混合](#基础混合)
2. [遮罩混合](#遮罩混合)
3. [混合空间](#混合空间)
4. [子片段混合](#子片段混合)
5. [高级混合模式](#高级混合模式)
6. [状态机集成](#状态机集成)
7. [物理集成](#物理集成)
8. [完整示例](#完整示例)

## 基础混合

### 简单过渡

在两个动画之间平滑过渡，如从站立到行走：

```typescript
// 创建动画控制器
const animator = new Animator(character);

// 加载动画片段
animator.addClip('idle', idleClip);
animator.addClip('walk', walkClip);

// 创建混合器
const blender = new AnimationBlender(animator);

// 添加混合层
const idleLayerIndex = blender.addLayer('idle', 1.0, BlendMode.OVERRIDE);
const walkLayerIndex = blender.addLayer('walk', 0.0, BlendMode.OVERRIDE);

// 从站立过渡到行走
function transitionToWalk() {
  blender.setLayerWeight(idleLayerIndex, 0.0, 1.0); // 1秒内将站立权重降为0
  blender.setLayerWeight(walkLayerIndex, 1.0, 1.0); // 1秒内将行走权重升为1
}

// 从行走过渡到站立
function transitionToIdle() {
  blender.setLayerWeight(idleLayerIndex, 1.0, 1.0); // 1秒内将站立权重升为1
  blender.setLayerWeight(walkLayerIndex, 0.0, 1.0); // 1秒内将行走权重降为0
}

// 在游戏循环中更新混合器
function update(deltaTime) {
  blender.update(deltaTime);
}
```

### 多层混合

混合多个动画层，如行走、持枪和瞄准：

```typescript
// 添加基础层（行走动画）
const walkLayerIndex = blender.addLayer('walk', 1.0, BlendMode.OVERRIDE);

// 添加持枪层（影响上半身）
const holdGunLayerIndex = blender.addLayer('holdGun', 1.0, BlendMode.ADDITIVE, 1.0, ['upperBody']);

// 添加瞄准层（影响手臂和头部）
const aimLayerIndex = blender.addLayer('aim', 0.0, BlendMode.ADDITIVE, 1.0, ['rightArm', 'leftArm', 'head']);

// 开始瞄准
function startAiming() {
  blender.setLayerWeight(aimLayerIndex, 1.0, 0.3); // 0.3秒内平滑过渡到瞄准姿势
}

// 停止瞄准
function stopAiming() {
  blender.setLayerWeight(aimLayerIndex, 0.0, 0.3); // 0.3秒内平滑过渡回正常姿势
}
```

## 遮罩混合

### 上下半身分离

使用遮罩将上半身和下半身动画分离：

```typescript
// 创建上半身遮罩
const upperBodyMask = AnimationMask.createBoneGroupMask(BoneGroupType.UPPER_BODY);

// 创建下半身遮罩
const lowerBodyMask = AnimationMask.createBoneGroupMask(BoneGroupType.LOWER_BODY);

// 添加下半身层（行走动画）
const walkLayerIndex = blender.addLayer('walk', 1.0, BlendMode.OVERRIDE, 1.0, lowerBodyMask);

// 添加上半身层（挥手动画）
const waveLayerIndex = blender.addLayer('wave', 1.0, BlendMode.OVERRIDE, 1.0, upperBodyMask);

// 在行走时挥手
function walkAndWave() {
  // 两个层都保持权重为1，但由于遮罩，它们只影响各自的身体部位
  blender.setLayerWeight(walkLayerIndex, 1.0);
  blender.setLayerWeight(waveLayerIndex, 1.0);
}
```

### 渐变遮罩

创建平滑过渡的遮罩，如从腰部向上逐渐减弱的影响：

```typescript
// 创建渐变遮罩
const gradientMask = new AnimationMask();

// 设置骨骼权重，从下到上逐渐减小
gradientMask.setBoneWeight('pelvis', 1.0);
gradientMask.setBoneWeight('spine1', 0.8);
gradientMask.setBoneWeight('spine2', 0.6);
gradientMask.setBoneWeight('spine3', 0.4);
gradientMask.setBoneWeight('neck', 0.2);
gradientMask.setBoneWeight('head', 0.0);

// 应用渐变遮罩
const twistLayerIndex = blender.addLayer('twist', 1.0, BlendMode.ADDITIVE, 1.0, gradientMask);
```

## 混合空间

### 1D移动混合空间

基于速度在站立、行走和跑步之间混合：

```typescript
// 创建1D混合空间
const locomotionBlendSpace = new BlendSpace1D({
  minValue: 0,
  maxValue: 5
});

// 添加节点
locomotionBlendSpace.addNode('idle', 0);
locomotionBlendSpace.addNode('walk', 1.5);
locomotionBlendSpace.addNode('run', 5);

// 在游戏循环中更新混合空间
function update(deltaTime) {
  // 获取角色速度
  const speed = character.getSpeed();
  
  // 设置混合空间位置
  locomotionBlendSpace.setPosition(speed);
  
  // 更新混合空间
  locomotionBlendSpace.update();
  
  // 获取活跃节点
  const activeNodes = locomotionBlendSpace.getActiveNodes();
  
  // 更新混合器
  blender.updateFromBlendSpace('locomotion', speed, activeNodes);
  
  // 更新混合器
  blender.update(deltaTime);
}
```

### 2D方向混合空间

基于速度和方向混合八向移动动画：

```typescript
// 创建2D混合空间
const directionalBlendSpace = new BlendSpace2D({
  minX: -1, maxX: 1, // 左右方向
  minY: -1, maxY: 1  // 前后方向
});

// 添加八向移动节点
directionalBlendSpace.addNode('idle', { x: 0, y: 0 });
directionalBlendSpace.addNode('walkForward', { x: 0, y: 1 });
directionalBlendSpace.addNode('walkBackward', { x: 0, y: -1 });
directionalBlendSpace.addNode('walkLeft', { x: -1, y: 0 });
directionalBlendSpace.addNode('walkRight', { x: 1, y: 0 });
directionalBlendSpace.addNode('walkForwardLeft', { x: -0.7, y: 0.7 });
directionalBlendSpace.addNode('walkForwardRight', { x: 0.7, y: 0.7 });
directionalBlendSpace.addNode('walkBackwardLeft', { x: -0.7, y: -0.7 });
directionalBlendSpace.addNode('walkBackwardRight', { x: 0.7, y: -0.7 });

// 在游戏循环中更新混合空间
function update(deltaTime) {
  // 获取输入方向
  const inputX = input.getAxis('Horizontal');
  const inputY = input.getAxis('Vertical');
  
  // 设置混合空间位置
  directionalBlendSpace.setPosition({ x: inputX, y: inputY });
  
  // 更新混合空间
  directionalBlendSpace.update();
  
  // 获取活跃节点
  const activeNodes = directionalBlendSpace.getActiveNodes();
  
  // 更新混合器
  blender.updateFromBlendSpace('directional', { x: inputX, y: inputY }, activeNodes);
  
  // 更新混合器
  blender.update(deltaTime);
}
```

## 子片段混合

### 创建循环子片段

从较长的动画中提取循环部分：

```typescript
// 创建走路循环子片段
const walkLoop = blender.createSubClip(
  'walkLoop',  // 子片段名称
  'walk',      // 源动画名称
  0.2,         // 开始时间（秒）
  0.8,         // 结束时间（秒）
  true         // 是否循环
);

// 创建跑步循环子片段
const runLoop = blender.createSubClip(
  'runLoop',
  'run',
  0.1,
  0.5,
  true
);

// 添加子片段层
const walkLoopLayerIndex = blender.addLayer('walkLoop', 1.0, BlendMode.OVERRIDE);
const runLoopLayerIndex = blender.addLayer('runLoop', 0.0, BlendMode.OVERRIDE);

// 在行走和跑步之间过渡
function transitionToRun() {
  blender.setLayerWeight(walkLoopLayerIndex, 0.0, 0.5);
  blender.setLayerWeight(runLoopLayerIndex, 1.0, 0.5);
}
```

### 高级子片段

创建具有自定义属性的高级子片段：

```typescript
// 创建高级子片段
const jumpSubClip = new AnimationSubClip({
  name: 'jumpAdvanced',
  sourceClipName: 'jump',
  startTime: 0.1,
  endTime: 0.9,
  loop: false,
  timeScale: 1.2,
  blendMode: BlendMode.OVERRIDE,
  events: [
    { time: 0.3, name: 'jumpStart', data: { height: 1.0 } },
    { time: 0.6, name: 'jumpPeak', data: { height: 2.0 } },
    { time: 0.8, name: 'jumpLand', data: { impact: 0.7 } }
  ]
});

// 添加高级子片段
blender.addAdvancedSubClip(jumpSubClip);

// 添加子片段层
const jumpLayerIndex = blender.addLayer('jumpAdvanced', 0.0, BlendMode.OVERRIDE);

// 播放跳跃动画
function jump() {
  blender.setLayerWeight(jumpLayerIndex, 1.0, 0.1);
  
  // 监听事件
  blender.addEventListener(BlendEventType.CLIP_EVENT, (event) => {
    if (event.name === 'jumpPeak') {
      // 在跳跃最高点触发特效
      createJumpEffect(event.data.height);
    }
  });
}
```

## 高级混合模式

### 加法混合

使用加法混合叠加动画效果：

```typescript
// 添加基础层
const idleLayerIndex = blender.addLayer('idle', 1.0, BlendMode.OVERRIDE);

// 添加叠加层（使用加法混合模式）
const breathLayerIndex = blender.addLayer('breathing', 0.3, BlendMode.ADDITIVE);
const lookAroundLayerIndex = blender.addLayer('lookAround', 0.5, BlendMode.ADDITIVE);
const twitchLayerIndex = blender.addLayer('twitch', 0.2, BlendMode.ADDITIVE);

// 随机调整叠加动画权重，创建生动的角色
function updateIdleAnimations() {
  // 呼吸动画保持稳定
  blender.setLayerWeight(breathLayerIndex, 0.3);
  
  // 随机调整环顾动画
  const lookWeight = Math.random() * 0.5;
  blender.setLayerWeight(lookAroundLayerIndex, lookWeight, 1.0);
  
  // 偶尔添加抽搐动画
  if (Math.random() < 0.1) {
    blender.setLayerWeight(twitchLayerIndex, 0.2, 0.2);
    setTimeout(() => {
      blender.setLayerWeight(twitchLayerIndex, 0.0, 0.2);
    }, 300);
  }
}
```

### 乘法混合

使用乘法混合修改现有动画：

```typescript
// 添加基础层
const walkLayerIndex = blender.addLayer('walk', 1.0, BlendMode.OVERRIDE);

// 添加修改层（使用乘法混合模式）
const limbLayerIndex = blender.addLayer('limping', 0.0, BlendMode.MULTIPLY);

// 开始跛行
function startLimping() {
  blender.setLayerWeight(limbLayerIndex, 1.0, 0.5);
}

// 停止跛行
function stopLimping() {
  blender.setLayerWeight(limbLayerIndex, 0.0, 1.0);
}
```

## 状态机集成

将动画混合器与状态机集成：

```typescript
// 创建动画状态机
const stateMachine = new AnimationStateMachine(animator);

// 添加状态
stateMachine.addState({
  name: 'Idle',
  type: 'SingleAnimationState',
  clipName: 'idle',
  loop: true
});

stateMachine.addState({
  name: 'Walk',
  type: 'SingleAnimationState',
  clipName: 'walk',
  loop: true
});

stateMachine.addState({
  name: 'Run',
  type: 'SingleAnimationState',
  clipName: 'run',
  loop: true
});

// 添加转换规则
stateMachine.addTransition({
  from: 'Idle',
  to: 'Walk',
  condition: () => input.getAxis('Vertical') > 0.1,
  duration: 0.3
});

stateMachine.addTransition({
  from: 'Walk',
  to: 'Idle',
  condition: () => input.getAxis('Vertical') < 0.1,
  duration: 0.3
});

stateMachine.addTransition({
  from: 'Walk',
  to: 'Run',
  condition: () => input.getAxis('Vertical') > 0.9,
  duration: 0.2
});

stateMachine.addTransition({
  from: 'Run',
  to: 'Walk',
  condition: () => input.getAxis('Vertical') < 0.9,
  duration: 0.2
});

// 设置初始状态
stateMachine.setCurrentState('Idle');

// 在游戏循环中更新状态机
function update(deltaTime) {
  stateMachine.update(deltaTime);
}
```

## 物理集成

将动画混合器与物理系统集成：

```typescript
// 创建物理动画集成
const physicsIntegration = new PhysicsAnimationIntegration(blender, physicsWorld);

// 添加物理骨骼
physicsIntegration.addPhysicsBone('head', {
  mass: 5,
  damping: 0.8,
  stiffness: 0.7
});

physicsIntegration.addPhysicsBone('ponytail', {
  mass: 1,
  damping: 0.5,
  stiffness: 0.3
});

// 启用物理模拟
physicsIntegration.enable();

// 在游戏循环中更新物理集成
function update(deltaTime) {
  // 更新混合器
  blender.update(deltaTime);
  
  // 更新物理集成
  physicsIntegration.update(deltaTime);
}

// 添加冲击力
function addImpact(force, position) {
  physicsIntegration.addForce('head', force, position);
}
```

## 完整示例

完整的角色动画系统示例：

```typescript
// 创建世界和实体
const world = new World();
const character = new Entity('player');
world.addEntity(character);

// 加载模型和动画
const modelLoader = new GLTFLoader();
modelLoader.load('character.glb', (gltf) => {
  // 添加模型到角色
  character.add(gltf.scene);
  
  // 创建动画控制器
  const animator = new Animator(character);
  character.addComponent('animator', animator);
  
  // 加载动画片段
  gltf.animations.forEach(clip => {
    animator.addClip(clip.name, clip);
  });
  
  // 创建混合器
  const blender = new AnimationBlender(animator);
  character.addComponent('blender', blender);
  
  // 启用性能优化
  blender.setCacheConfig(true, 1000);
  blender.setObjectPoolConfig(true);
  blender.setBatchProcessingConfig(true);
  
  // 创建混合空间
  setupBlendSpaces(blender, animator);
  
  // 创建状态机
  setupStateMachine(character, animator);
  
  // 创建物理集成
  setupPhysicsIntegration(blender, world.getPhysicsWorld());
  
  // 设置输入处理
  setupInputHandling(character, blender);
  
  // 启动游戏循环
  world.start();
});

// 游戏循环
function update(deltaTime) {
  world.update(deltaTime);
}
```

这些示例展示了IR引擎动画混合系统的各种功能和用法。您可以根据自己的需求组合和调整这些示例，创建复杂而自然的角色动画。
