# 场景编辑

场景编辑是IR引擎编辑器的核心功能之一，本文档将详细介绍如何创建、编辑和管理场景。

## 场景基础

### 什么是场景？

场景是IR引擎中的基本工作单位，包含了3D环境中的所有元素，如模型、灯光、相机、特效等。一个项目可以包含多个场景，每个场景可以独立编辑和管理。

### 场景结构

IR引擎的场景采用层次结构，主要包括以下元素：

- **实体（Entity）**：场景中的基本对象
- **组件（Component）**：附加到实体上的功能模块
- **变换（Transform）**：定义实体在3D空间中的位置、旋转和缩放
- **层级关系**：实体之间的父子关系

## 创建和管理场景

### 创建新场景

1. 点击顶部菜单栏的"文件 > 新建场景"
2. 或在场景面板中点击"+"按钮
3. 在弹出的对话框中，输入场景名称
4. 选择场景模板（空白场景、基础3D场景、室内场景等）
5. 点击"创建"按钮

![创建新场景](../../assets/images/new-scene-dialog.png)

### 打开场景

1. 点击顶部菜单栏的"文件 > 打开场景"
2. 或在场景面板中双击场景名称
3. 如果当前场景有未保存的更改，系统会提示保存

### 保存场景

1. 点击顶部菜单栏的"文件 > 保存场景"或按下Ctrl+S（Windows）/Command+S（Mac）
2. 如果是首次保存，会弹出对话框，输入场景名称
3. 点击"保存"按钮

### 场景设置

1. 在层次面板中选择场景根节点
2. 或点击顶部菜单栏的"编辑 > 场景设置"
3. 在属性面板中可以编辑以下设置：
   - 场景名称
   - 环境设置（天空盒、环境光、雾效等）
   - 物理设置（重力、物理材质等）
   - 渲染设置（阴影、后期处理等）

![场景设置](../../assets/images/scene-settings.png)

## 场景视图操作

### 视图导航

- **旋转视图**：按住鼠标右键并拖动
- **平移视图**：按住鼠标中键（或按住Alt键+鼠标左键）并拖动
- **缩放视图**：滚动鼠标滚轮
- **聚焦对象**：选中对象后按F键

### 视图模式

IR引擎编辑器提供多种视图模式，可以通过视图控制面板切换：

- **透视视图**：默认视图，模拟人眼视角
- **正交视图**：无透视效果，适合精确对齐
- **2D视图**：顶视图，适合2D场景编辑
- **自由视图**：可以自由调整视角
- **相机视图**：通过场景中的相机查看场景

![视图模式](../../assets/images/view-modes.png)

### 视图选项

- **显示网格**：显示/隐藏参考网格
- **显示坐标轴**：显示/隐藏世界坐标轴
- **显示碰撞体**：显示/隐藏物理碰撞体
- **显示边界框**：显示/隐藏对象边界框
- **显示线框**：切换线框渲染模式

## 对象操作

### 创建对象

1. 点击顶部菜单栏的"创建"或右键点击层次面板中的空白区域
2. 从下拉菜单中选择要创建的对象类型：
   - **3D基础对象**：立方体、球体、圆柱体、平面等
   - **灯光**：点光源、方向光、聚光灯、区域光等
   - **相机**：透视相机、正交相机
   - **特效**：粒子系统、光晕、镜头光晕等
   - **UI元素**：画布、按钮、文本、图像等
   - **空对象**：用于组织场景层次结构

![创建对象](../../assets/images/create-object-menu.png)

### 导入对象

1. 点击顶部菜单栏的"文件 > 导入 > 模型"
2. 或将模型文件直接拖放到场景视图或资产面板中
3. 在导入设置对话框中，配置导入选项
4. 点击"导入"按钮

### 选择对象

- **单选**：在场景视图中点击对象，或在层次面板中点击对象名称
- **多选**：按住Ctrl键（Windows）或Command键（Mac）的同时点击多个对象
- **框选**：在场景视图中按住鼠标左键拖动，创建选择框
- **全选**：按下Ctrl+A（Windows）或Command+A（Mac）
- **取消选择**：点击场景中的空白区域，或按下Esc键

### 变换对象

#### 移动对象

1. 选择要移动的对象
2. 按下W键或点击工具栏中的移动工具按钮
3. 拖动坐标轴控制柄移动对象
4. 或在属性面板中直接输入位置坐标

#### 旋转对象

1. 选择要旋转的对象
2. 按下E键或点击工具栏中的旋转工具按钮
3. 拖动旋转控制柄旋转对象
4. 或在属性面板中直接输入旋转角度

#### 缩放对象

1. 选择要缩放的对象
2. 按下R键或点击工具栏中的缩放工具按钮
3. 拖动缩放控制柄缩放对象
4. 或在属性面板中直接输入缩放比例

### 复制对象

- **复制粘贴**：选择对象，按下Ctrl+C复制，然后按Ctrl+V粘贴
- **快速复制**：选择对象，按下Ctrl+D创建副本
- **拖动复制**：按住Alt键的同时拖动对象

### 删除对象

- 选择对象，按下Delete键
- 或右键点击对象，选择"删除"

### 重命名对象

1. 在层次面板中选择对象
2. 按下F2键或右键点击选择"重命名"
3. 输入新名称，按Enter确认

### 锁定和隐藏对象

- **锁定对象**：在层次面板中点击对象旁边的锁定图标，防止意外修改
- **隐藏对象**：在层次面板中点击对象旁边的眼睛图标，在场景中隐藏对象

## 组织场景

### 层次结构

IR引擎使用父子关系组织场景对象：

- **创建父子关系**：在层次面板中，将一个对象拖动到另一个对象上
- **解除父子关系**：将子对象拖出父对象，放置在层次面板的其他位置
- **展开/折叠层次**：点击对象旁边的箭头图标

![层次结构](../../assets/images/hierarchy-panel-example.png)

### 使用空对象

空对象是没有可见几何体的对象，常用于组织场景结构：

1. 创建空对象（"创建 > 空对象"）
2. 将相关对象拖动到空对象下，形成逻辑组
3. 可以对整个组进行变换操作

### 使用预制体

预制体是可重用的对象模板：

1. **创建预制体**：选择对象，右键点击选择"创建预制体"
2. **使用预制体**：从资产面板将预制体拖入场景
3. **更新预制体**：修改预制体实例，右键点击选择"应用到预制体"
4. **断开预制体链接**：右键点击预制体实例，选择"断开预制体链接"

### 使用图层

图层用于组织和管理场景对象：

1. 打开图层面板（"视图 > 图层面板"）
2. 创建新图层（点击"+"按钮）
3. 将对象分配到不同图层（在属性面板中选择图层）
4. 使用图层面板控制整个图层的可见性和锁定状态

![图层面板](../../assets/images/layers-panel.png)

## 场景组件

### 添加组件

1. 选择对象
2. 在属性面板底部，点击"添加组件"按钮
3. 从组件列表中选择要添加的组件类型

### 常用组件

- **网格渲染器**：显示3D模型
- **碰撞体**：添加物理碰撞
- **刚体**：添加物理行为
- **光源**：添加光照效果
- **音频源**：播放声音
- **粒子系统**：创建粒子效果
- **动画控制器**：控制动画

### 编辑组件属性

1. 选择对象
2. 在属性面板中找到要编辑的组件
3. 修改组件的属性值

### 复制组件

1. 选择源对象
2. 右键点击并选择"复制组件"，然后选择要复制的组件
3. 选择目标对象
4. 右键点击并选择"粘贴组件值"

## 场景优化

### 性能监控

1. 打开性能面板（"视图 > 性能面板"）
2. 监控FPS、渲染统计、内存使用等指标
3. 使用性能分析工具找出瓶颈

![性能面板](../../assets/images/performance-panel.png)

### 优化技术

- **层级细节(LOD)**：为远处对象使用简化模型
- **遮挡剔除**：不渲染被遮挡的对象
- **实例化渲染**：高效渲染多个相同对象
- **光照烘焙**：预计算光照，减少实时计算
- **纹理压缩**：减少纹理内存占用
- **合并网格**：减少绘制调用

### 场景分块

对于大型场景，可以使用场景分块技术：

1. 将场景分割为多个子场景
2. 使用场景加载器动态加载/卸载子场景
3. 设置加载触发器（距离、区域等）

## 场景测试和预览

### 预览模式

1. 点击工具栏中的播放按钮或按下Ctrl+P（Windows）/Command+P（Mac）
2. 场景将进入播放模式，您可以看到动画、物理模拟等效果
3. 使用WASD键和鼠标导航场景
4. 再次点击播放按钮或按下Esc键退出预览模式

### 模拟设备预览

1. 点击顶部菜单栏的"预览 > 模拟设备"
2. 选择要模拟的设备（手机、平板、桌面等）
3. 场景视图将调整为所选设备的分辨率和比例

### 构建和运行

1. 点击顶部菜单栏的"文件 > 构建和运行"
2. 选择目标平台（Web、移动端、桌面端等）
3. 配置构建设置
4. 点击"构建"按钮
5. 构建完成后，应用将自动运行

## 下一步

现在您已经了解了场景编辑的基本功能，可以继续学习其他相关功能：

- [材质编辑](./material-editing.md)
- [动画系统](./animation-system.md)
- [物理系统](./physics-system.md)
- [交互系统](./interaction-system.md)
