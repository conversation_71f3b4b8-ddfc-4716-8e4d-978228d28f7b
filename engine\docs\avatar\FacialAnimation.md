# 面部动画系统

面部动画系统是IR引擎头像系统的核心组件，用于控制角色的面部表情和口型同步。它支持基于音频的实时口型生成、表情混合和AI驱动的面部动画。

## 主要功能

- **面部表情控制**：精确控制角色的面部表情
- **实时口型同步**：基于音频实时生成口型动画
- **面部动画编辑器**：直观的界面创建和编辑面部动画
- **AI驱动的面部动画合成**：基于文本描述生成面部动画
- **物理驱动的面部动画**：基于物理模拟驱动面部动画
- **GPU加速**：使用GPU加速面部动画计算，提高性能
- **优化的音频分析**：高效的音频分析算法，减少CPU使用率

## 系统架构

面部动画系统由以下几个主要部分组成：

1. **面部动画组件**：控制角色的面部表情和口型
2. **面部动画系统**：管理面部动画组件
3. **面部动画模型适配器**：将面部动画系统与具体的3D模型绑定
4. **口型同步系统**：根据音频实时生成口型动画
5. **AI动画合成系统**：基于文本描述生成面部动画
6. **面部动画编辑器**：编辑和管理面部动画

## 基础用法

### 创建面部动画系统

```typescript
import { 
  FacialAnimationSystem, 
  FacialAnimationModelAdapterSystem, 
  FacialAnimationModelType 
} from '../avatar';

// 创建世界
const world = new World();

// 创建面部动画系统
const facialAnimationSystem = new FacialAnimationSystem(world, {
  debug: true,
  autoDetectAudio: true
});

// 创建面部动画模型适配器系统
const modelAdapterSystem = new FacialAnimationModelAdapterSystem(world, {
  debug: true,
  defaultModelType: FacialAnimationModelType.GLTF
});

// 设置模型适配器系统
facialAnimationSystem.setModelAdapterSystem(modelAdapterSystem);

// 添加系统到世界
world.addSystem(facialAnimationSystem);
world.addSystem(modelAdapterSystem);
```

### 创建面部动画组件

```typescript
import { FacialExpressionType, VisemeType } from '../avatar';

// 创建实体
const characterEntity = world.createEntity();

// 创建面部动画组件
const facialAnimation = facialAnimationSystem.createFacialAnimation(characterEntity);

// 将面部动画组件与模型绑定
facialAnimationSystem.linkToModel(characterEntity, skinnedMesh);

// 设置表情
facialAnimation.setExpression(FacialExpressionType.HAPPY, 1.0, 0.5);

// 设置口型
facialAnimation.setViseme(VisemeType.AA, 1.0, 0.2);
```

## 口型同步

口型同步系统可以根据音频实时生成口型动画。

### 创建口型同步系统

```typescript
import { LipSyncSystem } from '../avatar';

// 创建口型同步系统
const lipSyncSystem = new LipSyncSystem(world, {
  debug: true,
  fftSize: 1024,
  volumeThreshold: 0.01
});

// 添加系统到世界
world.addSystem(lipSyncSystem);

// 创建口型同步组件
const lipSync = lipSyncSystem.createLipSync(characterEntity);
```

### 设置音频源

```typescript
// 创建音频元素
const audioElement = document.createElement('audio');
audioElement.src = 'path/to/audio.mp3';
audioElement.crossOrigin = 'anonymous';

// 设置音频源
lipSync.setAudioSource(audioElement);

// 播放音频
audioElement.play();
```

## 面部动画编辑器

面部动画编辑器用于创建和编辑面部动画。

### 创建面部动画编辑器系统

```typescript
import { FacialAnimationEditorSystem } from '../avatar';

// 创建面部动画编辑器系统
const editorSystem = new FacialAnimationEditorSystem(world, {
  debug: true,
  autoSaveInterval: 60000
});

// 添加系统到世界
world.addSystem(editorSystem);

// 创建编辑器组件
const editor = editorSystem.createEditor(characterEntity);
```

### 创建动画片段

```typescript
// 创建动画片段
const clip = editorSystem.createClip(characterEntity, '表情动画', 5.0);

// 添加表情关键帧
clip.addExpressionKeyframe(0.0, FacialExpressionType.NEUTRAL, 1.0);
clip.addExpressionKeyframe(1.0, FacialExpressionType.HAPPY, 1.0);
clip.addExpressionKeyframe(2.0, FacialExpressionType.SURPRISED, 1.0);
clip.addExpressionKeyframe(3.0, FacialExpressionType.ANGRY, 1.0);
clip.addExpressionKeyframe(4.0, FacialExpressionType.SAD, 1.0);
clip.addExpressionKeyframe(5.0, FacialExpressionType.NEUTRAL, 1.0);

// 添加口型关键帧
clip.addVisemeKeyframe(0.0, VisemeType.SILENT, 0.0);
clip.addVisemeKeyframe(1.0, VisemeType.AA, 1.0);
clip.addVisemeKeyframe(2.0, VisemeType.EE, 1.0);
clip.addVisemeKeyframe(3.0, VisemeType.OU, 1.0);
clip.addVisemeKeyframe(4.0, VisemeType.MM, 1.0);
clip.addVisemeKeyframe(5.0, VisemeType.SILENT, 0.0);
```

### 播放动画

```typescript
// 播放动画
editorSystem.playClip(characterEntity, '表情动画');

// 暂停动画
editorSystem.pauseClip(characterEntity);

// 停止动画
editorSystem.stopClip(characterEntity);
```

## AI动画合成

AI动画合成系统可以基于文本描述生成面部动画。

### 创建AI动画合成系统

```typescript
import { AIAnimationSynthesisSystem } from '../avatar';

// 创建AI动画合成系统
const aiSystem = new AIAnimationSynthesisSystem(world, {
  debug: true,
  useLocalModel: true
});

// 添加系统到世界
world.addSystem(aiSystem);

// 创建AI动画合成组件
const aiComponent = aiSystem.createAIAnimationSynthesis(characterEntity);
```

### 生成面部动画

```typescript
// 生成面部动画
const requestId = aiSystem.generateFacialAnimation(
  characterEntity,
  '开心地说话',
  5.0,
  {
    loop: true,
    style: '卡通',
    intensity: 0.8
  }
);

// 监听生成完成事件
aiSystem.addEventListener('generationComplete', (data) => {
  if (data.result.id === requestId) {
    console.log('AI动画生成完成:', data.result);
    
    // 如果生成成功，播放动画
    if (data.result.success && data.result.clip) {
      // 获取编辑器
      const editor = editorSystem.getEditor(characterEntity);
      if (editor) {
        // 添加生成的片段
        editor.addClip(data.result.clip);
        
        // 设置当前片段并播放
        editor.setCurrentClip(data.result.clip.name);
        editor.play();
      }
    }
  }
});
```

## 高级音频分析

面部动画系统支持多种高级音频分析算法，用于提高口型识别的准确性。

### MFCC分析

MFCC（梅尔频率倒谱系数）是一种常用的音频特征提取方法，可以更准确地识别语音中的音素。

```typescript
// 创建口型同步系统
const lipSyncSystem = new LipSyncSystem(world, {
  debug: true,
  fftSize: 1024,
  volumeThreshold: 0.01,
  useAdvancedAnalyzer: true
});
```

### 音素识别

音素识别可以将语音分解为基本的语音单元，从而更准确地生成口型动画。

```typescript
// 创建口型同步系统
const lipSyncSystem = new LipSyncSystem(world, {
  debug: true,
  fftSize: 1024,
  volumeThreshold: 0.01,
  useAdvancedAnalyzer: true,
  useAIPrediction: true
});
```

## GPU加速

面部动画系统支持使用GPU加速面部动画计算，提高性能。

```typescript
// 创建口型同步系统
const lipSyncSystem = new LipSyncSystem(world, {
  debug: true,
  fftSize: 1024,
  volumeThreshold: 0.01,
  useGPU: true
});
```

## 示例

完整的面部动画示例可以在 `examples/avatar/FacialAnimationExample.ts` 中找到。

## 注意事项

- 面部动画系统需要与具体的3D模型绑定才能正常工作
- 不同的模型可能需要不同的表情和口型映射
- 高级音频分析和AI动画合成可能需要更多的计算资源
- GPU加速需要WebGL支持

## 未来计划

- 支持更多的表情和口型类型
- 改进音频分析算法
- 增强AI动画合成能力
- 优化GPU加速性能
- 添加更多的面部动画编辑器功能
