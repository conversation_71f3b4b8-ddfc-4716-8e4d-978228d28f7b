/**
 * 动画混合性能监控工具
 * 用于收集和分析动画混合系统的性能数据
 */
export interface BlendPerformanceData {
  /** 操作名称 */
  operation: string;
  /** 开始时间 */
  startTime: number;
  /** 结束时间 */
  endTime: number;
  /** 持续时间（毫秒） */
  duration: number;
  /** 混合层数量 */
  layerCount?: number;
  /** 遮罩数量 */
  maskCount?: number;
  /** 混合模式 */
  blendMode?: string;
  /** 是否使用缓存 */
  useCache?: boolean;
  /** 是否使用对象池 */
  useObjectPool?: boolean;
  /** 是否使用批处理 */
  useBatchProcessing?: boolean;
  /** 其他元数据 */
  metadata?: Record<string, any>;
}

/**
 * 性能统计信息
 */
export interface PerformanceStats {
  /** 总操作次数 */
  totalOperations: number;
  /** 总持续时间 */
  totalDuration: number;
  /** 平均持续时间 */
  averageDuration: number;
  /** 最小持续时间 */
  minDuration: number;
  /** 最大持续时间 */
  maxDuration: number;
  /** 按操作类型分组的统计信息 */
  byOperation: Record<string, {
    count: number;
    totalDuration: number;
    averageDuration: number;
    minDuration: number;
    maxDuration: number;
  }>;
  /** 按混合层数量分组的统计信息 */
  byLayerCount?: Record<number, {
    count: number;
    totalDuration: number;
    averageDuration: number;
  }>;
  /** 按混合模式分组的统计信息 */
  byBlendMode?: Record<string, {
    count: number;
    totalDuration: number;
    averageDuration: number;
  }>;
  /** 缓存性能比较 */
  cacheComparison?: {
    withCache: {
      count: number;
      totalDuration: number;
      averageDuration: number;
    };
    withoutCache: {
      count: number;
      totalDuration: number;
      averageDuration: number;
    };
  };
  /** 对象池性能比较 */
  objectPoolComparison?: {
    withObjectPool: {
      count: number;
      totalDuration: number;
      averageDuration: number;
    };
    withoutObjectPool: {
      count: number;
      totalDuration: number;
      averageDuration: number;
    };
  };
  /** 批处理性能比较 */
  batchProcessingComparison?: {
    withBatchProcessing: {
      count: number;
      totalDuration: number;
      averageDuration: number;
    };
    withoutBatchProcessing: {
      count: number;
      totalDuration: number;
      averageDuration: number;
    };
  };
}

/**
 * 动画混合性能监控器
 */
export class BlendPerformanceMonitor {
  /** 性能数据记录 */
  private records: BlendPerformanceData[] = [];
  /** 是否启用 */
  private enabled: boolean = false;
  /** 最大记录数量 */
  private maxRecords: number = 1000;
  /** 单例实例 */
  private static instance: BlendPerformanceMonitor;

  /**
   * 获取单例实例
   */
  public static getInstance(): BlendPerformanceMonitor {
    if (!BlendPerformanceMonitor.instance) {
      BlendPerformanceMonitor.instance = new BlendPerformanceMonitor();
    }
    return BlendPerformanceMonitor.instance;
  }

  /**
   * 启用性能监控
   * @param maxRecords 最大记录数量
   */
  public enable(maxRecords: number = 1000): void {
    this.enabled = true;
    this.maxRecords = maxRecords;
  }

  /**
   * 禁用性能监控
   */
  public disable(): void {
    this.enabled = false;
  }

  /**
   * 清除所有记录
   */
  public clear(): void {
    this.records = [];
  }

  /**
   * 开始记录操作
   * @param operation 操作名称
   * @param metadata 元数据
   * @returns 操作ID
   */
  public startOperation(operation: string, metadata?: Record<string, any>): number {
    if (!this.enabled) return -1;

    const record: BlendPerformanceData = {
      operation,
      startTime: performance.now(),
      endTime: 0,
      duration: 0,
      metadata
    };

    this.records.push(record);
    
    // 限制记录数量
    if (this.records.length > this.maxRecords) {
      this.records.shift();
    }

    return this.records.length - 1;
  }

  /**
   * 结束记录操作
   * @param id 操作ID
   * @param additionalData 附加数据
   */
  public endOperation(id: number, additionalData?: Partial<BlendPerformanceData>): void {
    if (!this.enabled || id < 0 || id >= this.records.length) return;

    const record = this.records[id];
    record.endTime = performance.now();
    record.duration = record.endTime - record.startTime;

    // 添加附加数据
    if (additionalData) {
      Object.assign(record, additionalData);
    }
  }

  /**
   * 获取所有记录
   */
  public getRecords(): BlendPerformanceData[] {
    return [...this.records];
  }

  /**
   * 获取性能统计信息
   */
  public getStats(): PerformanceStats {
    const stats: PerformanceStats = {
      totalOperations: this.records.length,
      totalDuration: 0,
      averageDuration: 0,
      minDuration: Number.MAX_VALUE,
      maxDuration: 0,
      byOperation: {}
    };

    // 如果没有记录，返回空统计
    if (this.records.length === 0) {
      stats.minDuration = 0;
      return stats;
    }

    // 按操作类型分组
    const byOperation: Record<string, BlendPerformanceData[]> = {};
    const byLayerCount: Record<number, BlendPerformanceData[]> = {};
    const byBlendMode: Record<string, BlendPerformanceData[]> = {};
    const withCache: BlendPerformanceData[] = [];
    const withoutCache: BlendPerformanceData[] = [];
    const withObjectPool: BlendPerformanceData[] = [];
    const withoutObjectPool: BlendPerformanceData[] = [];
    const withBatchProcessing: BlendPerformanceData[] = [];
    const withoutBatchProcessing: BlendPerformanceData[] = [];

    // 分组数据
    for (const record of this.records) {
      // 总计
      stats.totalDuration += record.duration;
      stats.minDuration = Math.min(stats.minDuration, record.duration);
      stats.maxDuration = Math.max(stats.maxDuration, record.duration);

      // 按操作分组
      if (!byOperation[record.operation]) {
        byOperation[record.operation] = [];
      }
      byOperation[record.operation].push(record);

      // 按层数分组
      if (record.layerCount !== undefined) {
        if (!byLayerCount[record.layerCount]) {
          byLayerCount[record.layerCount] = [];
        }
        byLayerCount[record.layerCount].push(record);
      }

      // 按混合模式分组
      if (record.blendMode) {
        if (!byBlendMode[record.blendMode]) {
          byBlendMode[record.blendMode] = [];
        }
        byBlendMode[record.blendMode].push(record);
      }

      // 缓存分组
      if (record.useCache !== undefined) {
        if (record.useCache) {
          withCache.push(record);
        } else {
          withoutCache.push(record);
        }
      }

      // 对象池分组
      if (record.useObjectPool !== undefined) {
        if (record.useObjectPool) {
          withObjectPool.push(record);
        } else {
          withoutObjectPool.push(record);
        }
      }

      // 批处理分组
      if (record.useBatchProcessing !== undefined) {
        if (record.useBatchProcessing) {
          withBatchProcessing.push(record);
        } else {
          withoutBatchProcessing.push(record);
        }
      }
    }

    // 计算平均值
    stats.averageDuration = stats.totalDuration / stats.totalOperations;

    // 计算按操作分组的统计
    for (const operation in byOperation) {
      const records = byOperation[operation];
      const totalDuration = records.reduce((sum, record) => sum + record.duration, 0);
      const minDuration = Math.min(...records.map(record => record.duration));
      const maxDuration = Math.max(...records.map(record => record.duration));

      stats.byOperation[operation] = {
        count: records.length,
        totalDuration,
        averageDuration: totalDuration / records.length,
        minDuration,
        maxDuration
      };
    }

    // 计算按层数分组的统计
    if (Object.keys(byLayerCount).length > 0) {
      stats.byLayerCount = {};
      for (const layerCount in byLayerCount) {
        const records = byLayerCount[layerCount];
        const totalDuration = records.reduce((sum, record) => sum + record.duration, 0);

        stats.byLayerCount[layerCount] = {
          count: records.length,
          totalDuration,
          averageDuration: totalDuration / records.length
        };
      }
    }

    // 计算按混合模式分组的统计
    if (Object.keys(byBlendMode).length > 0) {
      stats.byBlendMode = {};
      for (const blendMode in byBlendMode) {
        const records = byBlendMode[blendMode];
        const totalDuration = records.reduce((sum, record) => sum + record.duration, 0);

        stats.byBlendMode[blendMode] = {
          count: records.length,
          totalDuration,
          averageDuration: totalDuration / records.length
        };
      }
    }

    // 计算缓存比较
    if (withCache.length > 0 && withoutCache.length > 0) {
      const withCacheTotalDuration = withCache.reduce((sum, record) => sum + record.duration, 0);
      const withoutCacheTotalDuration = withoutCache.reduce((sum, record) => sum + record.duration, 0);

      stats.cacheComparison = {
        withCache: {
          count: withCache.length,
          totalDuration: withCacheTotalDuration,
          averageDuration: withCacheTotalDuration / withCache.length
        },
        withoutCache: {
          count: withoutCache.length,
          totalDuration: withoutCacheTotalDuration,
          averageDuration: withoutCacheTotalDuration / withoutCache.length
        }
      };
    }

    // 计算对象池比较
    if (withObjectPool.length > 0 && withoutObjectPool.length > 0) {
      const withObjectPoolTotalDuration = withObjectPool.reduce((sum, record) => sum + record.duration, 0);
      const withoutObjectPoolTotalDuration = withoutObjectPool.reduce((sum, record) => sum + record.duration, 0);

      stats.objectPoolComparison = {
        withObjectPool: {
          count: withObjectPool.length,
          totalDuration: withObjectPoolTotalDuration,
          averageDuration: withObjectPoolTotalDuration / withObjectPool.length
        },
        withoutObjectPool: {
          count: withoutObjectPool.length,
          totalDuration: withoutObjectPoolTotalDuration,
          averageDuration: withoutObjectPoolTotalDuration / withoutObjectPool.length
        }
      };
    }

    // 计算批处理比较
    if (withBatchProcessing.length > 0 && withoutBatchProcessing.length > 0) {
      const withBatchProcessingTotalDuration = withBatchProcessing.reduce((sum, record) => sum + record.duration, 0);
      const withoutBatchProcessingTotalDuration = withoutBatchProcessing.reduce((sum, record) => sum + record.duration, 0);

      stats.batchProcessingComparison = {
        withBatchProcessing: {
          count: withBatchProcessing.length,
          totalDuration: withBatchProcessingTotalDuration,
          averageDuration: withBatchProcessingTotalDuration / withBatchProcessing.length
        },
        withoutBatchProcessing: {
          count: withoutBatchProcessing.length,
          totalDuration: withoutBatchProcessingTotalDuration,
          averageDuration: withoutBatchProcessingTotalDuration / withoutBatchProcessing.length
        }
      };
    }

    return stats;
  }

  /**
   * 导出性能数据为JSON
   */
  public exportToJSON(): string {
    return JSON.stringify({
      records: this.records,
      stats: this.getStats()
    }, null, 2);
  }
}
