# 交互系统实现细节

## 概述

交互系统是IR引擎的核心系统之一，用于处理3D场景中的对象交互。本文档详细介绍了交互系统的实现细节，包括高亮效果、世界位置获取、性能优化等方面。

## 核心组件

### InteractionSystem

交互系统的核心类，负责管理和协调所有交互组件。主要功能包括：

- 注册和注销可交互组件
- 收集可用的交互对象
- 处理输入和交互
- 更新高亮效果
- 管理交互事件

### InteractableComponent

标记可交互对象的组件，定义交互行为。主要功能包括：

- 定义交互类型（点击、接近、悬停）
- 处理交互回调
- 管理高亮状态
- 获取世界位置

### InteractionEventComponent

处理交互事件的组件，支持事件监听和分发。主要功能包括：

- 定义交互事件类型
- 添加和移除事件监听器
- 触发交互事件

### InteractionPromptComponent

显示交互提示的组件，支持文本和图标。主要功能包括：

- 定义提示文本和位置
- 控制提示的显示和隐藏
- 更新提示内容

### InteractionHighlightComponent

高亮可交互对象的组件，支持多种高亮效果。主要功能包括：

- 定义高亮类型（轮廓、发光、颜色、自定义）
- 应用和移除高亮效果
- 控制高亮的脉冲效果

## 高亮效果实现

交互系统支持多种高亮效果，每种效果的实现方式如下：

### 轮廓高亮（OUTLINE）

轮廓高亮使用线框材质模拟轮廓效果。在实际项目中，应该使用后处理效果实现更好的轮廓效果，例如使用OutlinePass或自定义着色器。

```typescript
private applyOutlineHighlight(object3D: Object3D): void {
  // 遍历所有网格
  object3D.traverse((child) => {
    if (child instanceof Mesh && child.material) {
      // 保存原始材质
      if (!this.originalMaterials.has(child)) {
        this.originalMaterials.set(child, child.material as MeshBasicMaterial);
      }
      
      // 创建轮廓材质
      const outlineMaterial = new MeshBasicMaterial({
        color: this._highlightColor,
        wireframe: true,
        transparent: true,
        opacity: this._highlightIntensity,
        depthTest: true
      });
      
      // 应用轮廓材质
      child.material = outlineMaterial;
      
      // 保存高亮材质
      this.highlightMaterials.set(child, outlineMaterial);
    }
  });
}
```

### 发光高亮（GLOW）

发光高亮使用自发光材质模拟发光效果。在实际项目中，应该使用后处理效果实现更好的发光效果，例如使用UnrealBloomPass或自定义着色器。

```typescript
private applyGlowHighlight(object3D: Object3D): void {
  // 遍历所有网格
  object3D.traverse((child) => {
    if (child instanceof Mesh && child.material) {
      // 保存原始材质
      if (!this.originalMaterials.has(child)) {
        this.originalMaterials.set(child, child.material as MeshBasicMaterial);
      }
      
      // 创建发光材质
      const glowMaterial = new MeshBasicMaterial({
        color: this._highlightColor,
        transparent: true,
        opacity: this._highlightIntensity,
        wireframe: false,
        depthTest: true,
        emissive: this._highlightColor,
        emissiveIntensity: this._highlightIntensity
      });
      
      // 应用发光材质
      child.material = glowMaterial;
      
      // 保存高亮材质
      this.highlightMaterials.set(child, glowMaterial);
    }
  });
}
```

### 颜色高亮（COLOR）

颜色高亮通过改变对象的颜色实现高亮效果。

```typescript
private applyColorHighlight(object3D: Object3D): void {
  // 遍历所有网格
  object3D.traverse((child) => {
    if (child instanceof Mesh && child.material) {
      // 保存原始材质
      if (!this.originalMaterials.has(child)) {
        this.originalMaterials.set(child, child.material as MeshBasicMaterial);
      }
      
      // 创建高亮材质
      const highlightMaterial = new MeshBasicMaterial({
        color: this._highlightColor,
        transparent: true,
        opacity: this._highlightIntensity,
        wireframe: false,
        depthTest: true
      });
      
      // 应用高亮材质
      child.material = highlightMaterial;
      
      // 保存高亮材质
      this.highlightMaterials.set(child, highlightMaterial);
    }
  });
}
```

### 自定义高亮（CUSTOM）

自定义高亮使用着色器材质实现更复杂的高亮效果，例如边缘发光效果。

```typescript
private applyCustomHighlight(object3D: Object3D): void {
  // 遍历所有网格
  object3D.traverse((child) => {
    if (child instanceof Mesh && child.material) {
      // 保存原始材质
      if (!this.originalMaterials.has(child)) {
        this.originalMaterials.set(child, child.material as MeshBasicMaterial);
      }
      
      // 创建自定义高亮材质
      const customMaterial = new ShaderMaterial({
        uniforms: {
          highlightColor: { value: new Color(this._highlightColor) },
          highlightIntensity: { value: this._highlightIntensity },
          time: { value: 0.0 }
        },
        vertexShader: `
          varying vec2 vUv;
          varying vec3 vNormal;
          
          void main() {
            vUv = uv;
            vNormal = normalize(normalMatrix * normal);
            gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
          }
        `,
        fragmentShader: `
          uniform vec3 highlightColor;
          uniform float highlightIntensity;
          uniform float time;
          
          varying vec2 vUv;
          varying vec3 vNormal;
          
          void main() {
            // 计算边缘发光效果
            float rim = 1.0 - max(dot(vNormal, vec3(0.0, 0.0, 1.0)), 0.0);
            rim = pow(rim, 2.0) * highlightIntensity;
            
            // 添加脉冲效果
            float pulse = 0.5 + 0.5 * sin(time * 2.0);
            
            // 最终颜色
            vec3 finalColor = highlightColor * rim * pulse;
            
            gl_FragColor = vec4(finalColor, rim * pulse);
          }
        `,
        transparent: true,
        depthTest: true
      });
      
      // 应用自定义高亮材质
      child.material = customMaterial;
      
      // 保存高亮材质
      this.highlightMaterials.set(child, customMaterial);
    }
  });
}
```

## 世界位置获取

交互系统需要获取对象的世界位置，以便进行距离计算和交互检测。实现方式如下：

```typescript
getWorldPosition(): Vector3 {
  // 从实体的变换组件中获取世界位置
  const transform = this.entity.getComponent('Transform');
  if (transform) {
    // 使用变换组件的getWorldPosition方法获取世界位置
    this._worldPosition.copy(transform.getWorldPosition());
  }
  return this._worldPosition.clone();
}
```

## 性能优化

为了提高交互系统的性能，特别是在处理大量交互对象时，我们实现了以下优化：

### 空间分区

使用空间分区网格（Spatial Grid）优化交互检测，只检测相机附近的对象。

```typescript
private getNearbyEntities(position: Vector3, radius: number): Entity[] {
  const result: Entity[] = [];
  const gridRadius = Math.ceil(radius / this.gridSize);
  const centerGridX = Math.floor(position.x / this.gridSize);
  const centerGridZ = Math.floor(position.z / this.gridSize);

  // 遍历附近的网格
  for (let dx = -gridRadius; dx <= gridRadius; dx++) {
    for (let dz = -gridRadius; dz <= gridRadius; dz++) {
      const gridX = centerGridX + dx;
      const gridZ = centerGridZ + dz;
      const gridKey = `${gridX},${gridZ}`;

      // 如果网格存在，添加其中的实体
      if (this.spatialGrid.has(gridKey)) {
        const entities = this.spatialGrid.get(gridKey)!;
        for (const entity of entities) {
          // 检查实际距离
          const transform = entity.getComponent('Transform');
          if (transform) {
            const entityPosition = transform.getPosition();
            const distance = entityPosition.distanceTo(position);
            if (distance <= radius) {
              result.push(entity);
            }
          }
        }
      }
    }
  }

  return result;
}
```

### 交互检测优化

优化交互检测逻辑，减少不必要的计算。

```typescript
private optimizedInteractionCheck(cameraPosition: Vector3, maxDistance: number): Entity[] {
  // 使用空间分区获取附近的实体
  const nearbyEntities = this.getNearbyEntities(cameraPosition, maxDistance);
  
  // 过滤出可交互的实体
  const interactableEntities = nearbyEntities.filter(entity => {
    const component = this.getInteractableComponent(entity);
    return component && component.visible && component.interactive;
  });
  
  // 按距离排序
  interactableEntities.sort((a, b) => {
    const componentA = this.getInteractableComponent(a);
    const componentB = this.getInteractableComponent(b);
    
    if (!componentA || !componentB) return 0;
    
    const positionA = componentA.getWorldPosition();
    const positionB = componentB.getWorldPosition();
    
    const distanceA = positionA.distanceTo(cameraPosition);
    const distanceB = positionB.distanceTo(cameraPosition);
    
    return distanceA - distanceB;
  });
  
  return interactableEntities;
}
```

### 更新频率控制

控制交互检测的更新频率，减少CPU负担。

```typescript
update(deltaTime: number): void {
  // 更新交互检测计时器
  this.interactionCheckTimer += deltaTime;
  
  // 每隔一段时间检测一次可交互对象
  if (this.interactionCheckTimer >= this.interactionCheckInterval) {
    this.interactionCheckTimer = 0;
    this.gatherAvailableInteractables();
  }
  
  // 处理输入
  this.handleInput();
  
  // 更新高亮效果
  this.updateHighlight();
}
```

## 示例

交互系统提供了多个示例，展示不同的功能和用法：

- **HighlightEffectsExample**：展示不同类型的高亮效果
- **InteractionTypesExample**：展示不同类型的交互
- **ComplexInteractionExample**：展示复杂交互场景
- **PerformanceOptimizationExample**：展示性能优化技术

## 最佳实践

- 对于大型场景，使用空间分区优化交互检测
- 根据需求选择合适的高亮效果
- 控制交互检测的更新频率
- 使用事件系统处理复杂的交互逻辑
- 为不同类型的交互提供清晰的视觉反馈
