# 图片资源目录

本目录包含IR引擎文档中使用的所有图片资源。

## 目录结构

- `components/` - 组件相关图片
  - `transform-component.png` - 变换组件截图
  - `mesh-renderer-component.png` - 网格渲染器组件截图
  - `skinned-mesh-renderer-component.png` - 蒙皮网格渲染器组件截图
  - `particle-system-component.png` - 粒子系统组件截图
  - `audio-source-component.png` - 音频源组件截图
  - `light-component.png` - 光源组件截图
  - `camera-component.png` - 摄像机组件截图
  - `collider-component.png` - 碰撞器组件截图
  - `rigidbody-component.png` - 刚体组件截图
  - `script-component.png` - 脚本组件截图
  - `ui-component.png` - UI组件截图

- `shaders/` - 着色器相关图片
  - `standard-shader.png` - 标准着色器截图
  - `pbr-shader.png` - PBR着色器截图
  - `unlit-shader.png` - 无光照着色器截图
  - `transparent-shader.png` - 透明着色器截图
  - `skybox-shader.png` - 天空盒着色器截图
  - `post-process-shader.png` - 后处理着色器截图
  - `particle-shader.png` - 粒子着色器截图
  - `ui-shader.png` - UI着色器截图
  - `custom-shader.png` - 自定义着色器截图

- `scripting/` - 脚本相关图片
  - `script-lifecycle.png` - 脚本生命周期图表
  - `script-editor.png` - 脚本编辑器截图
  - `visual-scripting.png` - 视觉脚本编辑器截图
  - `debugging.png` - 调试工具截图
  - `profiling.png` - 性能分析工具截图
  - `code-completion.png` - 代码补全功能截图

- `cli/` - 命令行工具相关图片
  - `cli-tool.png` - 命令行工具截图
  - `cli-help.png` - 命令行帮助信息截图
  - `cli-build.png` - 构建过程截图
  - `cli-asset-management.png` - 资产管理截图
  - `cli-batch-processing.png` - 批处理操作截图
  - `cli-automation.png` - 自动化脚本截图

- `tutorials/` - 教程相关图片
  - `tutorial-editor-basics.png` - 编辑器基础教程截图
  - `tutorial-visual-scripting.png` - 视觉脚本教程截图
  - `tutorial-material-editing.png` - 材质编辑教程截图
  - `tutorial-animation.png` - 动画系统教程截图
  - `tutorial-physics.png` - 物理系统教程截图
  - `tutorial-ui.png` - UI系统教程截图

- `diagrams/` - 图表和示意图
  - `engine-architecture.png` - 引擎架构图
  - `rendering-pipeline.png` - 渲染管线图
  - `physics-system.png` - 物理系统图
  - `animation-system.png` - 动画系统图
  - `input-system.png` - 输入系统图
  - `networking.png` - 网络系统图
  - `asset-workflow.png` - 资产工作流程图

## 图片规范

- 所有截图应当使用1920x1080分辨率
- 所有图表应当使用矢量格式（SVG）或高分辨率PNG（至少300dpi）
- 所有图片应当使用统一的配色方案和样式
- 所有图片应当包含适当的标题和标注
- 所有图片应当针对中文和英文版本分别提供

## 图片命名规范

- 使用小写字母和连字符（-）
- 不使用空格和特殊字符
- 使用有意义的描述性名称
- 对于多语言版本，使用后缀标识语言（例如：`engine-architecture-zh.png`和`engine-architecture-en.png`）

## 图片更新流程

1. 创建或更新图片
2. 将图片放置在适当的子目录中
3. 更新本README文件，添加新图片的描述
4. 在文档中引用图片
