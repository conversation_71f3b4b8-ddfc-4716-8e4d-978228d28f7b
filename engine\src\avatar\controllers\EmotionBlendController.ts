/**
 * 情感混合控制器
 * 用于控制多种情感的混合和过渡
 */
import { Entity } from '../../core/Entity';
import { World } from '../../core/World';
import { FacialAnimationSystem } from '../../animation/FacialAnimation';
import { FacialExpressionType } from '../components/FacialAnimationComponent';
import { EventEmitter } from '../../utils/EventEmitter';

/**
 * 情感表情数据
 */
export interface EmotionExpressionData {
  /** 表情类型 */
  expression: FacialExpressionType;
  /** 目标权重 */
  targetWeight: number;
  /** 当前权重 */
  currentWeight: number;
  /** 过渡速度 */
  transitionSpeed: number;
  /** 开始时间 */
  startTime: number;
  /** 持续时间 */
  duration: number;
  /** 是否活跃 */
  active: boolean;
  /** 优先级 */
  priority: number;
}

/**
 * 情感混合控制器配置
 */
export interface EmotionBlendControllerConfig {
  /** 是否启用调试 */
  debug?: boolean;
  /** 默认过渡时间（秒） */
  defaultTransitionTime?: number;
  /** 是否启用微表情 */
  enableMicroExpressions?: boolean;
  /** 微表情频率（每分钟） */
  microExpressionFrequency?: number;
  /** 微表情强度 */
  microExpressionIntensity?: number;
  /** 微表情持续时间（秒） */
  microExpressionDuration?: number;
  /** 是否启用自然变化 */
  enableNaturalVariation?: boolean;
  /** 自然变化幅度 */
  naturalVariationAmount?: number;
  /** 自然变化频率（每秒） */
  naturalVariationFrequency?: number;
  /** 混合模式 */
  blendMode?: 'add' | 'multiply' | 'override' | 'weighted';
}

/**
 * 情感混合控制器
 */
export class EmotionBlendController {
  /** 实体 */
  private entity: Entity;

  /** 世界 */
  private world: World;

  /** 配置 */
  private config: Required<EmotionBlendControllerConfig>;

  /** 面部动画系统 */
  private facialAnimationSystem: FacialAnimationSystem | null = null;

  /** 表情数据映射 */
  private expressions: Map<FacialExpressionType, EmotionExpressionData> = new Map();

  /** 事件发射器 */
  private eventEmitter: EventEmitter;

  /** 微表情计时器 */
  private microExpressionTimer: number = 0;

  /** 自然变化计时器 */
  private naturalVariationTimer: number = 0;

  /** 自然变化噪声种子 */
  private naturalVariationSeed: number = Math.random() * 1000;

  /**
   * 构造函数
   * @param entity 实体
   * @param world 世界
   * @param config 配置
   */
  constructor(entity: Entity, world: World, config: EmotionBlendControllerConfig = {}) {
    this.entity = entity;
    this.world = world;

    // 设置默认配置
    this.config = {
      debug: config.debug ?? false,
      defaultTransitionTime: config.defaultTransitionTime ?? 0.3,
      enableMicroExpressions: config.enableMicroExpressions ?? true,
      microExpressionFrequency: config.microExpressionFrequency ?? 4.0,
      microExpressionIntensity: config.microExpressionIntensity ?? 0.3,
      microExpressionDuration: config.microExpressionDuration ?? 0.2,
      enableNaturalVariation: config.enableNaturalVariation ?? true,
      naturalVariationAmount: config.naturalVariationAmount ?? 0.1,
      naturalVariationFrequency: config.naturalVariationFrequency ?? 0.5,
      blendMode: config.blendMode ?? 'weighted'
    };

    // 创建事件发射器
    this.eventEmitter = new EventEmitter();

    // 查找面部动画系统
    this.facialAnimationSystem = this.world.getSystem(FacialAnimationSystem.systemName) as FacialAnimationSystem;

    // 初始化表情
    this.initializeExpressions();
  }

  /**
   * 初始化表情
   */
  private initializeExpressions(): void {
    // 添加所有表情类型
    for (const expressionType of Object.values(FacialExpressionType)) {
      this.expressions.set(expressionType as FacialExpressionType, {
        expression: expressionType as FacialExpressionType,
        targetWeight: 0,
        currentWeight: 0,
        transitionSpeed: 1.0 / this.config.defaultTransitionTime,
        startTime: 0,
        duration: 0,
        active: false,
        priority: 0
      });
    }

    // 设置中性表情为活跃
    const neutralExpression = this.expressions.get(FacialExpressionType.NEUTRAL);
    if (neutralExpression) {
      neutralExpression.active = true;
      neutralExpression.targetWeight = 1.0;
      neutralExpression.currentWeight = 1.0;
    }
  }

  /**
   * 更新控制器
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    // 更新表情权重
    this.updateExpressionWeights(deltaTime);

    // 更新微表情
    if (this.config.enableMicroExpressions) {
      this.updateMicroExpressions(deltaTime);
    }

    // 更新自然变化
    if (this.config.enableNaturalVariation) {
      this.updateNaturalVariation(deltaTime);
    }

    // 应用混合表情
    this.applyBlendedExpression();
  }

  /**
   * 更新表情权重
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateExpressionWeights(deltaTime: number): void {
    const currentTime = Date.now() / 1000; // 转换为秒

    // 遍历所有表情
    for (const [expressionType, data] of this.expressions.entries()) {
      // 如果不活跃，则跳过
      if (!data.active) continue;

      // 检查是否超过持续时间
      if (data.duration > 0 && currentTime - data.startTime > data.duration) {
        // 设置目标权重为0
        data.targetWeight = 0;

        // 如果是中性表情，则保持活跃
        if (expressionType === FacialExpressionType.NEUTRAL) {
          data.targetWeight = 1.0;
        } else {
          // 标记为非活跃
          data.active = false;
        }
      }

      // 更新当前权重
      if (data.currentWeight !== data.targetWeight) {
        // 计算权重变化
        const weightChange = data.transitionSpeed * deltaTime;

        // 更新权重
        if (data.currentWeight < data.targetWeight) {
          data.currentWeight = Math.min(data.targetWeight, data.currentWeight + weightChange);
        } else {
          data.currentWeight = Math.max(data.targetWeight, data.currentWeight - weightChange);
        }
      }
    }
  }

  /**
   * 更新微表情
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateMicroExpressions(deltaTime: number): void {
    // 更新计时器
    this.microExpressionTimer += deltaTime;

    // 计算微表情间隔（秒）
    const microExpressionInterval = 60.0 / this.config.microExpressionFrequency;

    // 如果达到间隔，则触发微表情
    if (this.microExpressionTimer >= microExpressionInterval) {
      this.microExpressionTimer = 0;

      // 随机选择一个微表情
      this.triggerRandomMicroExpression();
    }
  }

  /**
   * 触发随机微表情
   */
  private triggerRandomMicroExpression(): void {
    // 获取当前主要表情
    const primaryExpression = this.getPrimaryExpression();

    // 如果没有主要表情，则跳过
    if (!primaryExpression) return;

    // 随机选择一个微表情类型
    const microExpressionTypes = [
      FacialExpressionType.SURPRISED,
      FacialExpressionType.HAPPY,
      FacialExpressionType.SAD,
      FacialExpressionType.FEARFUL,
      FacialExpressionType.DISGUSTED
    ];

    // 排除当前主要表情
    const availableMicroExpressions = microExpressionTypes.filter(type => type !== primaryExpression.expression);

    // 随机选择一个
    const randomIndex = Math.floor(Math.random() * availableMicroExpressions.length);
    const microExpressionType = availableMicroExpressions[randomIndex];

    // 添加微表情
    this.addExpression(
      microExpressionType,
      this.config.microExpressionIntensity,
      this.config.microExpressionDuration,
      0.05 // 快速过渡
    );

    if (this.config.debug) {
      console.log('触发微表情:', microExpressionType);
    }
  }

  /**
   * 更新自然变化
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateNaturalVariation(deltaTime: number): void {
    // 更新计时器
    this.naturalVariationTimer += deltaTime;

    // 计算自然变化间隔（秒）
    const naturalVariationInterval = 1.0 / this.config.naturalVariationFrequency;

    // 如果达到间隔，则应用自然变化
    if (this.naturalVariationTimer >= naturalVariationInterval) {
      this.naturalVariationTimer = 0;

      // 应用自然变化
      this.applyNaturalVariation();
    }
  }

  /**
   * 应用自然变化
   */
  private applyNaturalVariation(): void {
    // 获取当前主要表情
    const primaryExpression = this.getPrimaryExpression();

    // 如果没有主要表情，则跳过
    if (!primaryExpression) return;

    // 生成随机变化
    const variation = (Math.random() * 2 - 1) * this.config.naturalVariationAmount;

    // 应用变化
    primaryExpression.currentWeight = Math.max(0, Math.min(1, primaryExpression.currentWeight + variation));
  }

  /**
   * 应用混合表情
   */
  private applyBlendedExpression(): void {
    // 如果没有面部动画系统，则跳过
    if (!this.facialAnimationSystem) return;

    // 获取面部动画组件
    const facialAnimation = this.facialAnimationSystem.getFacialAnimation(this.entity);

    // 如果没有面部动画组件，则跳过
    if (!facialAnimation) return;

    // 根据混合模式应用表情
    switch (this.config.blendMode) {
      case 'override':
        this.applyOverrideBlend(facialAnimation);
        break;
      case 'add':
        this.applyAdditiveBlend(facialAnimation);
        break;
      case 'multiply':
        this.applyMultiplicativeBlend(facialAnimation);
        break;
      case 'weighted':
      default:
        this.applyWeightedBlend(facialAnimation);
        break;
    }
  }

  /**
   * 应用覆盖混合
   * @param facialAnimation 面部动画组件
   */
  private applyOverrideBlend(facialAnimation: any): void {
    // 获取优先级最高的活跃表情
    const activeExpressions = Array.from(this.expressions.values())
      .filter(data => data.active && data.currentWeight > 0.01)
      .sort((a, b) => b.priority - a.priority);

    // 如果没有活跃表情，则使用中性表情
    if (activeExpressions.length === 0) {
      facialAnimation.setExpression(FacialExpressionType.NEUTRAL, 1.0);
      return;
    }

    // 应用优先级最高的表情
    const primaryExpression = activeExpressions[0];
    facialAnimation.setExpression(primaryExpression.expression, primaryExpression.currentWeight);
  }

  /**
   * 应用加法混合
   * @param facialAnimation 面部动画组件
   */
  private applyAdditiveBlend(facialAnimation: any): void {
    // 获取所有活跃表情
    const activeExpressions = Array.from(this.expressions.values())
      .filter(data => data.active && data.currentWeight > 0.01);

    // 如果没有活跃表情，则使用中性表情
    if (activeExpressions.length === 0) {
      facialAnimation.setExpression(FacialExpressionType.NEUTRAL, 1.0);
      return;
    }

    // 应用所有活跃表情
    for (const expressionData of activeExpressions) {
      facialAnimation.addExpression(expressionData.expression, expressionData.currentWeight);
    }
  }

  /**
   * 应用乘法混合
   * @param facialAnimation 面部动画组件
   */
  private applyMultiplicativeBlend(facialAnimation: any): void {
    // 获取所有活跃表情
    const activeExpressions = Array.from(this.expressions.values())
      .filter(data => data.active && data.currentWeight > 0.01);

    // 如果没有活跃表情，则使用中性表情
    if (activeExpressions.length === 0) {
      facialAnimation.setExpression(FacialExpressionType.NEUTRAL, 1.0);
      return;
    }

    // 计算总权重
    let totalWeight = 1.0;

    // 应用所有活跃表情
    for (const expressionData of activeExpressions) {
      totalWeight *= expressionData.currentWeight;
      facialAnimation.addExpression(expressionData.expression, totalWeight);
    }
  }

  /**
   * 应用加权混合
   * @param facialAnimation 面部动画组件
   */
  private applyWeightedBlend(facialAnimation: any): void {
    // 获取所有活跃表情
    const activeExpressions = Array.from(this.expressions.values())
      .filter(data => data.active && data.currentWeight > 0.01);

    // 如果没有活跃表情，则使用中性表情
    if (activeExpressions.length === 0) {
      facialAnimation.setExpression(FacialExpressionType.NEUTRAL, 1.0);
      return;
    }

    // 计算总权重
    const totalWeight = activeExpressions.reduce((sum, data) => sum + data.currentWeight, 0);

    // 如果总权重为0，则使用中性表情
    if (totalWeight <= 0) {
      facialAnimation.setExpression(FacialExpressionType.NEUTRAL, 1.0);
      return;
    }

    // 应用所有活跃表情
    for (const expressionData of activeExpressions) {
      // 计算归一化权重
      const normalizedWeight = expressionData.currentWeight / totalWeight;

      // 应用表情
      facialAnimation.addExpression(expressionData.expression, normalizedWeight);
    }
  }

  /**
   * 添加表情
   * @param expression 表情类型
   * @param weight 权重
   * @param duration 持续时间（秒）
   * @param transitionTime 过渡时间（秒）
   * @param priority 优先级
   * @returns 是否成功添加
   */
  public addExpression(
    expression: FacialExpressionType,
    weight: number = 1.0,
    duration: number = 0,
    transitionTime: number = this.config.defaultTransitionTime,
    priority: number = 0
  ): boolean {
    // 获取表情数据
    const expressionData = this.expressions.get(expression);

    // 如果没有表情数据，则返回失败
    if (!expressionData) return false;

    // 更新表情数据
    expressionData.targetWeight = Math.max(0, Math.min(1, weight));
    expressionData.transitionSpeed = 1.0 / Math.max(0.001, transitionTime);
    expressionData.startTime = Date.now() / 1000; // 转换为秒
    expressionData.duration = duration;
    expressionData.active = true;
    expressionData.priority = priority;

    // 触发表情添加事件
    this.eventEmitter.emit('expressionAdded', {
      expression,
      weight,
      duration,
      transitionTime,
      priority
    });

    if (this.config.debug) {
      console.log('添加表情:', expression, '权重:', weight, '持续时间:', duration);
    }

    return true;
  }

  /**
   * 移除表情
   * @param expression 表情类型
   * @param transitionTime 过渡时间（秒）
   * @returns 是否成功移除
   */
  public removeExpression(
    expression: FacialExpressionType,
    transitionTime: number = this.config.defaultTransitionTime
  ): boolean {
    // 获取表情数据
    const expressionData = this.expressions.get(expression);

    // 如果没有表情数据，则返回失败
    if (!expressionData) return false;

    // 如果是中性表情，则不移除
    if (expression === FacialExpressionType.NEUTRAL) {
      expressionData.targetWeight = 1.0;
      expressionData.transitionSpeed = 1.0 / Math.max(0.001, transitionTime);
      return true;
    }

    // 更新表情数据
    expressionData.targetWeight = 0;
    expressionData.transitionSpeed = 1.0 / Math.max(0.001, transitionTime);

    // 触发表情移除事件
    this.eventEmitter.emit('expressionRemoved', {
      expression,
      transitionTime
    });

    if (this.config.debug) {
      console.log('移除表情:', expression);
    }

    return true;
  }

  /**
   * 清除所有表情
   * @param transitionTime 过渡时间（秒）
   */
  public clearExpressions(transitionTime: number = this.config.defaultTransitionTime): void {
    // 遍历所有表情
    for (const [expressionType, data] of this.expressions.entries()) {
      // 如果是中性表情，则设置为1.0
      if (expressionType === FacialExpressionType.NEUTRAL) {
        data.targetWeight = 1.0;
        data.transitionSpeed = 1.0 / Math.max(0.001, transitionTime);
        data.active = true;
      } else {
        // 否则设置为0
        data.targetWeight = 0;
        data.transitionSpeed = 1.0 / Math.max(0.001, transitionTime);
        data.active = false;
      }
    }

    // 触发表情清除事件
    this.eventEmitter.emit('expressionsCleared', {
      transitionTime
    });

    if (this.config.debug) {
      console.log('清除所有表情');
    }
  }

  /**
   * 获取表情数据
   * @param expression 表情类型
   * @returns 表情数据
   */
  public getExpressionData(expression: FacialExpressionType): EmotionExpressionData | null {
    return this.expressions.get(expression) || null;
  }

  /**
   * 获取所有活跃表情
   * @returns 活跃表情数组
   */
  public getActiveExpressions(): EmotionExpressionData[] {
    return Array.from(this.expressions.values())
      .filter(data => data.active && data.currentWeight > 0.01);
  }

  /**
   * 获取主要表情
   * @returns 主要表情数据
   */
  public getPrimaryExpression(): EmotionExpressionData | null {
    // 获取所有活跃表情
    const activeExpressions = this.getActiveExpressions();

    // 如果没有活跃表情，则返回null
    if (activeExpressions.length === 0) return null;

    // 按权重排序
    activeExpressions.sort((a, b) => b.currentWeight - a.currentWeight);

    // 返回权重最高的表情
    return activeExpressions[0];
  }

  /**
   * 是否有活跃表情
   * @returns 是否有活跃表情
   */
  public hasActiveExpressions(): boolean {
    // 检查是否有活跃表情
    for (const data of this.expressions.values()) {
      if (data.active && data.currentWeight > 0.01 && data.expression !== FacialExpressionType.NEUTRAL) {
        return true;
      }
    }

    return false;
  }

  /**
   * 添加事件监听器
   * @param event 事件名称
   * @param listener 监听器
   */
  public addEventListener(event: string, listener: Function): void {
    this.eventEmitter.on(event, listener);
  }

  /**
   * 移除事件监听器
   * @param event 事件名称
   * @param listener 监听器
   */
  public removeEventListener(event: string, listener: Function): void {
    this.eventEmitter.off(event, listener);
  }
}
