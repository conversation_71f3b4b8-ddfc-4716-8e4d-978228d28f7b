# IR引擎 API文档

## 概述

IR引擎API是一组RESTful接口，提供对IR引擎平台各种功能的访问。API采用微服务架构，包括用户服务、项目服务、资产服务、渲染服务等多个微服务。

## 基本信息

- **基础URL**: `https://api.ir-engine.example.com`
- **API版本**: v1
- **内容类型**: application/json
- **字符编码**: UTF-8

## 认证

IR引擎API使用JWT（JSON Web Token）进行认证。大多数API端点需要在请求头中包含有效的访问令牌。

### 获取访问令牌

```http
POST /auth/login
Content-Type: application/json

{
  "username": "your_username",
  "password": "your_password"
}
```

**响应**:

```json
{
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "username": "your_username",
    "email": "<EMAIL>"
  }
}
```

### 使用访问令牌

在后续请求中，将访问令牌添加到请求头中：

```http
GET /projects
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## 错误处理

API使用标准HTTP状态码表示请求的结果。常见的状态码包括：

- **200 OK**: 请求成功
- **201 Created**: 资源创建成功
- **400 Bad Request**: 请求参数无效
- **401 Unauthorized**: 未提供认证信息或认证信息无效
- **403 Forbidden**: 没有权限访问请求的资源
- **404 Not Found**: 请求的资源不存在
- **409 Conflict**: 请求冲突（例如，尝试创建已存在的资源）
- **422 Unprocessable Entity**: 请求格式正确，但由于语义错误而无法处理
- **429 Too Many Requests**: 请求频率超过限制
- **500 Internal Server Error**: 服务器内部错误

错误响应的格式如下：

```json
{
  "statusCode": 400,
  "message": "错误描述",
  "error": "Bad Request"
}
```

## 用户服务 API

### 注册新用户

```http
POST /users
Content-Type: application/json

{
  "username": "new_user",
  "email": "<EMAIL>",
  "password": "secure_password"
}
```

**响应**:

```json
{
  "id": "550e8400-e29b-41d4-a716-446655440000",
  "username": "new_user",
  "email": "<EMAIL>",
  "createdAt": "2023-01-01T00:00:00Z",
  "updatedAt": "2023-01-01T00:00:00Z"
}
```

### 获取当前用户信息

```http
GET /users/me
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**响应**:

```json
{
  "id": "550e8400-e29b-41d4-a716-446655440000",
  "username": "your_username",
  "email": "<EMAIL>",
  "isActive": true,
  "roles": ["user"],
  "createdAt": "2023-01-01T00:00:00Z",
  "updatedAt": "2023-01-01T00:00:00Z"
}
```

### 更新用户信息

```http
PATCH /users/me
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "email": "<EMAIL>"
}
```

**响应**:

```json
{
  "id": "550e8400-e29b-41d4-a716-446655440000",
  "username": "your_username",
  "email": "<EMAIL>",
  "isActive": true,
  "roles": ["user"],
  "createdAt": "2023-01-01T00:00:00Z",
  "updatedAt": "2023-01-02T00:00:00Z"
}
```

## 项目服务 API

### 创建新项目

```http
POST /projects
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "name": "我的新项目",
  "description": "这是一个测试项目"
}
```

**响应**:

```json
{
  "id": "550e8400-e29b-41d4-a716-446655440001",
  "name": "我的新项目",
  "description": "这是一个测试项目",
  "ownerId": "550e8400-e29b-41d4-a716-446655440000",
  "createdAt": "2023-01-01T00:00:00Z",
  "updatedAt": "2023-01-01T00:00:00Z"
}
```

### 获取项目列表

```http
GET /projects
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**响应**:

```json
[
  {
    "id": "550e8400-e29b-41d4-a716-446655440001",
    "name": "我的新项目",
    "description": "这是一个测试项目",
    "ownerId": "550e8400-e29b-41d4-a716-446655440000",
    "createdAt": "2023-01-01T00:00:00Z",
    "updatedAt": "2023-01-01T00:00:00Z"
  },
  {
    "id": "550e8400-e29b-41d4-a716-446655440002",
    "name": "另一个项目",
    "description": "这是另一个项目",
    "ownerId": "550e8400-e29b-41d4-a716-446655440000",
    "createdAt": "2023-01-02T00:00:00Z",
    "updatedAt": "2023-01-02T00:00:00Z"
  }
]
```

### 获取项目详情

```http
GET /projects/{projectId}
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**响应**:

```json
{
  "id": "550e8400-e29b-41d4-a716-446655440001",
  "name": "我的新项目",
  "description": "这是一个测试项目",
  "ownerId": "550e8400-e29b-41d4-a716-446655440000",
  "createdAt": "2023-01-01T00:00:00Z",
  "updatedAt": "2023-01-01T00:00:00Z",
  "scenes": [
    {
      "id": "550e8400-e29b-41d4-a716-446655440003",
      "name": "主场景",
      "createdAt": "2023-01-01T00:00:00Z",
      "updatedAt": "2023-01-01T00:00:00Z"
    }
  ],
  "collaborators": [
    {
      "userId": "550e8400-e29b-41d4-a716-446655440004",
      "username": "collaborator1",
      "permission": "read"
    }
  ]
}
```

### 更新项目

```http
PATCH /projects/{projectId}
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "name": "更新后的项目名称",
  "description": "更新后的项目描述"
}
```

**响应**:

```json
{
  "id": "550e8400-e29b-41d4-a716-446655440001",
  "name": "更新后的项目名称",
  "description": "更新后的项目描述",
  "ownerId": "550e8400-e29b-41d4-a716-446655440000",
  "createdAt": "2023-01-01T00:00:00Z",
  "updatedAt": "2023-01-03T00:00:00Z"
}
```

### 删除项目

```http
DELETE /projects/{projectId}
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**响应**:

```
204 No Content
```

### 共享项目

```http
POST /projects/{projectId}/share
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "username": "collaborator1",
  "permission": "read"
}
```

**响应**:

```json
{
  "projectId": "550e8400-e29b-41d4-a716-446655440001",
  "userId": "550e8400-e29b-41d4-a716-446655440004",
  "username": "collaborator1",
  "permission": "read",
  "createdAt": "2023-01-03T00:00:00Z"
}
```

## 资产服务 API

### 上传资产

```http
POST /assets/upload
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: multipart/form-data

file: [二进制文件数据]
projectId: 550e8400-e29b-41d4-a716-446655440001
type: model
```

**响应**:

```json
{
  "id": "550e8400-e29b-41d4-a716-446655440005",
  "filename": "model.gltf",
  "originalName": "model.gltf",
  "type": "model",
  "size": 12345,
  "url": "https://assets.ir-engine.example.com/550e8400-e29b-41d4-a716-446655440005/model.gltf",
  "thumbnailUrl": "https://assets.ir-engine.example.com/550e8400-e29b-41d4-a716-446655440005/thumbnail.jpg",
  "projectId": "550e8400-e29b-41d4-a716-446655440001",
  "ownerId": "550e8400-e29b-41d4-a716-446655440000",
  "createdAt": "2023-01-03T00:00:00Z",
  "updatedAt": "2023-01-03T00:00:00Z"
}
```

### 获取资产列表

```http
GET /assets?projectId=550e8400-e29b-41d4-a716-446655440001
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**响应**:

```json
[
  {
    "id": "550e8400-e29b-41d4-a716-446655440005",
    "filename": "model.gltf",
    "type": "model",
    "size": 12345,
    "url": "https://assets.ir-engine.example.com/550e8400-e29b-41d4-a716-446655440005/model.gltf",
    "thumbnailUrl": "https://assets.ir-engine.example.com/550e8400-e29b-41d4-a716-446655440005/thumbnail.jpg",
    "projectId": "550e8400-e29b-41d4-a716-446655440001",
    "ownerId": "550e8400-e29b-41d4-a716-446655440000",
    "createdAt": "2023-01-03T00:00:00Z",
    "updatedAt": "2023-01-03T00:00:00Z"
  }
]
```

## 渲染服务 API

### 创建渲染任务

```http
POST /render
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "projectId": "550e8400-e29b-41d4-a716-446655440001",
  "sceneId": "550e8400-e29b-41d4-a716-446655440003",
  "settings": {
    "width": 1920,
    "height": 1080,
    "quality": "high",
    "format": "png"
  }
}
```

**响应**:

```json
{
  "id": "550e8400-e29b-41d4-a716-************",
  "projectId": "550e8400-e29b-41d4-a716-446655440001",
  "sceneId": "550e8400-e29b-41d4-a716-446655440003",
  "userId": "550e8400-e29b-41d4-a716-446655440000",
  "status": "pending",
  "settings": {
    "width": 1920,
    "height": 1080,
    "quality": "high",
    "format": "png"
  },
  "createdAt": "2023-01-03T00:00:00Z",
  "updatedAt": "2023-01-03T00:00:00Z"
}
```

### 获取渲染任务状态

```http
GET /render/{renderId}
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**响应**:

```json
{
  "id": "550e8400-e29b-41d4-a716-************",
  "projectId": "550e8400-e29b-41d4-a716-446655440001",
  "sceneId": "550e8400-e29b-41d4-a716-446655440003",
  "userId": "550e8400-e29b-41d4-a716-446655440000",
  "status": "completed",
  "settings": {
    "width": 1920,
    "height": 1080,
    "quality": "high",
    "format": "png"
  },
  "outputUrl": "https://renders.ir-engine.example.com/550e8400-e29b-41d4-a716-************/output.png",
  "duration": 15.5,
  "createdAt": "2023-01-03T00:00:00Z",
  "updatedAt": "2023-01-03T00:00:30Z",
  "completedAt": "2023-01-03T00:00:30Z"
}
```

## 限制和注意事项

- API请求频率限制为每分钟100个请求
- 上传文件大小限制为100MB
- 访问令牌有效期为24小时
- 所有时间戳使用ISO 8601格式（UTC）
- 所有ID使用UUID格式

## 更多资源

- [API变更日志](./CHANGELOG.md)
- [API状态页面](https://status.ir-engine.example.com)
- [开发者社区](https://community.ir-engine.example.com)
- [API客户端库](./clients/README.md)
