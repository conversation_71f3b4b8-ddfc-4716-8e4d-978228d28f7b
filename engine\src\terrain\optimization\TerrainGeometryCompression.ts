/**
 * 地形几何体压缩
 * 提供地形几何体压缩功能，减少内存使用和提高渲染性能
 */
import * as THREE from 'three';
import { Debug } from '../../utils/Debug';
import { EventEmitter } from '../../utils/EventEmitter';
import { TerrainComponent } from '../components/TerrainComponent';
import { Entity } from '../../core/Entity';

/**
 * 二次误差矩阵
 */
class QuadricMatrix {
  private data: number[];

  constructor() {
    this.data = new Array(16).fill(0);
  }

  /**
   * 设置矩阵元素
   */
  public set(
    m11: number, m12: number, m13: number, m14: number,
    m21: number, m22: number, m23: number, m24: number,
    m31: number, m32: number, m33: number, m34: number,
    m41: number, m42: number, m43: number, m44: number
  ): this {
    const d = this.data;
    d[0] = m11; d[1] = m12; d[2] = m13; d[3] = m14;
    d[4] = m21; d[5] = m22; d[6] = m23; d[7] = m24;
    d[8] = m31; d[9] = m32; d[10] = m33; d[11] = m34;
    d[12] = m41; d[13] = m42; d[14] = m43; d[15] = m44;
    return this;
  }

  /**
   * 复制矩阵
   * @param m 矩阵
   * @returns 此矩阵
   */
  public copy(m: QuadricMatrix): this {
    for (let i = 0; i < 16; i++) {
      this.data[i] = m.data[i];
    }
    return this;
  }

  /**
   * 添加矩阵
   * @param m 矩阵
   * @returns 此矩阵
   */
  public add(m: QuadricMatrix): this {
    for (let i = 0; i < 16; i++) {
      this.data[i] += m.data[i];
    }
    return this;
  }

  /**
   * 评估点的误差
   * @param p 点
   * @returns 误差
   */
  public evaluate(p: THREE.Vector3): number {
    const d = this.data;
    const x = p.x, y = p.y, z = p.z;

    return x * (d[0] * x + d[1] * y + d[2] * z + d[3]) +
           y * (d[4] * x + d[5] * y + d[6] * z + d[7]) +
           z * (d[8] * x + d[9] * y + d[10] * z + d[11]) +
           (d[12] * x + d[13] * y + d[14] * z + d[15]);
  }

  /**
   * 查找最优位置
   * @param target 目标位置
   * @returns 是否成功
   */
  public findOptimalPosition(target: THREE.Vector3): boolean {
    const d = this.data;

    // 创建3x3矩阵和向量
    const a = [d[0], d[1], d[2], d[4], d[5], d[6], d[8], d[9], d[10]];
    const b = [-d[3], -d[7], -d[11]];

    // 求解线性方程组
    const det = this.determinant3x3(a);

    // 如果行列式接近零，则无法求解
    if (Math.abs(det) < 1e-10) {
      return false;
    }

    // 使用克莱默法则求解
    const detX = this.determinant3x3([b[0], a[1], a[2], b[1], a[4], a[5], b[2], a[7], a[8]]);
    const detY = this.determinant3x3([a[0], b[0], a[2], a[3], b[1], a[5], a[6], b[2], a[8]]);
    const detZ = this.determinant3x3([a[0], a[1], b[0], a[3], a[4], b[1], a[6], a[7], b[2]]);

    target.set(detX / det, detY / det, detZ / det);

    return true;
  }

  /**
   * 计算3x3矩阵的行列式
   * @param m 矩阵元素
   * @returns 行列式
   */
  private determinant3x3(m: number[]): number {
    return m[0] * (m[4] * m[8] - m[5] * m[7]) -
           m[1] * (m[3] * m[8] - m[5] * m[6]) +
           m[2] * (m[3] * m[7] - m[4] * m[6]);
  }
}

/**
 * 优先队列
 */
class PriorityQueue<T> {
  private elements: T[];
  private comparator: (a: T, b: T) => number;

  constructor(comparator: (a: T, b: T) => number) {
    this.elements = [];
    this.comparator = comparator;
  }

  public enqueue(element: T): void {
    this.elements.push(element);
    this.bubbleUp(this.elements.length - 1);
  }

  public dequeue(): T {
    const result = this.elements[0];
    const end = this.elements.pop()!;

    if (this.elements.length > 0) {
      this.elements[0] = end;
      this.sinkDown(0);
    }

    return result;
  }

  public isEmpty(): boolean {
    return this.elements.length === 0;
  }

  private bubbleUp(index: number): void {
    const element = this.elements[index];

    while (index > 0) {
      const parentIndex = Math.floor((index - 1) / 2);
      const parent = this.elements[parentIndex];

      if (this.comparator(element, parent) >= 0) {
        break;
      }

      this.elements[parentIndex] = element;
      this.elements[index] = parent;
      index = parentIndex;
    }
  }

  private sinkDown(index: number): void {
    const length = this.elements.length;
    const element = this.elements[index];

    while (true) {
      const leftChildIndex = 2 * index + 1;
      const rightChildIndex = 2 * index + 2;
      let smallestChildIndex = -1;

      if (leftChildIndex < length) {
        if (this.comparator(this.elements[leftChildIndex], element) < 0) {
          smallestChildIndex = leftChildIndex;
        }
      }

      if (rightChildIndex < length) {
        if (smallestChildIndex === -1) {
          if (this.comparator(this.elements[rightChildIndex], element) < 0) {
            smallestChildIndex = rightChildIndex;
          }
        } else if (this.comparator(this.elements[rightChildIndex], this.elements[leftChildIndex]) < 0) {
          smallestChildIndex = rightChildIndex;
        }
      }

      if (smallestChildIndex === -1) {
        break;
      }

      this.elements[index] = this.elements[smallestChildIndex];
      this.elements[smallestChildIndex] = element;
      index = smallestChildIndex;
    }
  }
}

/**
 * 几何体压缩事件类型
 */
export enum GeometryCompressionEventType {
  /** 压缩开始 */
  COMPRESSION_STARTED = 'compression_started',
  /** 压缩完成 */
  COMPRESSION_COMPLETED = 'compression_completed',
  /** 压缩进度 */
  COMPRESSION_PROGRESS = 'compression_progress',
  /** 压缩错误 */
  COMPRESSION_ERROR = 'compression_error'
}

/**
 * 几何体压缩配置
 */
export interface GeometryCompressionConfig {
  /** 是否启用 */
  enabled?: boolean;
  /** 是否使用量化 */
  useQuantization?: boolean;
  /** 位置量化精度 */
  positionQuantizationBits?: number;
  /** 法线量化精度 */
  normalQuantizationBits?: number;
  /** UV量化精度 */
  uvQuantizationBits?: number;
  /** 是否使用索引优化 */
  useIndexOptimization?: boolean;
  /** 是否使用顶点缓存优化 */
  useVertexCacheOptimization?: boolean;
  /** 是否使用顶点预取优化 */
  useVertexFetchOptimization?: boolean;
  /** 是否使用网格简化 */
  useMeshSimplification?: boolean;
  /** 简化质量 (0-1) */
  simplificationQuality?: number;
  /** 是否使用GPU加速 */
  useGPUAcceleration?: boolean;
  /** 是否启用调试 */
  debug?: boolean;
}

/**
 * 压缩结果
 */
export interface CompressionResult {
  /** 原始几何体 */
  originalGeometry: THREE.BufferGeometry;
  /** 压缩后的几何体 */
  compressedGeometry: THREE.BufferGeometry;
  /** 原始内存使用量（字节） */
  originalSize: number;
  /** 压缩后内存使用量（字节） */
  compressedSize: number;
  /** 压缩比率 */
  compressionRatio: number;
  /** 应用的优化 */
  appliedOptimizations: string[];
  /** 处理时间（毫秒） */
  processingTime: number;
}

/**
 * 地形几何体压缩类
 */
export class TerrainGeometryCompression {
  /** 是否启用 */
  private enabled: boolean;
  /** 是否使用量化 */
  private useQuantization: boolean;
  /** 位置量化精度 */
  private positionQuantizationBits: number;
  /** 法线量化精度 */
  private normalQuantizationBits: number;
  /** UV量化精度 */
  private uvQuantizationBits: number;
  /** 是否使用索引优化 */
  private useIndexOptimization: boolean;
  /** 是否使用顶点缓存优化 */
  private useVertexCacheOptimization: boolean;
  /** 是否使用顶点预取优化 */
  private useVertexFetchOptimization: boolean;
  /** 是否使用网格简化 */
  private useMeshSimplification: boolean;
  /** 简化质量 */
  private simplificationQuality: number;
  /** 是否使用GPU加速 */
  private useGPUAcceleration: boolean;
  /** 是否启用调试 */
  private debug: boolean;

  /** 事件发射器 */
  private eventEmitter: EventEmitter;
  /** 工作线程 */
  private worker: Worker | null;
  /** 是否支持工作线程 */
  private supportsWorkers: boolean;
  /** 是否支持GPU加速 */
  private supportsGPUAcceleration: boolean;

  /**
   * 创建地形几何体压缩
   * @param config 配置
   */
  constructor(config: GeometryCompressionConfig = {}) {
    // 初始化配置
    this.enabled = config.enabled !== undefined ? config.enabled : true;
    this.useQuantization = config.useQuantization !== undefined ? config.useQuantization : true;
    this.positionQuantizationBits = config.positionQuantizationBits || 16;
    this.normalQuantizationBits = config.normalQuantizationBits || 10;
    this.uvQuantizationBits = config.uvQuantizationBits || 12;
    this.useIndexOptimization = config.useIndexOptimization !== undefined ? config.useIndexOptimization : true;
    this.useVertexCacheOptimization = config.useVertexCacheOptimization !== undefined ? config.useVertexCacheOptimization : true;
    this.useVertexFetchOptimization = config.useVertexFetchOptimization !== undefined ? config.useVertexFetchOptimization : true;
    this.useMeshSimplification = config.useMeshSimplification !== undefined ? config.useMeshSimplification : true;
    this.simplificationQuality = config.simplificationQuality !== undefined ? config.simplificationQuality : 0.8;
    this.useGPUAcceleration = config.useGPUAcceleration !== undefined ? config.useGPUAcceleration : true;
    this.debug = config.debug !== undefined ? config.debug : false;

    // 初始化属性
    this.eventEmitter = new EventEmitter();
    this.worker = null;
    this.supportsWorkers = typeof Worker !== 'undefined';
    this.supportsGPUAcceleration = this.checkGPUAccelerationSupport();

    // 初始化工作线程
    if (this.supportsWorkers) {
      this.initWorker();
    }
  }

  /**
   * 检查GPU加速支持
   * @returns 是否支持GPU加速
   */
  private checkGPUAccelerationSupport(): boolean {
    // 检查WebGL2支持
    try {
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl2');
      return !!gl;
    } catch (error) {
      return false;
    }
  }

  /**
   * 初始化工作线程
   */
  private initWorker(): void {
    try {
      // 创建工作线程
      // 注意：实际实现需要创建一个工作线程脚本
      // this.worker = new Worker('path/to/geometry-compression-worker.js');

      // 注册消息处理器
      // this.worker.onmessage = this.handleWorkerMessage.bind(this);
    } catch (error) {
      Debug.warn('TerrainGeometryCompression', '初始化工作线程失败:', error);
      this.supportsWorkers = false;
    }
  }

  /**
   * 处理工作线程消息
   * @param event 消息事件
   */
  private handleWorkerMessage(event: MessageEvent): void {
    const { type, data } = event.data;

    switch (type) {
      case 'progress':
        this.eventEmitter.emit(GeometryCompressionEventType.COMPRESSION_PROGRESS, data.progress);
        break;
      case 'complete':
        this.eventEmitter.emit(GeometryCompressionEventType.COMPRESSION_COMPLETED, data.result);
        break;
      case 'error':
        this.eventEmitter.emit(GeometryCompressionEventType.COMPRESSION_ERROR, data.error);
        break;
    }
  }

  /**
   * 压缩几何体
   * @param geometry 几何体
   * @returns 压缩结果
   */
  public async compressGeometry(geometry: THREE.BufferGeometry): Promise<CompressionResult> {
    if (!this.enabled) {
      return this.createDummyResult(geometry);
    }

    try {
      // 发出压缩开始事件
      this.eventEmitter.emit(GeometryCompressionEventType.COMPRESSION_STARTED, geometry);

      // 记录开始时间
      const startTime = performance.now();

      // 创建结果对象
      const result: CompressionResult = {
        originalGeometry: geometry,
        compressedGeometry: geometry.clone(),
        originalSize: this.calculateGeometrySize(geometry),
        compressedSize: 0,
        compressionRatio: 1,
        appliedOptimizations: [],
        processingTime: 0
      };

      // 应用优化
      if (this.useQuantization) {
        await this.applyQuantization(result);
        result.appliedOptimizations.push('quantization');
      }

      if (this.useIndexOptimization) {
        await this.applyIndexOptimization(result);
        result.appliedOptimizations.push('index_optimization');
      }

      if (this.useVertexCacheOptimization) {
        await this.applyVertexCacheOptimization(result);
        result.appliedOptimizations.push('vertex_cache_optimization');
      }

      if (this.useVertexFetchOptimization) {
        await this.applyVertexFetchOptimization(result);
        result.appliedOptimizations.push('vertex_fetch_optimization');
      }

      if (this.useMeshSimplification) {
        await this.applyMeshSimplification(result);
        result.appliedOptimizations.push('mesh_simplification');
      }

      // 计算压缩后大小
      result.compressedSize = this.calculateGeometrySize(result.compressedGeometry);
      result.compressionRatio = result.originalSize / result.compressedSize;
      result.processingTime = performance.now() - startTime;

      // 发出压缩完成事件
      this.eventEmitter.emit(GeometryCompressionEventType.COMPRESSION_COMPLETED, result);

      return result;
    } catch (error) {
      // 发出压缩错误事件
      this.eventEmitter.emit(GeometryCompressionEventType.COMPRESSION_ERROR, error);
      Debug.error('TerrainGeometryCompression', '压缩几何体失败:', error);
      return this.createDummyResult(geometry);
    }
  }

  /**
   * 创建虚拟结果
   * @param geometry 几何体
   * @returns 压缩结果
   */
  private createDummyResult(geometry: THREE.BufferGeometry): CompressionResult {
    const size = this.calculateGeometrySize(geometry);
    return {
      originalGeometry: geometry,
      compressedGeometry: geometry.clone(),
      originalSize: size,
      compressedSize: size,
      compressionRatio: 1,
      appliedOptimizations: [],
      processingTime: 0
    };
  }

  /**
   * 计算几何体大小
   * @param geometry 几何体
   * @returns 大小（字节）
   */
  private calculateGeometrySize(geometry: THREE.BufferGeometry): number {
    let size = 0;

    // 计算索引大小
    const index = geometry.index;
    if (index) {
      size += index.array.byteLength;
    }

    // 计算属性大小
    const attributes = geometry.attributes;
    for (const name in attributes) {
      const attribute = attributes[name];
      size += attribute.array.byteLength;
    }

    return size;
  }

  /**
   * 应用量化
   * @param result 压缩结果
   */
  private async applyQuantization(result: CompressionResult): Promise<void> {
    const geometry = result.compressedGeometry;

    // 获取位置属性
    const positionAttribute = geometry.getAttribute('position');
    if (positionAttribute && this.useQuantization) {
      this.quantizePositions(geometry, this.positionQuantizationBits);
    }

    // 获取法线属性
    const normalAttribute = geometry.getAttribute('normal');
    if (normalAttribute && this.useQuantization) {
      this.quantizeNormals(geometry, this.normalQuantizationBits);
    }

    // 获取UV属性
    const uvAttribute = geometry.getAttribute('uv');
    if (uvAttribute && this.useQuantization) {
      this.quantizeUVs(geometry, this.uvQuantizationBits);
    }
  }

  /**
   * 量化位置
   * @param geometry 几何体
   * @param bits 量化位数
   */
  private quantizePositions(geometry: THREE.BufferGeometry, bits: number): void {
    // 获取位置属性
    const positionAttribute = geometry.getAttribute('position');
    if (!positionAttribute) {
      return;
    }

    // 获取位置数据
    const positions = positionAttribute.array;
    const itemSize = positionAttribute.itemSize;
    const count = positionAttribute.count;

    // 计算位置范围
    const min = new THREE.Vector3(Infinity, Infinity, Infinity);
    const max = new THREE.Vector3(-Infinity, -Infinity, -Infinity);

    for (let i = 0; i < count; i++) {
      const x = positions[i * itemSize];
      const y = positions[i * itemSize + 1];
      const z = positions[i * itemSize + 2];

      min.x = Math.min(min.x, x);
      min.y = Math.min(min.y, y);
      min.z = Math.min(min.z, z);

      max.x = Math.max(max.x, x);
      max.y = Math.max(max.y, y);
      max.z = Math.max(max.z, z);
    }

    // 计算量化参数
    const range = new THREE.Vector3().subVectors(max, min);
    const maxValue = Math.pow(2, bits) - 1;

    // 创建量化后的位置数据
    const quantizedPositions = new Uint16Array(count * itemSize);

    // 量化位置
    for (let i = 0; i < count; i++) {
      const x = positions[i * itemSize];
      const y = positions[i * itemSize + 1];
      const z = positions[i * itemSize + 2];

      // 归一化并量化
      const qx = Math.round(((x - min.x) / range.x) * maxValue);
      const qy = Math.round(((y - min.y) / range.y) * maxValue);
      const qz = Math.round(((z - min.z) / range.z) * maxValue);

      // 存储量化值
      quantizedPositions[i * itemSize] = qx;
      quantizedPositions[i * itemSize + 1] = qy;
      quantizedPositions[i * itemSize + 2] = qz;
    }

    // 存储量化参数（用于解量化）
    geometry.userData.positionQuantization = {
      bits,
      min: min.toArray(),
      max: max.toArray(),
      range: range.toArray()
    };

    // 替换原始位置属性
    geometry.setAttribute('positionQuantized', new THREE.BufferAttribute(quantizedPositions, itemSize));

    // 保留原始位置属性（可选）
    // geometry.setAttribute('positionOriginal', positionAttribute.clone());

    Debug.log('TerrainGeometryCompression', `位置量化完成: ${bits}位, 压缩率: ${(positions.byteLength / quantizedPositions.byteLength).toFixed(2)}x`);
  }

  /**
   * 量化法线
   * @param geometry 几何体
   * @param bits 量化位数
   */
  private quantizeNormals(geometry: THREE.BufferGeometry, bits: number): void {
    // 获取法线属性
    const normalAttribute = geometry.getAttribute('normal');
    if (!normalAttribute) {
      return;
    }

    // 获取法线数据
    const normals = normalAttribute.array;
    const itemSize = normalAttribute.itemSize;
    const count = normalAttribute.count;

    // 创建量化后的法线数据
    const quantizedNormals = new Uint16Array(count * itemSize);

    // 计算量化参数
    const maxValue = Math.pow(2, bits) - 1;

    // 量化法线
    for (let i = 0; i < count; i++) {
      const x = normals[i * itemSize];
      const y = normals[i * itemSize + 1];
      const z = normals[i * itemSize + 2];

      // 将法线从 [-1, 1] 映射到 [0, maxValue]
      const qx = Math.round(((x + 1) / 2) * maxValue);
      const qy = Math.round(((y + 1) / 2) * maxValue);
      const qz = Math.round(((z + 1) / 2) * maxValue);

      // 存储量化值
      quantizedNormals[i * itemSize] = qx;
      quantizedNormals[i * itemSize + 1] = qy;
      quantizedNormals[i * itemSize + 2] = qz;
    }

    // 存储量化参数（用于解量化）
    geometry.userData.normalQuantization = {
      bits
    };

    // 替换原始法线属性
    geometry.setAttribute('normalQuantized', new THREE.BufferAttribute(quantizedNormals, itemSize));

    // 保留原始法线属性（可选）
    // geometry.setAttribute('normalOriginal', normalAttribute.clone());

    Debug.log('TerrainGeometryCompression', `法线量化完成: ${bits}位, 压缩率: ${(normals.byteLength / quantizedNormals.byteLength).toFixed(2)}x`);
  }

  /**
   * 量化UV
   * @param geometry 几何体
   * @param bits 量化位数
   */
  private quantizeUVs(geometry: THREE.BufferGeometry, bits: number): void {
    // 获取UV属性
    const uvAttribute = geometry.getAttribute('uv');
    if (!uvAttribute) {
      return;
    }

    // 获取UV数据
    const uvs = uvAttribute.array;
    const itemSize = uvAttribute.itemSize;
    const count = uvAttribute.count;

    // 创建量化后的UV数据
    const quantizedUVs = new Uint16Array(count * itemSize);

    // 计算量化参数
    const maxValue = Math.pow(2, bits) - 1;

    // 量化UV
    for (let i = 0; i < count; i++) {
      const u = uvs[i * itemSize];
      const v = uvs[i * itemSize + 1];

      // 将UV从 [0, 1] 映射到 [0, maxValue]
      const qu = Math.round(u * maxValue);
      const qv = Math.round(v * maxValue);

      // 存储量化值
      quantizedUVs[i * itemSize] = qu;
      quantizedUVs[i * itemSize + 1] = qv;
    }

    // 存储量化参数（用于解量化）
    geometry.userData.uvQuantization = {
      bits
    };

    // 替换原始UV属性
    geometry.setAttribute('uvQuantized', new THREE.BufferAttribute(quantizedUVs, itemSize));

    // 保留原始UV属性（可选）
    // geometry.setAttribute('uvOriginal', uvAttribute.clone());

    Debug.log('TerrainGeometryCompression', `UV量化完成: ${bits}位, 压缩率: ${(uvs.byteLength / quantizedUVs.byteLength).toFixed(2)}x`);
  }

  /**
   * 应用索引优化
   * @param result 压缩结果
   */
  private async applyIndexOptimization(result: CompressionResult): Promise<void> {
    const geometry = result.compressedGeometry;

    // 检查几何体是否有索引
    if (!geometry.index) {
      Debug.warn('TerrainGeometryCompression', '几何体没有索引，跳过索引优化');
      return;
    }

    // 获取索引数据
    const indices = geometry.index.array;
    const indexCount = indices.length;

    // 获取顶点数据
    const positions = geometry.getAttribute('position').array;
    const itemSize = geometry.getAttribute('position').itemSize;
    const vertexCount = geometry.getAttribute('position').count;

    // 创建顶点缓存
    const vertexCache = new Map<string, number>();

    // 创建新的顶点和索引数组
    const newPositions: number[] = [];
    const newIndices: number[] = [];

    // 如果有法线和UV，也需要优化
    const hasNormals = geometry.getAttribute('normal') !== undefined;
    const hasUVs = geometry.getAttribute('uv') !== undefined;

    const normals = hasNormals ? geometry.getAttribute('normal').array : null;
    const uvs = hasUVs ? geometry.getAttribute('uv').array : null;

    const newNormals: number[] = [];
    const newUVs: number[] = [];

    // 遍历所有三角形
    for (let i = 0; i < indexCount; i += 3) {
      for (let j = 0; j < 3; j++) {
        const index = indices[i + j];

        // 获取顶点数据
        const px = positions[index * itemSize];
        const py = positions[index * itemSize + 1];
        const pz = positions[index * itemSize + 2];

        // 创建顶点键
        let key = `${px},${py},${pz}`;

        // 如果有法线，添加到键
        if (hasNormals) {
          const nx = normals![index * itemSize];
          const ny = normals![index * itemSize + 1];
          const nz = normals![index * itemSize + 2];
          key += `,${nx},${ny},${nz}`;
        }

        // 如果有UV，添加到键
        if (hasUVs) {
          const u = uvs![index * 2];
          const v = uvs![index * 2 + 1];
          key += `,${u},${v}`;
        }

        // 检查顶点是否已存在
        if (vertexCache.has(key)) {
          // 使用现有顶点
          newIndices.push(vertexCache.get(key)!);
        } else {
          // 添加新顶点
          const newIndex = newPositions.length / itemSize;

          // 添加位置
          newPositions.push(px, py, pz);

          // 添加法线
          if (hasNormals) {
            newNormals.push(
              normals![index * itemSize],
              normals![index * itemSize + 1],
              normals![index * itemSize + 2]
            );
          }

          // 添加UV
          if (hasUVs) {
            newUVs.push(
              uvs![index * 2],
              uvs![index * 2 + 1]
            );
          }

          // 添加索引
          newIndices.push(newIndex);

          // 缓存顶点
          vertexCache.set(key, newIndex);
        }
      }
    }

    // 创建新的几何体
    const newGeometry = new THREE.BufferGeometry();

    // 设置位置属性
    newGeometry.setAttribute(
      'position',
      new THREE.Float32BufferAttribute(newPositions, itemSize)
    );

    // 设置法线属性
    if (hasNormals) {
      newGeometry.setAttribute(
        'normal',
        new THREE.Float32BufferAttribute(newNormals, itemSize)
      );
    }

    // 设置UV属性
    if (hasUVs) {
      newGeometry.setAttribute(
        'uv',
        new THREE.Float32BufferAttribute(newUVs, 2)
      );
    }

    // 设置索引
    newGeometry.setIndex(newIndices);

    // 复制其他属性
    newGeometry.userData = { ...geometry.userData };

    // 更新结果
    result.compressedGeometry = newGeometry;

    // 计算压缩率
    const originalIndexSize = indexCount * 4; // 假设原始索引是 Uint32Array
    const newIndexSize = newIndices.length * 4;

    Debug.log('TerrainGeometryCompression', `索引优化完成: 顶点数量从 ${vertexCount} 减少到 ${newPositions.length / itemSize}, 索引数量从 ${indexCount} 减少到 ${newIndices.length}`);
    Debug.log('TerrainGeometryCompression', `索引压缩率: ${(originalIndexSize / newIndexSize).toFixed(2)}x`);
  }

  /**
   * 应用顶点缓存优化
   * @param result 压缩结果
   */
  private async applyVertexCacheOptimization(result: CompressionResult): Promise<void> {
    const geometry = result.compressedGeometry;

    // 检查几何体是否有索引
    if (!geometry.index) {
      Debug.warn('TerrainGeometryCompression', '几何体没有索引，跳过顶点缓存优化');
      return;
    }

    // 获取索引数据
    const indices = Array.from(geometry.index.array);
    const indexCount = indices.length;

    // 获取顶点数量
    const vertexCount = geometry.getAttribute('position').count;

    // 实现Tipsify算法（顶点缓存优化）
    const optimizedIndices = this.tipsify(indices, vertexCount, 16); // 假设缓存大小为16

    // 更新索引
    geometry.setIndex(optimizedIndices);

    Debug.log('TerrainGeometryCompression', `顶点缓存优化完成: 索引数量 ${indexCount}`);
  }

  /**
   * Tipsify算法实现（顶点缓存优化）
   * @param indices 索引数组
   * @param vertexCount 顶点数量
   * @param cacheSize 缓存大小
   * @returns 优化后的索引数组
   */
  private tipsify(indices: number[], vertexCount: number, cacheSize: number): number[] {
    // 创建输出索引数组
    const outputIndices = new Array(indices.length);

    // 创建顶点访问时间数组
    const vertexTimeStamp = new Array(vertexCount).fill(-1);

    // 创建每个顶点的相邻三角形列表
    const vertexTriangles: number[][] = new Array(vertexCount);
    for (let i = 0; i < vertexCount; i++) {
      vertexTriangles[i] = [];
    }

    // 填充相邻三角形列表
    const triangleCount = indices.length / 3;
    for (let i = 0; i < triangleCount; i++) {
      const i0 = indices[i * 3];
      const i1 = indices[i * 3 + 1];
      const i2 = indices[i * 3 + 2];

      vertexTriangles[i0].push(i);
      vertexTriangles[i1].push(i);
      vertexTriangles[i2].push(i);
    }

    // 创建已处理三角形标记
    const processedTriangles = new Array(triangleCount).fill(false);

    // 创建死亡时间数组
    const deadEnd = new Array(vertexCount).fill(0);

    // 当前时间戳
    let timeStamp = 0;

    // 当前输出索引位置
    let outputPosition = 0;

    // 选择起始三角形
    let startTriangle = 0;

    // 主循环
    while (outputPosition < indices.length) {
      // 如果需要找新的起始三角形
      if (processedTriangles[startTriangle]) {
        // 查找具有最大死亡时间的顶点
        let maxDeadEnd = -1;
        let maxVertex = -1;

        for (let i = 0; i < vertexCount; i++) {
          if (deadEnd[i] > maxDeadEnd && vertexTriangles[i].some(t => !processedTriangles[t])) {
            maxDeadEnd = deadEnd[i];
            maxVertex = i;
          }
        }

        // 如果找不到，说明所有三角形都已处理
        if (maxVertex === -1) {
          break;
        }

        // 找到该顶点的未处理三角形
        startTriangle = vertexTriangles[maxVertex].find(t => !processedTriangles[t])!;
      }

      // 处理当前三角形
      processedTriangles[startTriangle] = true;

      // 获取三角形顶点
      const i0 = indices[startTriangle * 3];
      const i1 = indices[startTriangle * 3 + 1];
      const i2 = indices[startTriangle * 3 + 2];

      // 添加到输出
      outputIndices[outputPosition++] = i0;
      outputIndices[outputPosition++] = i1;
      outputIndices[outputPosition++] = i2;

      // 更新时间戳
      vertexTimeStamp[i0] = timeStamp++;
      vertexTimeStamp[i1] = timeStamp++;
      vertexTimeStamp[i2] = timeStamp++;

      // 查找下一个三角形
      let nextTriangle = -1;
      let minScore = Number.MAX_VALUE;

      // 检查与当前三角形共享顶点的所有未处理三角形
      const candidateTriangles = new Set([
        ...vertexTriangles[i0],
        ...vertexTriangles[i1],
        ...vertexTriangles[i2]
      ]);

      for (const triangle of candidateTriangles) {
        if (processedTriangles[triangle]) {
          continue;
        }

        // 计算三角形分数
        const tri0 = indices[triangle * 3];
        const tri1 = indices[triangle * 3 + 1];
        const tri2 = indices[triangle * 3 + 2];

        // 计算缓存位置分数
        let score = 0;

        if (vertexTimeStamp[tri0] !== -1) {
          const cachePosition = timeStamp - vertexTimeStamp[tri0];
          if (cachePosition < cacheSize) {
            score += cacheSize - cachePosition;
          }
        }

        if (vertexTimeStamp[tri1] !== -1) {
          const cachePosition = timeStamp - vertexTimeStamp[tri1];
          if (cachePosition < cacheSize) {
            score += cacheSize - cachePosition;
          }
        }

        if (vertexTimeStamp[tri2] !== -1) {
          const cachePosition = timeStamp - vertexTimeStamp[tri2];
          if (cachePosition < cacheSize) {
            score += cacheSize - cachePosition;
          }
        }

        // 选择分数最低的三角形
        if (score < minScore) {
          minScore = score;
          nextTriangle = triangle;
        }
      }

      // 更新死亡时间
      for (let i = 0; i < vertexCount; i++) {
        if (vertexTimeStamp[i] !== -1 && timeStamp - vertexTimeStamp[i] > cacheSize) {
          deadEnd[i] = timeStamp;
        }
      }

      // 设置下一个三角形
      startTriangle = nextTriangle !== -1 ? nextTriangle : 0;
    }

    return outputIndices;
  }

  /**
   * 应用顶点预取优化
   * @param result 压缩结果
   */
  private async applyVertexFetchOptimization(result: CompressionResult): Promise<void> {
    const geometry = result.compressedGeometry;

    // 检查几何体是否有索引
    if (!geometry.index) {
      Debug.warn('TerrainGeometryCompression', '几何体没有索引，跳过顶点预取优化');
      return;
    }

    // 获取索引数据
    const indices = Array.from(geometry.index.array);
    const indexCount = indices.length;

    // 获取顶点数量
    const vertexCount = geometry.getAttribute('position').count;

    // 创建顶点重映射
    const vertexRemap = new Array(vertexCount);
    for (let i = 0; i < vertexCount; i++) {
      vertexRemap[i] = i;
    }

    // 创建顶点访问顺序
    const vertexAccessOrder = new Set<number>();

    // 按索引顺序记录顶点访问
    for (let i = 0; i < indexCount; i++) {
      vertexAccessOrder.add(indices[i]);
    }

    // 转换为数组并分配新索引
    const vertexOrder = Array.from(vertexAccessOrder);
    const vertexIndexMap = new Map<number, number>();

    for (let i = 0; i < vertexOrder.length; i++) {
      vertexIndexMap.set(vertexOrder[i], i);
    }

    // 重新映射索引
    const remappedIndices = indices.map(index => vertexIndexMap.get(index)!);

    // 重新排序顶点数据
    const attributes = geometry.attributes;

    // 处理每个属性
    for (const name in attributes) {
      const attribute = attributes[name];
      const itemSize = attribute.itemSize;
      const array = attribute.array;
      const newArray = new (array.constructor as any)(array.length);

      // 重新排序属性数据
      for (let i = 0; i < vertexOrder.length; i++) {
        const oldIndex = vertexOrder[i];
        const newIndex = i;

        for (let j = 0; j < itemSize; j++) {
          newArray[newIndex * itemSize + j] = array[oldIndex * itemSize + j];
        }
      }

      // 更新属性
      geometry.setAttribute(name, new THREE.BufferAttribute(newArray, itemSize));
    }

    // 更新索引
    geometry.setIndex(remappedIndices);

    Debug.log('TerrainGeometryCompression', `顶点预取优化完成: 顶点数量 ${vertexCount}, 索引数量 ${indexCount}`);
  }

  /**
   * 应用网格简化
   * @param result 压缩结果
   */
  private async applyMeshSimplification(result: CompressionResult): Promise<void> {
    const geometry = result.compressedGeometry;

    // 检查几何体是否有索引
    if (!geometry.index) {
      Debug.warn('TerrainGeometryCompression', '几何体没有索引，跳过网格简化');
      return;
    }

    // 获取索引数据
    const indices = Array.from(geometry.index.array);
    const indexCount = indices.length;

    // 获取顶点数据
    const positions = geometry.getAttribute('position').array;
    const itemSize = geometry.getAttribute('position').itemSize;
    const vertexCount = geometry.getAttribute('position').count;

    // 计算简化目标
    const targetTriangleCount = Math.floor(indexCount / 3 * this.simplificationQuality);

    // 如果目标三角形数量大于等于当前三角形数量，则不需要简化
    if (targetTriangleCount >= indexCount / 3) {
      Debug.log('TerrainGeometryCompression', '不需要网格简化，跳过');
      return;
    }

    // 创建顶点数据结构
    const vertices: { position: THREE.Vector3, quadricError: QuadricMatrix, edges: Set<number> }[] = [];

    // 初始化顶点
    for (let i = 0; i < vertexCount; i++) {
      vertices.push({
        position: new THREE.Vector3(
          positions[i * itemSize],
          positions[i * itemSize + 1],
          positions[i * itemSize + 2]
        ),
        quadricError: new QuadricMatrix(),
        edges: new Set<number>()
      });
    }

    // 创建边数据结构
    const edges: { v1: number, v2: number, cost: number, target: THREE.Vector3, valid: boolean }[] = [];
    const edgeMap = new Map<string, number>();

    // 创建三角形数据结构
    const triangles: { v1: number, v2: number, v3: number, normal: THREE.Vector3, valid: boolean }[] = [];

    // 初始化三角形
    for (let i = 0; i < indexCount; i += 3) {
      const v1 = indices[i];
      const v2 = indices[i + 1];
      const v3 = indices[i + 2];

      // 计算三角形法线
      const p1 = vertices[v1].position;
      const p2 = vertices[v2].position;
      const p3 = vertices[v3].position;

      const normal = new THREE.Vector3()
        .crossVectors(
          new THREE.Vector3().subVectors(p2, p1),
          new THREE.Vector3().subVectors(p3, p1)
        )
        .normalize();

      // 添加三角形
      triangles.push({
        v1,
        v2,
        v3,
        normal,
        valid: true
      });

      // 计算三角形的二次误差矩阵
      const triangleQuadric = this.computeTriangleQuadric(p1, p2, p3, normal);

      // 累加到顶点的二次误差矩阵
      vertices[v1].quadricError.add(triangleQuadric);
      vertices[v2].quadricError.add(triangleQuadric);
      vertices[v3].quadricError.add(triangleQuadric);

      // 添加边
      this.addEdge(v1, v2, vertices, edges, edgeMap);
      this.addEdge(v2, v3, vertices, edges, edgeMap);
      this.addEdge(v3, v1, vertices, edges, edgeMap);
    }

    // 计算所有边的初始代价
    for (let i = 0; i < edges.length; i++) {
      const edge = edges[i];
      const { cost, target } = this.computeEdgeCollapseCost(edge.v1, edge.v2, vertices);
      edge.cost = cost;
      edge.target = target;
    }

    // 创建优先队列
    const priorityQueue = new PriorityQueue<number>((a, b) => edges[a].cost - edges[b].cost);

    // 将所有边添加到优先队列
    for (let i = 0; i < edges.length; i++) {
      priorityQueue.enqueue(i);
    }

    // 当前三角形数量
    let currentTriangleCount = triangles.length;

    // 简化循环
    while (currentTriangleCount > targetTriangleCount && !priorityQueue.isEmpty()) {
      // 获取代价最小的边
      const edgeIndex = priorityQueue.dequeue();
      const edge = edges[edgeIndex];

      // 如果边无效，则跳过
      if (!edge.valid) {
        continue;
      }

      // 执行边折叠
      const v1 = edge.v1;
      const v2 = edge.v2;

      // 更新顶点位置
      vertices[v1].position.copy(edge.target);

      // 合并二次误差矩阵
      vertices[v1].quadricError.add(vertices[v2].quadricError);

      // 更新三角形
      for (let i = 0; i < triangles.length; i++) {
        const triangle = triangles[i];

        // 如果三角形无效，则跳过
        if (!triangle.valid) {
          continue;
        }

        // 如果三角形包含被移除的顶点
        if (triangle.v1 === v2 || triangle.v2 === v2 || triangle.v3 === v2) {
          // 如果三角形包含两个要合并的顶点，则移除三角形
          if ((triangle.v1 === v1 && triangle.v2 === v2) ||
              (triangle.v1 === v1 && triangle.v3 === v2) ||
              (triangle.v2 === v1 && triangle.v1 === v2) ||
              (triangle.v2 === v1 && triangle.v3 === v2) ||
              (triangle.v3 === v1 && triangle.v1 === v2) ||
              (triangle.v3 === v1 && triangle.v2 === v2)) {
            triangle.valid = false;
            currentTriangleCount--;
          } else {
            // 否则，更新三角形顶点
            if (triangle.v1 === v2) triangle.v1 = v1;
            if (triangle.v2 === v2) triangle.v2 = v1;
            if (triangle.v3 === v2) triangle.v3 = v1;

            // 检查三角形是否退化
            if (triangle.v1 === triangle.v2 || triangle.v1 === triangle.v3 || triangle.v2 === triangle.v3) {
              triangle.valid = false;
              currentTriangleCount--;
            } else {
              // 更新三角形法线
              const p1 = vertices[triangle.v1].position;
              const p2 = vertices[triangle.v2].position;
              const p3 = vertices[triangle.v3].position;

              triangle.normal.crossVectors(
                new THREE.Vector3().subVectors(p2, p1),
                new THREE.Vector3().subVectors(p3, p1)
              ).normalize();
            }
          }
        }
      }

      // 标记边为无效
      edge.valid = false;

      // 更新相关边
      for (const otherEdgeIndex of vertices[v2].edges) {
        const otherEdge = edges[otherEdgeIndex];

        // 如果边无效，则跳过
        if (!otherEdge.valid) {
          continue;
        }

        // 更新边的顶点
        if (otherEdge.v1 === v2) otherEdge.v1 = v1;
        if (otherEdge.v2 === v2) otherEdge.v2 = v1;

        // 检查边是否退化
        if (otherEdge.v1 === otherEdge.v2) {
          otherEdge.valid = false;
        } else {
          // 更新边的代价
          const { cost, target } = this.computeEdgeCollapseCost(otherEdge.v1, otherEdge.v2, vertices);
          otherEdge.cost = cost;
          otherEdge.target = target;

          // 将更新后的边重新添加到优先队列
          priorityQueue.enqueue(otherEdgeIndex);

          // 更新顶点的边集合
          vertices[v1].edges.add(otherEdgeIndex);
        }
      }

      // 清除被移除顶点的边集合
      vertices[v2].edges.clear();
    }

    // 创建简化后的几何体
    const newGeometry = new THREE.BufferGeometry();

    // 创建顶点映射
    const vertexMap = new Map<number, number>();
    let nextVertexIndex = 0;

    // 创建新的索引和顶点数组
    const newIndices: number[] = [];
    const newPositions: number[] = [];

    // 如果有法线和UV，也需要简化
    const hasNormals = geometry.getAttribute('normal') !== undefined;
    const hasUVs = geometry.getAttribute('uv') !== undefined;

    const normals = hasNormals ? geometry.getAttribute('normal').array : null;
    const uvs = hasUVs ? geometry.getAttribute('uv').array : null;

    const newNormals: number[] = [];
    const newUVs: number[] = [];

    // 收集有效三角形
    for (let i = 0; i < triangles.length; i++) {
      const triangle = triangles[i];

      // 如果三角形无效，则跳过
      if (!triangle.valid) {
        continue;
      }

      // 处理三角形的顶点
      for (const vertexIndex of [triangle.v1, triangle.v2, triangle.v3]) {
        // 如果顶点尚未映射，则添加到新的顶点数组
        if (!vertexMap.has(vertexIndex)) {
          vertexMap.set(vertexIndex, nextVertexIndex++);

          // 添加位置
          const position = vertices[vertexIndex].position;
          newPositions.push(position.x, position.y, position.z);

          // 添加法线
          if (hasNormals) {
            newNormals.push(
              normals![vertexIndex * itemSize],
              normals![vertexIndex * itemSize + 1],
              normals![vertexIndex * itemSize + 2]
            );
          }

          // 添加UV
          if (hasUVs) {
            newUVs.push(
              uvs![vertexIndex * 2],
              uvs![vertexIndex * 2 + 1]
            );
          }
        }

        // 添加索引
        newIndices.push(vertexMap.get(vertexIndex)!);
      }
    }

    // 设置位置属性
    newGeometry.setAttribute(
      'position',
      new THREE.Float32BufferAttribute(newPositions, itemSize)
    );

    // 设置法线属性
    if (hasNormals) {
      newGeometry.setAttribute(
        'normal',
        new THREE.Float32BufferAttribute(newNormals, itemSize)
      );
    }

    // 设置UV属性
    if (hasUVs) {
      newGeometry.setAttribute(
        'uv',
        new THREE.Float32BufferAttribute(newUVs, 2)
      );
    }

    // 设置索引
    newGeometry.setIndex(newIndices);

    // 复制其他属性
    newGeometry.userData = { ...geometry.userData };

    // 更新结果
    result.compressedGeometry = newGeometry;

    Debug.log('TerrainGeometryCompression', `网格简化完成: 三角形数量从 ${indexCount / 3} 减少到 ${newIndices.length / 3}, 顶点数量从 ${vertexCount} 减少到 ${newPositions.length / itemSize}`);
  }

  /**
   * 添加边
   * @param v1 顶点1
   * @param v2 顶点2
   * @param vertices 顶点数组
   * @param edges 边数组
   * @param edgeMap 边映射
   */
  private addEdge(
    v1: number,
    v2: number,
    vertices: { position: THREE.Vector3, quadricError: QuadricMatrix, edges: Set<number> }[],
    edges: { v1: number, v2: number, cost: number, target: THREE.Vector3, valid: boolean }[],
    edgeMap: Map<string, number>
  ): void {
    // 确保v1 < v2
    if (v1 > v2) {
      [v1, v2] = [v2, v1];
    }

    // 创建边键
    const edgeKey = `${v1}_${v2}`;

    // 检查边是否已存在
    if (edgeMap.has(edgeKey)) {
      return;
    }

    // 添加边
    const edgeIndex = edges.length;
    edges.push({
      v1,
      v2,
      cost: 0,
      target: new THREE.Vector3(),
      valid: true
    });

    // 更新边映射
    edgeMap.set(edgeKey, edgeIndex);

    // 更新顶点的边集合
    vertices[v1].edges.add(edgeIndex);
    vertices[v2].edges.add(edgeIndex);
  }

  /**
   * 计算三角形的二次误差矩阵
   * @param p1 顶点1
   * @param p2 顶点2
   * @param p3 顶点3
   * @param normal 法线
   * @returns 二次误差矩阵
   */
  private computeTriangleQuadric(
    p1: THREE.Vector3,
    p2: THREE.Vector3,
    p3: THREE.Vector3,
    normal: THREE.Vector3
  ): QuadricMatrix {
    // 计算平面方程: ax + by + cz + d = 0
    const a = normal.x;
    const b = normal.y;
    const c = normal.z;
    const d = -normal.dot(p1);

    // 创建二次误差矩阵
    const quadric = new QuadricMatrix();

    // 设置矩阵元素
    quadric.set(
      a * a, a * b, a * c, a * d,
      a * b, b * b, b * c, b * d,
      a * c, b * c, c * c, c * d,
      a * d, b * d, c * d, d * d
    );

    return quadric;
  }

  /**
   * 计算边折叠代价
   * @param v1 顶点1
   * @param v2 顶点2
   * @param vertices 顶点数组
   * @returns 代价和目标位置
   */
  private computeEdgeCollapseCost(
    v1: number,
    v2: number,
    vertices: { position: THREE.Vector3, quadricError: QuadricMatrix, edges: Set<number> }[]
  ): { cost: number, target: THREE.Vector3 } {
    // 合并二次误差矩阵
    const quadric = new QuadricMatrix().copy(vertices[v1].quadricError).add(vertices[v2].quadricError);

    // 尝试求解最优位置
    const target = new THREE.Vector3();
    const success = quadric.findOptimalPosition(target);

    // 如果无法求解，则使用边的中点
    if (!success) {
      target.addVectors(vertices[v1].position, vertices[v2].position).multiplyScalar(0.5);
    }

    // 计算代价
    const cost = quadric.evaluate(target);

    return { cost, target };
  }





  /**
   * 应用到地形组件
   * @param entity 实体
   * @param component 地形组件
   * @returns 压缩结果
   */
  public async applyToTerrainComponent(entity: Entity, component: TerrainComponent): Promise<CompressionResult | null> {
    if (!entity || !component) {
      Debug.warn('TerrainGeometryCompression', '无效的实体或地形组件');
      return null;
    }

    try {
      // 获取地形几何体
      const geometry = component.getGeometry();
      if (!geometry) {
        Debug.warn('TerrainGeometryCompression', '无法获取地形几何体');
        return null;
      }

      // 压缩几何体
      const result = await this.compressGeometry(geometry);

      // 更新地形几何体
      component.setGeometry(result.compressedGeometry);

      return result;
    } catch (error) {
      Debug.error('TerrainGeometryCompression', '应用到地形组件失败:', error);
      return null;
    }
  }

  /**
   * 注册事件监听器
   * @param event 事件类型
   * @param listener 监听器
   */
  public on(event: GeometryCompressionEventType, listener: (...args: any[]) => void): void {
    this.eventEmitter.on(event, listener);
  }

  /**
   * 移除事件监听器
   * @param event 事件类型
   * @param listener 监听器
   */
  public off(event: GeometryCompressionEventType, listener: (...args: any[]) => void): void {
    this.eventEmitter.off(event, listener);
  }
}
